load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],  # Apache 2.0
)

go_library(
    name = "go_default_library",
    srcs = [
        "decls.go",
    ],
    importpath = "github.com/google/cel-go/common/decls",
    deps = [
        "//checker/decls:go_default_library",
        "//common/functions:go_default_library",
        "//common/types:go_default_library",
        "//common/types/ref:go_default_library",
        "//common/types/traits:go_default_library",
        "@org_golang_google_genproto_googleapis_api//expr/v1alpha1:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "decls_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//checker/decls:go_default_library",
        "//common/overloads:go_default_library",
        "//common/types:go_default_library",
        "//common/types/ref:go_default_library",
        "//common/types/traits:go_default_library",
        "@org_golang_google_genproto_googleapis_api//expr/v1alpha1:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)
