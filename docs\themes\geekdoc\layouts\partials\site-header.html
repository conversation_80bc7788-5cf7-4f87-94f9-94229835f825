<header class="gdoc-header">
  <div class="container flex align-center justify-between">
    {{ if .MenuEnabled }}
      <label for="menu-control" class="gdoc-nav__control" tabindex="0">
        <svg class="gdoc-icon gdoc_menu">
          <title>{{ i18n "button_nav_open" }}</title>
          <use xlink:href="#gdoc_menu"></use>
        </svg>
        <svg class="gdoc-icon gdoc_arrow_back">
          <title>{{ i18n "button_nav_close" }}</title>
          <use xlink:href="#gdoc_arrow_back"></use>
        </svg>
      </label>
    {{ end }}
    <div>
      <a class="gdoc-brand gdoc-header__link" href="{{ .Root.Site.BaseURL }}">
        <span class="flex align-center">
          <img
            class="gdoc-brand__img"
            src="{{ (default "brand.svg" .Root.Site.Params.geekdocLogo) | relURL }}"
            alt=""
          />
          <span class="gdoc-brand__title">{{ .Root.Site.Title }}</span>
        </span>
      </a>
    </div>
    <div class="gdoc-menu-header">
      <span class="gdoc-menu-header__items">
        {{ if .Root.Site.Data.menu.extra.header }}
          {{ partial "menu-extra" (dict "current" .Root "source" .Root.Site.Data.menu.extra.header "target" "header") }}
        {{ end }}


        <span id="gdoc-color-theme">
          <svg class="gdoc-icon gdoc_brightness_dark">
            <title>{{ i18n "button_toggle_dark" }}</title>
            <use xlink:href="#gdoc_brightness_dark"></use>
          </svg>
          <svg class="gdoc-icon gdoc_brightness_light">
            <title>{{ i18n "button_toggle_dark" }}</title>
            <use xlink:href="#gdoc_brightness_light"></use>
          </svg>
          <svg class="gdoc-icon gdoc_brightness_auto">
            <title>{{ i18n "button_toggle_dark" }}</title>
            <use xlink:href="#gdoc_brightness_auto"></use>
          </svg>
        </span>

        <span class="gdoc-menu-header__home">
          <a href="{{ .Root.Site.BaseURL }}" class="gdoc-header__link">
            <svg class="gdoc-icon gdoc_home">
              <title>{{ i18n "button_homepage" }}</title>
              <use xlink:href="#gdoc_home"></use>
            </svg>
          </a>
        </span>

        {{ partial "language" .Root }}


        <span class="gdoc-menu-header__control">
          <label for="menu-header-control">
            <svg class="gdoc-icon gdoc_keyboard_arrow_right">
              <use xlink:href="#gdoc_keyboard_arrow_right"></use>
              <title>{{ i18n "button_menu_close" }}</title>
            </svg>
          </label>
        </span>
      </span>
      <label for="menu-header-control" class="gdoc-menu-header__control">
        <svg class="gdoc-icon gdoc_keyboard_arrow_left">
          <use xlink:href="#gdoc_keyboard_arrow_left"></use>
          <title>{{ i18n "button_menu_open" }}</title>
        </svg>
      </label>
    </div>
  </div>
</header>
