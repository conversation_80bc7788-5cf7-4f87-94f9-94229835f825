#!/usr/bin/env bash
set -x

APP_DIR=$1
VERSION=$2

[[ $# < 2 ]] && echo 'not enough args' && exit 1

DATE_COMMAND="date"
if [[ $(uname) == "Darwin" ]]; then
    DATE_COMMAND="docker run --rm alpine date"
fi

GIT_COMMAND="git -C $APP_DIR"
origVersion="$VERSION"
rpmVersion="$VERSION"
rpmRelease=3

if [[ "$rpmVersion" =~ .*-tp[0-9]+$ ]]; then
    tpVersion=${rpmVersion#*-tp}
    rpmVersion=${rpmVersion%-tp*}
    rpmRelease="0.${tpVersion}.tp${tpVersion}"
elif [[ "$rpmVersion" =~ .*-beta[0-9]+$ ]]; then
    betaVersion=${rpmVersion#*-beta}
    rpmVersion=${rpmVersion%-beta*}
    rpmRelease="1.${betaVersion}.beta${betaVersion}"
elif [[ "$rpmVersion" =~ .*-rc[0-9]+$ ]]; then
    rcVersion=${rpmVersion#*-rc}
    rpmVersion=${rpmVersion%-rc*}
    rpmRelease="2.${rcVersion}.rc${rcVersion}"
fi

CRI_DOCKER_GITCOMMIT=$($GIT_COMMAND rev-parse --short HEAD)
if [ -n "$($GIT_COMMAND status --porcelain --untracked-files=no)" ]; then
    CRI_DOCKER_GITCOMMIT="$CRI_DOCKER_GITCOMMIT-unsupported"
fi

# if we have a "-dev" suffix or have change in Git, let's make this package version more complex so it works better
if [[ "$rpmVersion" == *-dev ]] || [ -n "$($GIT_COMMAND status --porcelain)" ]; then
    # based on golang's pseudo-version: https://groups.google.com/forum/#!topic/golang-dev/a5PqQuBljF4
    #
    # using a "pseudo-version" of the form v0.0.0-yyyymmddhhmmss-abcdefabcdef,
    # where the time is the commit time in UTC and the final suffix is the prefix
    # of the commit hash. The time portion ensures that two pseudo-versions can
    # be compared to determine which happened later, the commit hash identifes
    # the underlying commit, and the v0.0.0- prefix identifies the pseudo-version
    # as a pre-release before version v0.0.0, so that the go command prefers any
    # tagged release over any pseudo-version.
    gitUnix="$($GIT_COMMAND log -1 --pretty='%ct')"
    gitDate="$($DATE_COMMAND --utc --date "@$gitUnix" +'%Y%m%d%H%M%S')"
    gitCommit="$($GIT_COMMAND log -1 --pretty='%h')"
    # rpmVersion is now something like '0.0.0-20180719213702-cd5e2db'
    rpmVersion="${VERSION/-dev/}-${gitDate}-${gitCommit}"
    rpmRelease="0"
    origVersion=$rpmVersion
fi

# Replace any other dashes with periods
rpmVersion="${rpmVersion//-/.}"
echo $rpmVersion $rpmRelease $CRI_DOCKER_GITCOMMIT $origVersion
