{{- $size := default "regular" (.Get "size" | lower) }}

{{- if not (in (slice "regular" "large" "small") $size) }}
  {{- $size = "regular" }}
{{- end }}


<div class="gdoc-columns gdoc-columns--{{ $size }} flex flex-gap flex-mobile-column">
  {{- range split .Inner "<--->" }}
    <div class="gdoc-columns__content gdoc-markdown--nested flex-even">
      {{ . | $.Page.RenderString -}}
    </div>
  {{- end }}
</div>
