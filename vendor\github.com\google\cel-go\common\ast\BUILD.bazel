load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(
    default_visibility = [
        "//cel:__subpackages__",
        "//checker:__subpackages__",
        "//common:__subpackages__",
        "//interpreter:__subpackages__",
    ],
    licenses = ["notice"],  # Apache 2.0
)

go_library(
    name = "go_default_library",
    srcs = [
        "ast.go",
        "expr.go",
    ],
    importpath = "github.com/google/cel-go/common/ast",
    deps = [    
        "//common/types:go_default_library",
        "//common/types/ref:go_default_library",
        "@org_golang_google_genproto_googleapis_api//expr/v1alpha1:go_default_library",
        "@org_golang_google_protobuf//types/known/structpb:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "ast_test.go",
        "expr_test.go",
    ],
    embed = [
        ":go_default_library",
    ],
    deps = [    
        "//checker:go_default_library",
        "//checker/decls:go_default_library",
        "//common:go_default_library",
        "//common/containers:go_default_library",
        "//common/decls:go_default_library",
        "//common/overloads:go_default_library",
        "//common/stdlib:go_default_library",
        "//common/types:go_default_library",
        "//common/types/ref:go_default_library",
        "//parser:go_default_library",
        "//test/proto3pb:go_default_library",
        "@org_golang_google_genproto_googleapis_api//expr/v1alpha1:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)