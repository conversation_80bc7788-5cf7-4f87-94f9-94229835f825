load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],  # Apache 2.0
)

go_library(
    name = "go_default_library",
    srcs = [
        "container.go",
    ],
    importpath = "github.com/google/cel-go/common/containers",
    deps = [
        "@org_golang_google_genproto_googleapis_api//expr/v1alpha1:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    size = "small",
    srcs = [
        "container_test.go",
    ],
    embed = [
        ":go_default_library",
    ],
    deps = [
        "@org_golang_google_genproto_googleapis_api//expr/v1alpha1:go_default_library",
    ],
)
