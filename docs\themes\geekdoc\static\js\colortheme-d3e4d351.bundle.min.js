!function(){var t={1860:function(t){var e,n,r,i;e=this,n=this&&this.define,r={version:"2.14.2",areas:{},apis:{},nsdelim:".",inherit:function(t,e){for(var n in t)e.hasOwnProperty(n)||Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n));return e},stringify:function(t,e){return void 0===t||"function"==typeof t?t+"":JSON.stringify(t,e||r.replace)},parse:function(t,e){try{return JSON.parse(t,e||r.revive)}catch(e){return t}},fn:function(t,e){for(var n in r.storeAPI[t]=e,r.apis)r.apis[n][t]=e},get:function(t,e){return t.getItem(e)},set:function(t,e,n){t.setItem(e,n)},remove:function(t,e){t.removeItem(e)},key:function(t,e){return t.key(e)},length:function(t){return t.length},clear:function(t){t.clear()},Store:function(t,e,n){var i=r.inherit(r.storeAPI,(function(t,e,n){return 0===arguments.length?i.getAll():"function"==typeof e?i.transact(t,e,n):void 0!==e?i.set(t,e,n):"string"==typeof t||"number"==typeof t?i.get(t):"function"==typeof t?i.each(t):t?i.setAll(t,e):i.clear()}));i._id=t;try{var s="__store2_test";e.setItem(s,"ok"),i._area=e,e.removeItem(s)}catch(t){i._area=r.storage("fake")}return i._ns=n||"",r.areas[t]||(r.areas[t]=i._area),r.apis[i._ns+i._id]||(r.apis[i._ns+i._id]=i),i},storeAPI:{area:function(t,e){var n=this[t];return n&&n.area||(n=r.Store(t,e,this._ns),this[t]||(this[t]=n)),n},namespace:function(t,e,n){if(n=n||this._delim||r.nsdelim,!t)return this._ns?this._ns.substring(0,this._ns.length-n.length):"";var i=t,s=this[i];if(!(s&&s.namespace||((s=r.Store(this._id,this._area,this._ns+i+n))._delim=n,this[i]||(this[i]=s),e)))for(var o in r.areas)s.area(o,r.areas[o]);return s},isFake:function(t){return t?(this._real=this._area,this._area=r.storage("fake")):!1===t&&(this._area=this._real||this._area),"fake"===this._area.name},toString:function(){return"store"+(this._ns?"."+this.namespace():"")+"["+this._id+"]"},has:function(t){return this._area.has?this._area.has(this._in(t)):!!(this._in(t)in this._area)},size:function(){return this.keys().length},each:function(t,e){for(var n=0,i=r.length(this._area);n<i;n++){var s=this._out(r.key(this._area,n));if(void 0!==s&&!1===t.call(this,s,this.get(s),e))break;i>r.length(this._area)&&(i--,n--)}return e||this},keys:function(t){return this.each((function(t,e,n){n.push(t)}),t||[])},get:function(t,e){var n,i=r.get(this._area,this._in(t));return"function"==typeof e&&(n=e,e=null),null!==i?r.parse(i,n):null!=e?e:i},getAll:function(t){return this.each((function(t,e,n){n[t]=e}),t||{})},transact:function(t,e,n){var r=this.get(t,n),i=e(r);return this.set(t,void 0===i?r:i),this},set:function(t,e,n){var i,s=this.get(t);return null!=s&&!1===n?e:("function"==typeof n&&(i=n,n=void 0),r.set(this._area,this._in(t),r.stringify(e,i),n)||s)},setAll:function(t,e){var n,r;for(var i in t)r=t[i],this.set(i,r,e)!==r&&(n=!0);return n},add:function(t,e,n){var i=this.get(t);if(i instanceof Array)e=i.concat(e);else if(null!==i){var s=typeof i;if(s===typeof e&&"object"===s){for(var o in e)i[o]=e[o];e=i}else e=i+e}return r.set(this._area,this._in(t),r.stringify(e,n)),e},remove:function(t,e){var n=this.get(t,e);return r.remove(this._area,this._in(t)),n},clear:function(){return this._ns?this.each((function(t){r.remove(this._area,this._in(t))}),1):r.clear(this._area),this},clearAll:function(){var t=this._area;for(var e in r.areas)r.areas.hasOwnProperty(e)&&(this._area=r.areas[e],this.clear());return this._area=t,this},_in:function(t){return"string"!=typeof t&&(t=r.stringify(t)),this._ns?this._ns+t:t},_out:function(t){return this._ns?t&&0===t.indexOf(this._ns)?t.substring(this._ns.length):void 0:t}},storage:function(t){return r.inherit(r.storageAPI,{items:{},name:t})},storageAPI:{length:0,has:function(t){return this.items.hasOwnProperty(t)},key:function(t){var e=0;for(var n in this.items)if(this.has(n)&&t===e++)return n},setItem:function(t,e){this.has(t)||this.length++,this.items[t]=e},removeItem:function(t){this.has(t)&&(delete this.items[t],this.length--)},getItem:function(t){return this.has(t)?this.items[t]:null},clear:function(){for(var t in this.items)this.removeItem(t)}}},(i=r.Store("local",function(){try{return localStorage}catch(t){}}())).local=i,i._=r,i.area("session",function(){try{return sessionStorage}catch(t){}}()),i.area("page",r.storage("page")),"function"==typeof n&&void 0!==n.amd?n("store2",[],(function(){return i})):t.exports?t.exports=i:(e.store&&(r.conflict=e.store),e.store=i)},6914:function(t,e,n){"use strict";n.r(e),n.d(e,{COLOR_THEME_AUTO:function(){return s},COLOR_THEME_DARK:function(){return r},COLOR_THEME_LIGHT:function(){return i},THEME:function(){return o},TOGGLE_COLOR_THEMES:function(){return a}});const r="dark",i="light",s="auto",o="hugo-geekdoc",a=[s,r,i]}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var s=e[r]={exports:{}};return t[r].call(s.exports,s,s.exports,n),s.exports}n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},function(){const t=n(1860),{TOGGLE_COLOR_THEMES:e,THEME:r,COLOR_THEME_AUTO:i}=n(6914);function s(n=!0){if(t.isFake())return;let s=t.namespace(r),o=document.documentElement,a=e.includes(s.get("color-theme"))?s.get("color-theme"):i;o.setAttribute("class","color-toggle-"+a),a===i?o.removeAttribute("color-theme"):o.setAttribute("color-theme",a),n||location.reload()}s(),document.addEventListener("DOMContentLoaded",(n=>{document.getElementById("gdoc-color-theme").onclick=function(){let n=t.namespace(r),o=n.get("color-theme")||i,a=function(t=[],e){let n=t.indexOf(e),r=0;return n<t.length-1&&(r=n+1),r}(e,o);n.set("color-theme",e[a]),s(!1)}}))}()}();