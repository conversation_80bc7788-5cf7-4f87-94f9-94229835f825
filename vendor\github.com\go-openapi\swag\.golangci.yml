linters-settings:
  govet:
    check-shadowing: true
  golint:
    min-confidence: 0
  gocyclo:
    min-complexity: 25
  maligned:
    suggest-new: true
  dupl:
    threshold: 100
  goconst:
    min-len: 3
    min-occurrences: 2

linters:
  enable-all: true
  disable:
    - maligned
    - lll
    - gochecknoinits
    - gochecknoglobals
    - nlreturn
    - testpackage
    - wrapcheck
    - gomnd
    - exhaustive
    - exhaustivestruct
    - goerr113
    - wsl
    - whitespace
    - gofumpt
    - godot
    - nestif
    - godox
    - funlen
    - gci
    - gocognit
    - paralleltest
    - thelper
    - ifshort
    - gomoddirectives
    - cyclop
    - forcetypeassert
    - ireturn
    - tagliatelle
    - varnamelen
    - goimports
    - tenv
    - golint
    - exhaustruct
    - nilnil
    - nonamedreturns
    - nosnakecase
