{{ define "main" }}
  {{ range .Paginator.Pages.ByTitle }}
    <article class="gdoc-post">
      <header class="gdoc-post__header">
        <h1 class="gdoc-post__title">
          <a href="{{ .RelPermalink }}">{{ partial "utils/title" . }}</a>
        </h1>
      </header>

      <footer class="gdoc-post__meta flex align-center">
        <span class="flex align-center no-wrap">
          {{ $pageCount := len .Pages }}
          <svg class="gdoc-icon gdoc_tag"><use xlink:href="#gdoc_tag"></use></svg>
          <span class="gdoc-post__tag">
            {{ i18n "posts_count" $pageCount }}
          </span>
        </span>

        <span class="flex align-center no-wrap">
          <svg class="gdoc-icon gdoc_star"><use xlink:href="#gdoc_star"></use></svg>
          <span>
            {{ $latet := index .Pages.ByDate 0 }}
            {{ with $latet }}
              <a href="{{ .RelPermalink }}">{{ partial "utils/title" . }}</a>
            {{ end }}
          </span>
        </span>
      </footer>
    </article>
  {{ end }}
  {{ partial "pagination.html" . }}
{{ end }}
