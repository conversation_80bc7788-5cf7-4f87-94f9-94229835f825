{{- $searchDataFile := printf "search/%s.data.json" .Language.Lang -}}
{{- $searchData := resources.Get "search/data.json" | resources.ExecuteAsTemplate $searchDataFile . | resources.Minify -}}
{
  "dataFile": {{ $searchData.RelPermalink | jsonify }},
  "indexConfig": {{ .Site.Params.geekdocSearchConfig | jsonify }},
  "showParent": {{ if .Site.Params.geekdocSearchShowParent }}true{{ else }}false{{ end }},
  "showDescription": {{ if .Site.Params.geekdocSearchshowDescription }}true{{ else }}false{{ end }}
}
