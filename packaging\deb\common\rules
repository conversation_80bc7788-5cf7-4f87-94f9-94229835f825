#!/usr/bin/make -f

VERSION ?= $(shell cat app/VERSION)

override_dh_gencontrol:
	# if we're on Ubuntu, we need to Recommends: apparmor
	echo 'apparmor:Recommends=$(shell dpkg-vendor --is Ubuntu && echo apparmor)' >> debian/cri-dockerd.substvars
	dh_gencontrol

override_dh_auto_build:
	# Build the daemon and dependencies
	cd app && GOPROXY="https://proxy.golang.org" GO111MODULE=on go build -ldflags "$(shell echo ${LDFLAGS} | sed -e 's/-ldflags //')"
	echo "DONE"

override_dh_auto_test:
	./app/cri-dockerd --version

override_dh_strip:
	# Go has lots of problems with stripping, so just don't

override_dh_auto_install:
	install -D -m 0755 ./app/cri-dockerd debian/cri-dockerd/usr/bin/cri-dockerd
	#install -D -m 0755 /go/src/github.com/Mirantis/cri-dockerd/cri-dockerd debian/cri-docker/usr/bin/cri-dockerd
	install -D -m 0644 /sources/cri-docker.service debian/cri-dockerd/lib/systemd/system/cri-docker.service
	install -D -m 0644 /sources/cri-docker.socket debian/cri-dockerd/lib/systemd/system/cri-docker.socket

override_dh_installinit:
	dh_installinit --name=cri-dockerd

override_dh_shlibdeps:
	dh_shlibdeps --dpkg-shlibdeps-params=--ignore-missing-info

override_dh_install:
	dh_install

%:
	dh $@ --with=bash-completion $(shell command -v dh_systemd_enable > /dev/null 2>&1 && echo --with=systemd)
