{{- if .Inner }}{{ end }}
{{- $id := .Get 0 }}
{{- $group := printf "tabs-%s" $id }}


<div class="gdoc-tabs">
  {{- range $index, $tab := .Scratch.Get $group }}
    <input
      type="radio"
      class="gdoc-tabs__control hidden"
      name="{{ $group }}"
      id="{{ printf "%s-%d" $group $index }}"
      {{ if not $index }}checked="checked"{{ end }}
    />
    <label for="{{ printf "%s-%d" $group $index }}" class="gdoc-tabs__label">
      {{ $tab.Name }}
    </label>
    <div class="gdoc-markdown--nested gdoc-tabs__content">
      {{ .Content | $.Page.RenderString }}
    </div>
  {{- end }}
</div>
