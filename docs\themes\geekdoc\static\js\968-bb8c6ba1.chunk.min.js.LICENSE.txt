/*!
  Embeddable Minimum Strictly-Compliant Promises/A+ 1.1.1 Thenable
  Copyright (c) 2013-2014 <PERSON><PERSON> (http://engelschall.com)
  Licensed under The MIT License (http://opensource.org/licenses/MIT)
  */

/*! Bezier curve function generator. Copyright <PERSON><PERSON><PERSON>. MIT License: http://en.wikipedia.org/wiki/MIT_License */

/*! Runge-Kutta spring physics function generator. Adapted from Framer.js, copyright Koen <PERSON>. MIT License: http://en.wikipedia.org/wiki/MIT_License */
