!function(){var t,e,n={1860:function(t){!function(e,n){var r={version:"2.14.2",areas:{},apis:{},nsdelim:".",inherit:function(t,e){for(var n in t)e.hasOwnProperty(n)||Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n));return e},stringify:function(t,e){return void 0===t||"function"==typeof t?t+"":JSON.stringify(t,e||r.replace)},parse:function(t,e){try{return JSON.parse(t,e||r.revive)}catch(e){return t}},fn:function(t,e){for(var n in r.storeAPI[t]=e,r.apis)r.apis[n][t]=e},get:function(t,e){return t.getItem(e)},set:function(t,e,n){t.setItem(e,n)},remove:function(t,e){t.removeItem(e)},key:function(t,e){return t.key(e)},length:function(t){return t.length},clear:function(t){t.clear()},Store:function(t,e,n){var i=r.inherit(r.storeAPI,(function(t,e,n){return 0===arguments.length?i.getAll():"function"==typeof e?i.transact(t,e,n):void 0!==e?i.set(t,e,n):"string"==typeof t||"number"==typeof t?i.get(t):"function"==typeof t?i.each(t):t?i.setAll(t,e):i.clear()}));i._id=t;try{var a="__store2_test";e.setItem(a,"ok"),i._area=e,e.removeItem(a)}catch(t){i._area=r.storage("fake")}return i._ns=n||"",r.areas[t]||(r.areas[t]=i._area),r.apis[i._ns+i._id]||(r.apis[i._ns+i._id]=i),i},storeAPI:{area:function(t,e){var n=this[t];return n&&n.area||(n=r.Store(t,e,this._ns),this[t]||(this[t]=n)),n},namespace:function(t,e,n){if(n=n||this._delim||r.nsdelim,!t)return this._ns?this._ns.substring(0,this._ns.length-n.length):"";var i=t,a=this[i];if(!(a&&a.namespace||((a=r.Store(this._id,this._area,this._ns+i+n))._delim=n,this[i]||(this[i]=a),e)))for(var o in r.areas)a.area(o,r.areas[o]);return a},isFake:function(t){return t?(this._real=this._area,this._area=r.storage("fake")):!1===t&&(this._area=this._real||this._area),"fake"===this._area.name},toString:function(){return"store"+(this._ns?"."+this.namespace():"")+"["+this._id+"]"},has:function(t){return this._area.has?this._area.has(this._in(t)):!!(this._in(t)in this._area)},size:function(){return this.keys().length},each:function(t,e){for(var n=0,i=r.length(this._area);n<i;n++){var a=this._out(r.key(this._area,n));if(void 0!==a&&!1===t.call(this,a,this.get(a),e))break;i>r.length(this._area)&&(i--,n--)}return e||this},keys:function(t){return this.each((function(t,e,n){n.push(t)}),t||[])},get:function(t,e){var n,i=r.get(this._area,this._in(t));return"function"==typeof e&&(n=e,e=null),null!==i?r.parse(i,n):null!=e?e:i},getAll:function(t){return this.each((function(t,e,n){n[t]=e}),t||{})},transact:function(t,e,n){var r=this.get(t,n),i=e(r);return this.set(t,void 0===i?r:i),this},set:function(t,e,n){var i,a=this.get(t);return null!=a&&!1===n?e:("function"==typeof n&&(i=n,n=void 0),r.set(this._area,this._in(t),r.stringify(e,i),n)||a)},setAll:function(t,e){var n,r;for(var i in t)r=t[i],this.set(i,r,e)!==r&&(n=!0);return n},add:function(t,e,n){var i=this.get(t);if(i instanceof Array)e=i.concat(e);else if(null!==i){var a=typeof i;if(a===typeof e&&"object"===a){for(var o in e)i[o]=e[o];e=i}else e=i+e}return r.set(this._area,this._in(t),r.stringify(e,n)),e},remove:function(t,e){var n=this.get(t,e);return r.remove(this._area,this._in(t)),n},clear:function(){return this._ns?this.each((function(t){r.remove(this._area,this._in(t))}),1):r.clear(this._area),this},clearAll:function(){var t=this._area;for(var e in r.areas)r.areas.hasOwnProperty(e)&&(this._area=r.areas[e],this.clear());return this._area=t,this},_in:function(t){return"string"!=typeof t&&(t=r.stringify(t)),this._ns?this._ns+t:t},_out:function(t){return this._ns?t&&0===t.indexOf(this._ns)?t.substring(this._ns.length):void 0:t}},storage:function(t){return r.inherit(r.storageAPI,{items:{},name:t})},storageAPI:{length:0,has:function(t){return this.items.hasOwnProperty(t)},key:function(t){var e=0;for(var n in this.items)if(this.has(n)&&t===e++)return n},setItem:function(t,e){this.has(t)||this.length++,this.items[t]=e},removeItem:function(t){this.has(t)&&(delete this.items[t],this.length--)},getItem:function(t){return this.has(t)?this.items[t]:null},clear:function(){for(var t in this.items)this.removeItem(t)}}},i=r.Store("local",function(){try{return localStorage}catch(t){}}());i.local=i,i._=r,i.area("session",function(){try{return sessionStorage}catch(t){}}()),i.area("page",r.storage("page")),"function"==typeof n&&void 0!==n.amd?n("store2",[],(function(){return i})):t.exports?t.exports=i:(e.store&&(r.conflict=e.store),e.store=i)}(this,this&&this.define)},6914:function(t,e,n){"use strict";n.r(e),n.d(e,{COLOR_THEME_AUTO:function(){return a},COLOR_THEME_DARK:function(){return r},COLOR_THEME_LIGHT:function(){return i},THEME:function(){return o},TOGGLE_COLOR_THEMES:function(){return s}});const r="dark",i="light",a="auto",o="hugo-geekdoc",s=[a,r,i]}},r={};function i(t){var e=r[t];if(void 0!==e)return e.exports;var a=r[t]={exports:{}};return n[t].call(a.exports,a,a.exports,i),a.exports}i.m=n,i.d=function(t,e){for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.f={},i.e=function(t){return Promise.all(Object.keys(i.f).reduce((function(e,n){return i.f[n](t,e),e}),[]))},i.u=function(t){return"js/"+t+"-"+{105:"7a46a89c",120:"8522b24e",177:"040c64bb",209:"c3adcb05",246:"0f95be8b",264:"68fcc9b2",311:"5bd6ac4e",321:"479928b6",422:"156f22f0",435:"486719a5",451:"2756cb7b",522:"68ed520b",604:"5faadc0f",637:"3456bdaf",648:"d659639f",670:"b8cde8a2",684:"c45993cc",759:"e5cc88d0",770:"a679cffc",796:"b653a12b",851:"43fe3c07",861:"619d670c",880:"5781b8a1",902:"7a89ddf4",968:"bb8c6ba1",978:"b383dcdf",980:"8efa2815"}[t]+".chunk.min.js"},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},t={},e="geekdoc:",i.l=function(n,r,a,o){if(t[n])t[n].push(r);else{var s,c;if(void 0!==a)for(var u=document.getElementsByTagName("script"),f=0;f<u.length;f++){var h=u[f];if(h.getAttribute("src")==n||h.getAttribute("data-webpack")==e+a){s=h;break}}s||(c=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,i.nc&&s.setAttribute("nonce",i.nc),s.setAttribute("data-webpack",e+a),s.src=n),t[n]=[r];var l=function(e,r){s.onerror=s.onload=null,clearTimeout(d);var i=t[n];if(delete t[n],s.parentNode&&s.parentNode.removeChild(s),i&&i.forEach((function(t){return t(r)})),e)return e(r)},d=setTimeout(l.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=l.bind(null,s.onerror),s.onload=l.bind(null,s.onload),c&&document.head.appendChild(s)}},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},function(){var t;i.g.importScripts&&(t=i.g.location+"");var e=i.g.document;if(!t&&e&&(e.currentScript&&(t=e.currentScript.src),!t)){var n=e.getElementsByTagName("script");if(n.length)for(var r=n.length-1;r>-1&&!t;)t=n[r--].src}if(!t)throw new Error("Automatic publicPath is not supported in this browser");t=t.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),i.p=t+"../"}(),function(){var t={552:0};i.f.j=function(e,n){var r=i.o(t,e)?t[e]:void 0;if(0!==r)if(r)n.push(r[2]);else{var a=new Promise((function(n,i){r=t[e]=[n,i]}));n.push(r[2]=a);var o=i.p+i.u(e),s=new Error;i.l(o,(function(n){if(i.o(t,e)&&(0!==(r=t[e])&&(t[e]=void 0),r)){var a=n&&("load"===n.type?"missing":n.type),o=n&&n.target&&n.target.src;s.message="Loading chunk "+e+" failed.\n("+a+": "+o+")",s.name="ChunkLoadError",s.type=a,s.request=o,r[1](s)}}),"chunk-"+e,e)}};var e=function(e,n){var r,a,o=n[0],s=n[1],c=n[2],u=0;if(o.some((function(e){return 0!==t[e]}))){for(r in s)i.o(s,r)&&(i.m[r]=s[r]);c&&c(i)}for(e&&e(n);u<o.length;u++)a=o[u],i.o(t,a)&&t[a]&&t[a][0](),t[a]=0},n=self.webpackChunkgeekdoc=self.webpackChunkgeekdoc||[];n.forEach(e.bind(null,0)),n.push=e.bind(null,n.push.bind(n))}(),function(){const t=i(1860),{COLOR_THEME_DARK:e,THEME:n,COLOR_THEME_AUTO:r}=i(6914);document.addEventListener("DOMContentLoaded",(function(a){let o=t.namespace(n).get("color-theme")||r,s=window.matchMedia("(prefers-color-scheme: dark)"),c=!1,u="default";(o===e||o===r&&s.matches)&&(c=!0,u="dark"),i.e(637).then(i.bind(i,6637)).then((({default:t})=>{t.initialize({flowchart:{useMaxWidth:!0},theme:u,themeVariables:{darkMode:c}})})).catch((t=>console.error(t)))}))}()}();