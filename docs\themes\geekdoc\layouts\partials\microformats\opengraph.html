{{ $isPage := or (and (ne .Type "posts") (in "section page" .Kind )) (and (eq .Type "posts") (eq .Kind "page")) }}

{{- if ne .Kind "home" }}
  <meta
    property="og:title"
    {{ partial "utils/title" . | printf "content=%q" | safeHTMLAttr }}
  />
{{- end }}
{{- with .Site.Title }}
  <meta property="og:site_name" {{ . | printf "content=%q" | safeHTMLAttr }} />
{{- end }}
{{- with partial "utils/featured" . }}
  <meta property="og:image" content="{{ . }}" />
{{- end }}
{{- with partial "utils/description" . }}
  <meta property="og:description" content="{{ . | plainify | htmlUnescape | chomp }}" />
{{- end }}
<meta property="og:type" content="{{ if $isPage }}article{{ else }}website{{ end }}" />
<meta property="og:url" content="{{ .Permalink }}" />
{{- with .Params.audio }}
  <meta property="og:audio" content="{{ . }}" />
{{- end }}
{{- with .Params.locale }}
  <meta property="og:locale" content="{{ . }}" />
{{- end }}
{{- with .Params.videos }}
  {{- range . }}
    <meta property="og:video" content="{{ . | absURL }}" />
  {{- end }}
{{- end }}

{{- /* If it is part of a series, link to related articles */}}
{{- if .Site.Taxonomies.series }}
  {{- $permalink := .Permalink -}}
  {{- $siteSeries := .Site.Taxonomies.series -}}
  {{- with .Params.series }}
    {{- range $name := . }}
      {{- $series := index $siteSeries ($name | urlize) }}
      {{- range $page := first 6 $series.Pages }}
        {{- if ne $page.Permalink $permalink }}
          <meta property="og:see_also" content="{{ $page.Permalink }}" />
        {{- end }}
      {{- end }}
    {{- end }}
  {{- end }}
{{- end }}

{{ if $isPage -}}
  {{- $iso8601 := "2006-01-02T15:04:05-07:00" -}}
  <meta property="article:section" content="{{ .Section | humanize | title }}" />
  {{- with .PublishDate }}
    <meta
      property="article:published_time"
      {{ .Format $iso8601 | printf "content=%q" | safeHTMLAttr }}
    />
  {{- end }}
  {{- with .Lastmod }}
    <meta
      property="article:modified_time"
      {{ .Format $iso8601 | printf "content=%q" | safeHTMLAttr }}
    />
  {{- end }}
{{- end }}

{{- /* Facebook Page Admin ID for Domain Insights */}}
{{- with .Site.Social.facebook_admin }}
  <meta property="fb:admins" content="{{ . }}" />
{{- end }}
