/*! For license information please see 968-bb8c6ba1.chunk.min.js.LICENSE.txt */
(self.webpackChunkgeekdoc=self.webpackChunkgeekdoc||[]).push([[968],{4182:function(e,t,n){var r;r=function(e){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.i=function(e){return e},n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=7)}([function(t,n){t.exports=e},function(e,t,n){"use strict";var r=n(0).FDLayoutConstants;function i(){}for(var a in r)i[a]=r[a];i.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,i.DEFAULT_RADIAL_SEPARATION=r.DEFAULT_EDGE_LENGTH,i.DEFAULT_COMPONENT_SEPERATION=60,i.TILE=!0,i.TILING_PADDING_VERTICAL=10,i.TILING_PADDING_HORIZONTAL=10,i.TREE_REDUCTION_ON_INCREMENTAL=!1,e.exports=i},function(e,t,n){"use strict";var r=n(0).FDLayoutEdge;function i(e,t,n){r.call(this,e,t,n)}for(var a in i.prototype=Object.create(r.prototype),r)i[a]=r[a];e.exports=i},function(e,t,n){"use strict";var r=n(0).LGraph;function i(e,t,n){r.call(this,e,t,n)}for(var a in i.prototype=Object.create(r.prototype),r)i[a]=r[a];e.exports=i},function(e,t,n){"use strict";var r=n(0).LGraphManager;function i(e){r.call(this,e)}for(var a in i.prototype=Object.create(r.prototype),r)i[a]=r[a];e.exports=i},function(e,t,n){"use strict";var r=n(0).FDLayoutNode,i=n(0).IMath;function a(e,t,n,i){r.call(this,e,t,n,i)}for(var o in a.prototype=Object.create(r.prototype),r)a[o]=r[o];a.prototype.move=function(){var e=this.graphManager.getLayout();this.displacementX=e.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY=e.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren,Math.abs(this.displacementX)>e.coolingFactor*e.maxNodeDisplacement&&(this.displacementX=e.coolingFactor*e.maxNodeDisplacement*i.sign(this.displacementX)),Math.abs(this.displacementY)>e.coolingFactor*e.maxNodeDisplacement&&(this.displacementY=e.coolingFactor*e.maxNodeDisplacement*i.sign(this.displacementY)),null==this.child||0==this.child.getNodes().length?this.moveBy(this.displacementX,this.displacementY):this.propogateDisplacementToChildren(this.displacementX,this.displacementY),e.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},a.prototype.propogateDisplacementToChildren=function(e,t){for(var n,r=this.getChild().getNodes(),i=0;i<r.length;i++)null==(n=r[i]).getChild()?(n.moveBy(e,t),n.displacementX+=e,n.displacementY+=t):n.propogateDisplacementToChildren(e,t)},a.prototype.setPred1=function(e){this.pred1=e},a.prototype.getPred1=function(){return pred1},a.prototype.getPred2=function(){return pred2},a.prototype.setNext=function(e){this.next=e},a.prototype.getNext=function(){return next},a.prototype.setProcessed=function(e){this.processed=e},a.prototype.isProcessed=function(){return processed},e.exports=a},function(e,t,n){"use strict";var r=n(0).FDLayout,i=n(4),a=n(3),o=n(5),s=n(2),l=n(1),u=n(0).FDLayoutConstants,c=n(0).LayoutConstants,h=n(0).Point,d=n(0).PointD,p=n(0).Layout,g=n(0).Integer,f=n(0).IGeometry,v=n(0).LGraph,y=n(0).Transform;function m(){r.call(this),this.toBeTiled={}}for(var b in m.prototype=Object.create(r.prototype),r)m[b]=r[b];m.prototype.newGraphManager=function(){var e=new i(this);return this.graphManager=e,e},m.prototype.newGraph=function(e){return new a(null,this.graphManager,e)},m.prototype.newNode=function(e){return new o(this.graphManager,e)},m.prototype.newEdge=function(e){return new s(null,null,e)},m.prototype.initParameters=function(){r.prototype.initParameters.call(this,arguments),this.isSubLayout||(l.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=l.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=l.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.springConstant=u.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=u.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=u.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=u.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=u.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=u.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1,this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/u.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=u.CONVERGENCE_CHECK_PERIOD/this.maxIterations,this.coolingAdjuster=1)},m.prototype.layout=function(){return c.DEFAULT_CREATE_BENDS_AS_NEEDED&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},m.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental)l.TREE_REDUCTION_ON_INCREMENTAL&&(this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation(),t=new Set(this.getAllNodes()),n=this.nodesWithGravity.filter((function(e){return t.has(e)})),this.graphManager.setAllNodesToApplyGravitation(n));else{var e=this.getFlatForest();if(e.length>0)this.positionNodesRadially(e);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var t=new Set(this.getAllNodes()),n=this.nodesWithGravity.filter((function(e){return t.has(e)}));this.graphManager.setAllNodesToApplyGravitation(n),this.positionNodesRandomly()}}return this.initSpringEmbedder(),this.runSpringEmbedder(),!0},m.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished){if(!(this.prunedNodesAll.length>0))return!0;this.isTreeGrowing=!0}if(this.totalIterations%u.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged()){if(!(this.prunedNodesAll.length>0))return!0;this.isTreeGrowing=!0}this.coolingCycle++,0==this.layoutQuality?this.coolingAdjuster=this.coolingCycle:1==this.layoutQuality&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var e=new Set(this.getAllNodes()),t=this.nodesWithGravity.filter((function(t){return e.has(t)}));this.graphManager.setAllNodesToApplyGravitation(t),this.graphManager.updateBounds(),this.updateGrid(),this.coolingFactor=u.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),this.coolingFactor=u.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var n=!this.isTreeGrowing&&!this.isGrowthFinished,r=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(n,r),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},m.prototype.getPositionsData=function(){for(var e=this.graphManager.getAllNodes(),t={},n=0;n<e.length;n++){var r=e[n].rect,i=e[n].id;t[i]={id:i,x:r.getCenterX(),y:r.getCenterY(),w:r.width,h:r.height}}return t},m.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var e=!1;if("during"===u.ANIMATE)this.emit("layoutstarted");else{for(;!e;)e=this.tick();this.graphManager.updateBounds()}},m.prototype.calculateNodesToApplyGravitationTo=function(){var e,t,n=[],r=this.graphManager.getGraphs(),i=r.length;for(t=0;t<i;t++)(e=r[t]).updateConnected(),e.isConnected||(n=n.concat(e.getNodes()));return n},m.prototype.createBendpoints=function(){var e=[];e=e.concat(this.graphManager.getAllEdges());var t,n=new Set;for(t=0;t<e.length;t++){var r=e[t];if(!n.has(r)){var i=r.getSource(),a=r.getTarget();if(i==a)r.getBendpoints().push(new d),r.getBendpoints().push(new d),this.createDummyNodesForBendpoints(r),n.add(r);else{var o=[];if(o=(o=o.concat(i.getEdgeListToNode(a))).concat(a.getEdgeListToNode(i)),!n.has(o[0])){var s;if(o.length>1)for(s=0;s<o.length;s++){var l=o[s];l.getBendpoints().push(new d),this.createDummyNodesForBendpoints(l)}o.forEach((function(e){n.add(e)}))}}}if(n.size==e.length)break}},m.prototype.positionNodesRadially=function(e){for(var t=new h(0,0),n=Math.ceil(Math.sqrt(e.length)),r=0,i=0,a=0,o=new d(0,0),s=0;s<e.length;s++){s%n==0&&(a=0,i=r,0!=s&&(i+=l.DEFAULT_COMPONENT_SEPERATION),r=0);var u=e[s],g=p.findCenterOfTree(u);t.x=a,t.y=i,(o=m.radialLayout(u,g,t)).y>r&&(r=Math.floor(o.y)),a=Math.floor(o.x+l.DEFAULT_COMPONENT_SEPERATION)}this.transform(new d(c.WORLD_CENTER_X-o.x/2,c.WORLD_CENTER_Y-o.y/2))},m.radialLayout=function(e,t,n){var r=Math.max(this.maxDiagonalInTree(e),l.DEFAULT_RADIAL_SEPARATION);m.branchRadialLayout(t,null,0,359,0,r);var i=v.calculateBounds(e),a=new y;a.setDeviceOrgX(i.getMinX()),a.setDeviceOrgY(i.getMinY()),a.setWorldOrgX(n.x),a.setWorldOrgY(n.y);for(var o=0;o<e.length;o++)e[o].transform(a);var s=new d(i.getMaxX(),i.getMaxY());return a.inverseTransformPoint(s)},m.branchRadialLayout=function(e,t,n,r,i,a){var o=(r-n+1)/2;o<0&&(o+=180);var s=(o+n)%360*f.TWO_PI/360,l=(Math.cos(s),i*Math.cos(s)),u=i*Math.sin(s);e.setCenter(l,u);var c=[],h=(c=c.concat(e.getEdges())).length;null!=t&&h--;for(var d,p=0,g=c.length,v=e.getEdgesBetween(t);v.length>1;){var y=v[0];v.splice(0,1);var b=c.indexOf(y);b>=0&&c.splice(b,1),g--,h--}d=null!=t?(c.indexOf(v[0])+1)%g:0;for(var x=Math.abs(r-n)/h,w=d;p!=h;w=++w%g){var E=c[w].getOtherEnd(e);if(E!=t){var _=(n+p*x)%360,T=(_+x)%360;m.branchRadialLayout(E,e,_,T,i+a,a),p++}}},m.maxDiagonalInTree=function(e){for(var t=g.MIN_VALUE,n=0;n<e.length;n++){var r=e[n].getDiagonal();r>t&&(t=r)}return t},m.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},m.prototype.groupZeroDegreeMembers=function(){var e=this,t={};this.memberGroups={},this.idToDummyNode={};for(var n=[],r=this.graphManager.getAllNodes(),i=0;i<r.length;i++){var a=(s=r[i]).getParent();0!==this.getNodeDegreeWithChildren(s)||null!=a.id&&this.getToBeTiled(a)||n.push(s)}for(i=0;i<n.length;i++){var s,l=(s=n[i]).getParent().id;void 0===t[l]&&(t[l]=[]),t[l]=t[l].concat(s)}Object.keys(t).forEach((function(n){if(t[n].length>1){var r="DummyCompound_"+n;e.memberGroups[r]=t[n];var i=t[n][0].getParent(),a=new o(e.graphManager);a.id=r,a.paddingLeft=i.paddingLeft||0,a.paddingRight=i.paddingRight||0,a.paddingBottom=i.paddingBottom||0,a.paddingTop=i.paddingTop||0,e.idToDummyNode[r]=a;var s=e.getGraphManager().add(e.newGraph(),a),l=i.getChild();l.add(a);for(var u=0;u<t[n].length;u++){var c=t[n][u];l.remove(c),s.add(c)}}}))},m.prototype.clearCompounds=function(){var e={},t={};this.performDFSOnCompounds();for(var n=0;n<this.compoundOrder.length;n++)t[this.compoundOrder[n].id]=this.compoundOrder[n],e[this.compoundOrder[n].id]=[].concat(this.compoundOrder[n].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[n].getChild()),this.compoundOrder[n].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(e,t)},m.prototype.clearZeroDegreeMembers=function(){var e=this,t=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach((function(n){var r=e.idToDummyNode[n];t[n]=e.tileNodes(e.memberGroups[n],r.paddingLeft+r.paddingRight),r.rect.width=t[n].width,r.rect.height=t[n].height}))},m.prototype.repopulateCompounds=function(){for(var e=this.compoundOrder.length-1;e>=0;e--){var t=this.compoundOrder[e],n=t.id,r=t.paddingLeft,i=t.paddingTop;this.adjustLocations(this.tiledMemberPack[n],t.rect.x,t.rect.y,r,i)}},m.prototype.repopulateZeroDegreeMembers=function(){var e=this,t=this.tiledZeroDegreePack;Object.keys(t).forEach((function(n){var r=e.idToDummyNode[n],i=r.paddingLeft,a=r.paddingTop;e.adjustLocations(t[n],r.rect.x,r.rect.y,i,a)}))},m.prototype.getToBeTiled=function(e){var t=e.id;if(null!=this.toBeTiled[t])return this.toBeTiled[t];var n=e.getChild();if(null==n)return this.toBeTiled[t]=!1,!1;for(var r=n.getNodes(),i=0;i<r.length;i++){var a=r[i];if(this.getNodeDegree(a)>0)return this.toBeTiled[t]=!1,!1;if(null!=a.getChild()){if(!this.getToBeTiled(a))return this.toBeTiled[t]=!1,!1}else this.toBeTiled[a.id]=!1}return this.toBeTiled[t]=!0,!0},m.prototype.getNodeDegree=function(e){e.id;for(var t=e.getEdges(),n=0,r=0;r<t.length;r++){var i=t[r];i.getSource().id!==i.getTarget().id&&(n+=1)}return n},m.prototype.getNodeDegreeWithChildren=function(e){var t=this.getNodeDegree(e);if(null==e.getChild())return t;for(var n=e.getChild().getNodes(),r=0;r<n.length;r++){var i=n[r];t+=this.getNodeDegreeWithChildren(i)}return t},m.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},m.prototype.fillCompexOrderByDFS=function(e){for(var t=0;t<e.length;t++){var n=e[t];null!=n.getChild()&&this.fillCompexOrderByDFS(n.getChild().getNodes()),this.getToBeTiled(n)&&this.compoundOrder.push(n)}},m.prototype.adjustLocations=function(e,t,n,r,i){n+=i;for(var a=t+=r,o=0;o<e.rows.length;o++){var s=e.rows[o];t=a;for(var l=0,u=0;u<s.length;u++){var c=s[u];c.rect.x=t,c.rect.y=n,t+=c.rect.width+e.horizontalPadding,c.rect.height>l&&(l=c.rect.height)}n+=l+e.verticalPadding}},m.prototype.tileCompoundMembers=function(e,t){var n=this;this.tiledMemberPack=[],Object.keys(e).forEach((function(r){var i=t[r];n.tiledMemberPack[r]=n.tileNodes(e[r],i.paddingLeft+i.paddingRight),i.rect.width=n.tiledMemberPack[r].width,i.rect.height=n.tiledMemberPack[r].height}))},m.prototype.tileNodes=function(e,t){var n={rows:[],rowWidth:[],rowHeight:[],width:0,height:t,verticalPadding:l.TILING_PADDING_VERTICAL,horizontalPadding:l.TILING_PADDING_HORIZONTAL};e.sort((function(e,t){return e.rect.width*e.rect.height>t.rect.width*t.rect.height?-1:e.rect.width*e.rect.height<t.rect.width*t.rect.height?1:0}));for(var r=0;r<e.length;r++){var i=e[r];0==n.rows.length?this.insertNodeToRow(n,i,0,t):this.canAddHorizontal(n,i.rect.width,i.rect.height)?this.insertNodeToRow(n,i,this.getShortestRowIndex(n),t):this.insertNodeToRow(n,i,n.rows.length,t),this.shiftToLastRow(n)}return n},m.prototype.insertNodeToRow=function(e,t,n,r){var i=r;n==e.rows.length&&(e.rows.push([]),e.rowWidth.push(i),e.rowHeight.push(0));var a=e.rowWidth[n]+t.rect.width;e.rows[n].length>0&&(a+=e.horizontalPadding),e.rowWidth[n]=a,e.width<a&&(e.width=a);var o=t.rect.height;n>0&&(o+=e.verticalPadding);var s=0;o>e.rowHeight[n]&&(s=e.rowHeight[n],e.rowHeight[n]=o,s=e.rowHeight[n]-s),e.height+=s,e.rows[n].push(t)},m.prototype.getShortestRowIndex=function(e){for(var t=-1,n=Number.MAX_VALUE,r=0;r<e.rows.length;r++)e.rowWidth[r]<n&&(t=r,n=e.rowWidth[r]);return t},m.prototype.getLongestRowIndex=function(e){for(var t=-1,n=Number.MIN_VALUE,r=0;r<e.rows.length;r++)e.rowWidth[r]>n&&(t=r,n=e.rowWidth[r]);return t},m.prototype.canAddHorizontal=function(e,t,n){var r=this.getShortestRowIndex(e);if(r<0)return!0;var i=e.rowWidth[r];if(i+e.horizontalPadding+t<=e.width)return!0;var a,o,s=0;return e.rowHeight[r]<n&&r>0&&(s=n+e.verticalPadding-e.rowHeight[r]),a=e.width-i>=t+e.horizontalPadding?(e.height+s)/(i+t+e.horizontalPadding):(e.height+s)/e.width,s=n+e.verticalPadding,(o=e.width<t?(e.height+s)/t:(e.height+s)/e.width)<1&&(o=1/o),a<1&&(a=1/a),a<o},m.prototype.shiftToLastRow=function(e){var t=this.getLongestRowIndex(e),n=e.rowWidth.length-1,r=e.rows[t],i=r[r.length-1],a=i.width+e.horizontalPadding;if(e.width-e.rowWidth[n]>a&&t!=n){r.splice(-1,1),e.rows[n].push(i),e.rowWidth[t]=e.rowWidth[t]-a,e.rowWidth[n]=e.rowWidth[n]+a,e.width=e.rowWidth[instance.getLongestRowIndex(e)];for(var o=Number.MIN_VALUE,s=0;s<r.length;s++)r[s].height>o&&(o=r[s].height);t>0&&(o+=e.verticalPadding);var l=e.rowHeight[t]+e.rowHeight[n];e.rowHeight[t]=o,e.rowHeight[n]<i.height+e.verticalPadding&&(e.rowHeight[n]=i.height+e.verticalPadding);var u=e.rowHeight[t]+e.rowHeight[n];e.height+=u-l,this.shiftToLastRow(e)}},m.prototype.tilingPreLayout=function(){l.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},m.prototype.tilingPostLayout=function(){l.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},m.prototype.reduceTrees=function(){for(var e,t=[],n=!0;n;){var r=this.graphManager.getAllNodes(),i=[];n=!1;for(var a=0;a<r.length;a++)1!=(e=r[a]).getEdges().length||e.getEdges()[0].isInterGraph||null!=e.getChild()||(i.push([e,e.getEdges()[0],e.getOwner()]),n=!0);if(1==n){for(var o=[],s=0;s<i.length;s++)1==i[s][0].getEdges().length&&(o.push(i[s]),i[s][0].getOwner().remove(i[s][0]));t.push(o),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=t},m.prototype.growTree=function(e){for(var t,n=e[e.length-1],r=0;r<n.length;r++)t=n[r],this.findPlaceforPrunedNode(t),t[2].add(t[0]),t[2].add(t[1],t[1].source,t[1].target);e.splice(e.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},m.prototype.findPlaceforPrunedNode=function(e){var t,n,r=e[0],i=(n=r==e[1].source?e[1].target:e[1].source).startX,a=n.finishX,o=n.startY,s=n.finishY,l=[0,0,0,0];if(o>0)for(var c=i;c<=a;c++)l[0]+=this.grid[c][o-1].length+this.grid[c][o].length-1;if(a<this.grid.length-1)for(c=o;c<=s;c++)l[1]+=this.grid[a+1][c].length+this.grid[a][c].length-1;if(s<this.grid[0].length-1)for(c=i;c<=a;c++)l[2]+=this.grid[c][s+1].length+this.grid[c][s].length-1;if(i>0)for(c=o;c<=s;c++)l[3]+=this.grid[i-1][c].length+this.grid[i][c].length-1;for(var h,d,p=g.MAX_VALUE,f=0;f<l.length;f++)l[f]<p?(p=l[f],h=1,d=f):l[f]==p&&h++;if(3==h&&0==p)0==l[0]&&0==l[1]&&0==l[2]?t=1:0==l[0]&&0==l[1]&&0==l[3]?t=0:0==l[0]&&0==l[2]&&0==l[3]?t=3:0==l[1]&&0==l[2]&&0==l[3]&&(t=2);else if(2==h&&0==p){var v=Math.floor(2*Math.random());t=0==l[0]&&0==l[1]?0==v?0:1:0==l[0]&&0==l[2]?0==v?0:2:0==l[0]&&0==l[3]?0==v?0:3:0==l[1]&&0==l[2]?0==v?1:2:0==l[1]&&0==l[3]?0==v?1:3:0==v?2:3}else t=4==h&&0==p?v=Math.floor(4*Math.random()):d;0==t?r.setCenter(n.getCenterX(),n.getCenterY()-n.getHeight()/2-u.DEFAULT_EDGE_LENGTH-r.getHeight()/2):1==t?r.setCenter(n.getCenterX()+n.getWidth()/2+u.DEFAULT_EDGE_LENGTH+r.getWidth()/2,n.getCenterY()):2==t?r.setCenter(n.getCenterX(),n.getCenterY()+n.getHeight()/2+u.DEFAULT_EDGE_LENGTH+r.getHeight()/2):r.setCenter(n.getCenterX()-n.getWidth()/2-u.DEFAULT_EDGE_LENGTH-r.getWidth()/2,n.getCenterY())},e.exports=m},function(e,t,n){"use strict";var r={};r.layoutBase=n(0),r.CoSEConstants=n(1),r.CoSEEdge=n(2),r.CoSEGraph=n(3),r.CoSEGraphManager=n(4),r.CoSELayout=n(6),r.CoSENode=n(5),e.exports=r}])},e.exports=r(n(2241))},4607:function(e,t,n){var r;r=function(e){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.i=function(e){return e},n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=1)}([function(t,n){t.exports=e},function(e,t,n){"use strict";var r=n(0).layoutBase.LayoutConstants,i=n(0).layoutBase.FDLayoutConstants,a=n(0).CoSEConstants,o=n(0).CoSELayout,s=n(0).CoSENode,l=n(0).layoutBase.PointD,u=n(0).layoutBase.DimensionD,c={ready:function(){},stop:function(){},quality:"default",nodeDimensionsIncludeLabels:!1,refresh:30,fit:!0,padding:10,randomize:!0,nodeRepulsion:4500,idealEdgeLength:50,edgeElasticity:.45,nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,animate:"end",animationDuration:500,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.5};function h(e){this.options=function(e,t){var n={};for(var r in e)n[r]=e[r];for(var r in t)n[r]=t[r];return n}(c,e),d(this.options)}var d=function(e){null!=e.nodeRepulsion&&(a.DEFAULT_REPULSION_STRENGTH=i.DEFAULT_REPULSION_STRENGTH=e.nodeRepulsion),null!=e.idealEdgeLength&&(a.DEFAULT_EDGE_LENGTH=i.DEFAULT_EDGE_LENGTH=e.idealEdgeLength),null!=e.edgeElasticity&&(a.DEFAULT_SPRING_STRENGTH=i.DEFAULT_SPRING_STRENGTH=e.edgeElasticity),null!=e.nestingFactor&&(a.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=i.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=e.nestingFactor),null!=e.gravity&&(a.DEFAULT_GRAVITY_STRENGTH=i.DEFAULT_GRAVITY_STRENGTH=e.gravity),null!=e.numIter&&(a.MAX_ITERATIONS=i.MAX_ITERATIONS=e.numIter),null!=e.gravityRange&&(a.DEFAULT_GRAVITY_RANGE_FACTOR=i.DEFAULT_GRAVITY_RANGE_FACTOR=e.gravityRange),null!=e.gravityCompound&&(a.DEFAULT_COMPOUND_GRAVITY_STRENGTH=i.DEFAULT_COMPOUND_GRAVITY_STRENGTH=e.gravityCompound),null!=e.gravityRangeCompound&&(a.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=i.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=e.gravityRangeCompound),null!=e.initialEnergyOnIncremental&&(a.DEFAULT_COOLING_FACTOR_INCREMENTAL=i.DEFAULT_COOLING_FACTOR_INCREMENTAL=e.initialEnergyOnIncremental),"draft"==e.quality?r.QUALITY=0:"proof"==e.quality?r.QUALITY=2:r.QUALITY=1,a.NODE_DIMENSIONS_INCLUDE_LABELS=i.NODE_DIMENSIONS_INCLUDE_LABELS=r.NODE_DIMENSIONS_INCLUDE_LABELS=e.nodeDimensionsIncludeLabels,a.DEFAULT_INCREMENTAL=i.DEFAULT_INCREMENTAL=r.DEFAULT_INCREMENTAL=!e.randomize,a.ANIMATE=i.ANIMATE=r.ANIMATE=e.animate,a.TILE=e.tile,a.TILING_PADDING_VERTICAL="function"==typeof e.tilingPaddingVertical?e.tilingPaddingVertical.call():e.tilingPaddingVertical,a.TILING_PADDING_HORIZONTAL="function"==typeof e.tilingPaddingHorizontal?e.tilingPaddingHorizontal.call():e.tilingPaddingHorizontal};h.prototype.run=function(){var e,t,n=this.options,r=(this.idToLNode={},this.layout=new o),i=this;i.stopped=!1,this.cy=this.options.cy,this.cy.trigger({type:"layoutstart",layout:this});var a=r.newGraphManager();this.gm=a;var s=this.options.eles.nodes(),l=this.options.eles.edges();this.root=a.addRoot(),this.processChildrenList(this.root,this.getTopMostNodes(s),r);for(var u=0;u<l.length;u++){var c=l[u],h=this.idToLNode[c.data("source")],d=this.idToLNode[c.data("target")];h!==d&&0==h.getEdgesBetween(d).length&&(a.add(r.newEdge(),h,d).id=c.id())}var p=function(e,t){"number"==typeof e&&(e=t);var n=e.data("id"),r=i.idToLNode[n];return{x:r.getRect().getCenterX(),y:r.getRect().getCenterY()}},g=function a(){for(var o,s=function(){n.fit&&n.cy.fit(n.eles,n.padding),e||(e=!0,i.cy.one("layoutready",n.ready),i.cy.trigger({type:"layoutready",layout:i}))},l=i.options.refresh,u=0;u<l&&!o;u++)o=i.stopped||i.layout.tick();if(o)return r.checkLayoutSuccess()&&!r.isSubLayout&&r.doPostLayout(),r.tilingPostLayout&&r.tilingPostLayout(),r.isLayoutFinished=!0,i.options.eles.nodes().positions(p),s(),i.cy.one("layoutstop",i.options.stop),i.cy.trigger({type:"layoutstop",layout:i}),t&&cancelAnimationFrame(t),void(e=!1);var c=i.layout.getPositionsData();n.eles.nodes().positions((function(e,t){if("number"==typeof e&&(e=t),!e.isParent()){for(var n=e.id(),r=c[n],i=e;null==r&&(r=c[i.data("parent")]||c["DummyCompound_"+i.data("parent")],c[n]=r,null!=(i=i.parent()[0])););return null!=r?{x:r.x,y:r.y}:{x:e.position("x"),y:e.position("y")}}})),s(),t=requestAnimationFrame(a)};return r.addListener("layoutstarted",(function(){"during"===i.options.animate&&(t=requestAnimationFrame(g))})),r.runLayout(),"during"!==this.options.animate&&(i.options.eles.nodes().not(":parent").layoutPositions(i,i.options,p),e=!1),this},h.prototype.getTopMostNodes=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].id()]=!0;var r=e.filter((function(e,n){"number"==typeof e&&(e=n);for(var r=e.parent()[0];null!=r;){if(t[r.id()])return!1;r=r.parent()[0]}return!0}));return r},h.prototype.processChildrenList=function(e,t,n){for(var r=t.length,i=0;i<r;i++){var a,o,c=t[i],h=c.children(),d=c.layoutDimensions({nodeDimensionsIncludeLabels:this.options.nodeDimensionsIncludeLabels});if((a=null!=c.outerWidth()&&null!=c.outerHeight()?e.add(new s(n.graphManager,new l(c.position("x")-d.w/2,c.position("y")-d.h/2),new u(parseFloat(d.w),parseFloat(d.h)))):e.add(new s(this.graphManager))).id=c.data("id"),a.paddingLeft=parseInt(c.css("padding")),a.paddingTop=parseInt(c.css("padding")),a.paddingRight=parseInt(c.css("padding")),a.paddingBottom=parseInt(c.css("padding")),this.options.nodeDimensionsIncludeLabels&&c.isParent()){var p=c.boundingBox({includeLabels:!0,includeNodes:!1}).w,g=c.boundingBox({includeLabels:!0,includeNodes:!1}).h,f=c.css("text-halign");a.labelWidth=p,a.labelHeight=g,a.labelPos=f}this.idToLNode[c.data("id")]=a,isNaN(a.rect.x)&&(a.rect.x=0),isNaN(a.rect.y)&&(a.rect.y=0),null!=h&&h.length>0&&(o=n.getGraphManager().add(n.newGraph(),a),this.processChildrenList(o,h,n))}},h.prototype.stop=function(){return this.stopped=!0,this};var p=function(e){e("layout","cose-bilkent",h)};"undefined"!=typeof cytoscape&&p(cytoscape),e.exports=p}])},e.exports=r(n(4182))},1377:function(e,t,n){e.exports=function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function i(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,s=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){s=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(s)throw i}}return a}}(e,t)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var l="undefined"==typeof window?null:window,u=l?l.navigator:null;l&&l.document;var c=e(""),h=e({}),d=e((function(){})),p="undefined"==typeof HTMLElement?"undefined":e(HTMLElement),g=function(e){return e&&e.instanceString&&v(e.instanceString)?e.instanceString():null},f=function(t){return null!=t&&e(t)==c},v=function(t){return null!=t&&e(t)===d},y=function(e){return!w(e)&&(Array.isArray?Array.isArray(e):null!=e&&e instanceof Array)},m=function(t){return null!=t&&e(t)===h&&!y(t)&&t.constructor===Object},b=function(t){return null!=t&&e(t)===e(1)&&!isNaN(t)},x=function(e){return"undefined"===p?void 0:null!=e&&e instanceof HTMLElement},w=function(e){return E(e)||_(e)},E=function(e){return"collection"===g(e)&&e._private.single},_=function(e){return"collection"===g(e)&&!e._private.single},T=function(e){return"core"===g(e)},D=function(e){return"stylesheet"===g(e)},C=function(e){return null==e||!(""!==e&&!e.match(/^\s+$/))},N=function(t){return function(t){return null!=t&&e(t)===h}(t)&&v(t.then)},A=function(e,t){t||(t=function(){if(1===arguments.length)return arguments[0];if(0===arguments.length)return"undefined";for(var e=[],t=0;t<arguments.length;t++)e.push(arguments[t]);return e.join("$")});var n=function n(){var r,i=arguments,a=t.apply(this,i),o=n.cache;return(r=o[a])||(r=o[a]=e.apply(this,i)),r};return n.cache={},n},L=A((function(e){return e.replace(/([A-Z])/g,(function(e){return"-"+e.toLowerCase()}))})),k=A((function(e){return e.replace(/(-\w)/g,(function(e){return e[1].toUpperCase()}))})),S=A((function(e,t){return e+t[0].toUpperCase()+t.substring(1)}),(function(e,t){return e+"$"+t})),I=function(e){return C(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},M="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",O="rgb[a]?\\(("+M+"[%]?)\\s*,\\s*("+M+"[%]?)\\s*,\\s*("+M+"[%]?)(?:\\s*,\\s*("+M+"))?\\)",P="rgb[a]?\\((?:"+M+"[%]?)\\s*,\\s*(?:"+M+"[%]?)\\s*,\\s*(?:"+M+"[%]?)(?:\\s*,\\s*(?:"+M+"))?\\)",R="hsl[a]?\\(("+M+")\\s*,\\s*("+M+"[%])\\s*,\\s*("+M+"[%])(?:\\s*,\\s*("+M+"))?\\)",B="hsl[a]?\\((?:"+M+")\\s*,\\s*(?:"+M+"[%])\\s*,\\s*(?:"+M+"[%])(?:\\s*,\\s*(?:"+M+"))?\\)",F=function(e,t){return e<t?-1:e>t?1:0},z=null!=Object.assign?Object.assign.bind(Object):function(e){for(var t=arguments,n=1;n<t.length;n++){var r=t[n];if(null!=r)for(var i=Object.keys(r),a=0;a<i.length;a++){var o=i[a];e[o]=r[o]}}return e},G=function(e){return(y(e)?e:null)||function(e){return Y[e.toLowerCase()]}(e)||function(e){if((4===e.length||7===e.length)&&"#"===e[0]){var t,n,r,i=16;return 4===e.length?(t=parseInt(e[1]+e[1],i),n=parseInt(e[2]+e[2],i),r=parseInt(e[3]+e[3],i)):(t=parseInt(e[1]+e[2],i),n=parseInt(e[3]+e[4],i),r=parseInt(e[5]+e[6],i)),[t,n,r]}}(e)||function(e){var t,n=new RegExp("^"+O+"$").exec(e);if(n){t=[];for(var r=[],i=1;i<=3;i++){var a=n[i];if("%"===a[a.length-1]&&(r[i]=!0),a=parseFloat(a),r[i]&&(a=a/100*255),a<0||a>255)return;t.push(Math.floor(a))}var o=r[1]||r[2]||r[3],s=r[1]&&r[2]&&r[3];if(o&&!s)return;var l=n[4];if(void 0!==l){if((l=parseFloat(l))<0||l>1)return;t.push(l)}}return t}(e)||function(e){var t,n,r,i,a,o,s,l;function u(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}var c=new RegExp("^"+R+"$").exec(e);if(c){if((n=parseInt(c[1]))<0?n=(360- -1*n%360)%360:n>360&&(n%=360),n/=360,(r=parseFloat(c[2]))<0||r>100)return;if(r/=100,(i=parseFloat(c[3]))<0||i>100)return;if(i/=100,void 0!==(a=c[4])&&((a=parseFloat(a))<0||a>1))return;if(0===r)o=s=l=Math.round(255*i);else{var h=i<.5?i*(1+r):i+r-i*r,d=2*i-h;o=Math.round(255*u(d,h,n+1/3)),s=Math.round(255*u(d,h,n)),l=Math.round(255*u(d,h,n-1/3))}t=[o,s,l,a]}return t}(e)},Y={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},X=function(e){for(var t=e.map,n=e.keys,r=n.length,i=0;i<r;i++){var a=n[i];if(m(a))throw Error("Tried to set map with object key");i<n.length-1?(null==t[a]&&(t[a]={}),t=t[a]):t[a]=e.value}},V=function(e){for(var t=e.map,n=e.keys,r=n.length,i=0;i<r;i++){var a=n[i];if(m(a))throw Error("Tried to get map with object key");if(null==(t=t[a]))return t}return t};var U=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},j="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==n.g?n.g:"undefined"!=typeof self?self:{};var q="object"==typeof j&&j&&j.Object===Object&&j,H="object"==typeof self&&self&&self.Object===Object&&self,W=q||H||Function("return this")(),$=function(){return W.Date.now()},K=/\s/;var Z=function(e){for(var t=e.length;t--&&K.test(e.charAt(t)););return t},Q=/^\s+/;var J=function(e){return e?e.slice(0,Z(e)+1).replace(Q,""):e},ee=W.Symbol,te=Object.prototype,ne=te.hasOwnProperty,re=te.toString,ie=ee?ee.toStringTag:void 0;var ae=function(e){var t=ne.call(e,ie),n=e[ie];try{e[ie]=void 0;var r=!0}catch(e){}var i=re.call(e);return r&&(t?e[ie]=n:delete e[ie]),i},oe=Object.prototype.toString;var se=function(e){return oe.call(e)},le=ee?ee.toStringTag:void 0;var ue=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":le&&le in Object(e)?ae(e):se(e)};var ce=function(e){return null!=e&&"object"==typeof e};var he=function(e){return"symbol"==typeof e||ce(e)&&"[object Symbol]"==ue(e)},de=/^[-+]0x[0-9a-f]+$/i,pe=/^0b[01]+$/i,ge=/^0o[0-7]+$/i,fe=parseInt;var ve=function(e){if("number"==typeof e)return e;if(he(e))return NaN;if(U(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=U(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=J(e);var n=pe.test(e);return n||ge.test(e)?fe(e.slice(2),n?2:8):de.test(e)?NaN:+e},ye=Math.max,me=Math.min;var be=function(e,t,n){var r,i,a,o,s,l,u=0,c=!1,h=!1,d=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function p(t){var n=r,a=i;return r=i=void 0,u=t,o=e.apply(a,n)}function g(e){var n=e-l;return void 0===l||n>=t||n<0||h&&e-u>=a}function f(){var e=$();if(g(e))return v(e);s=setTimeout(f,function(e){var n=t-(e-l);return h?me(n,a-(e-u)):n}(e))}function v(e){return s=void 0,d&&r?p(e):(r=i=void 0,o)}function y(){var e=$(),n=g(e);if(r=arguments,i=this,l=e,n){if(void 0===s)return function(e){return u=e,s=setTimeout(f,t),c?p(e):o}(l);if(h)return clearTimeout(s),s=setTimeout(f,t),p(l)}return void 0===s&&(s=setTimeout(f,t)),o}return t=ve(t)||0,U(n)&&(c=!!n.leading,a=(h="maxWait"in n)?ye(ve(n.maxWait)||0,t):a,d="trailing"in n?!!n.trailing:d),y.cancel=function(){void 0!==s&&clearTimeout(s),u=0,r=l=i=s=void 0},y.flush=function(){return void 0===s?o:v($())},y},xe=l?l.performance:null,we=xe&&xe.now?function(){return xe.now()}:function(){return Date.now()},Ee=function(){if(l){if(l.requestAnimationFrame)return function(e){l.requestAnimationFrame(e)};if(l.mozRequestAnimationFrame)return function(e){l.mozRequestAnimationFrame(e)};if(l.webkitRequestAnimationFrame)return function(e){l.webkitRequestAnimationFrame(e)};if(l.msRequestAnimationFrame)return function(e){l.msRequestAnimationFrame(e)}}return function(e){e&&setTimeout((function(){e(we())}),1e3/60)}}(),_e=function(e){return Ee(e)},Te=we,De=9261,Ce=5381,Ne=function(e){for(var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:De;!(t=e.next()).done;)n=65599*n+t.value|0;return n},Ae=function(e){return 65599*(arguments.length>1&&void 0!==arguments[1]?arguments[1]:De)+e|0},Le=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Ce;return(t<<5)+t+e|0},ke=function(e){return 2097152*e[0]+e[1]},Se=function(e,t){return[Ae(e[0],t[0]),Le(e[1],t[1])]},Ie=function(e,t){var n={value:0,done:!1},r=0,i=e.length;return Ne({next:function(){return r<i?n.value=e.charCodeAt(r++):n.done=!0,n}},t)},Me=function(){return Oe(arguments)},Oe=function(e){for(var t,n=0;n<e.length;n++){var r=e[n];t=0===n?Ie(r):Ie(r,t)}return t},Pe=!0,Re=null!=console.warn,Be=null!=console.trace,Fe=Number.MAX_SAFE_INTEGER||9007199254740991,ze=function(){return!0},Ge=function(){return!1},Ye=function(){return 0},Xe=function(){},Ve=function(e){throw new Error(e)},Ue=function(e){if(void 0===e)return Pe;Pe=!!e},je=function(e){Ue()&&(Re?console.warn(e):(console.log(e),Be&&console.trace()))},qe=function(e){return null==e?e:y(e)?e.slice():m(e)?function(e){return z({},e)}(e):e},He=function(e,t){for(t=e="";e++<36;t+=51*e&52?(15^e?8^Math.random()*(20^e?16:4):4).toString(16):"-");return t},We={},$e=function(){return We},Ke=function(e){var t=Object.keys(e);return function(n){for(var r={},i=0;i<t.length;i++){var a=t[i],o=null==n?void 0:n[a];r[a]=void 0===o?e[a]:o}return r}},Ze=function(e,t,n){for(var r=e.length-1;r>=0&&(e[r]!==t||(e.splice(r,1),!n));r--);},Qe=function(e){e.splice(0,e.length)},Je=function(e,t,n){return n&&(t=S(n,t)),e[t]},et=function(e,t,n,r){n&&(t=S(n,t)),e[t]=r},tt="undefined"!=typeof Map?Map:function(){function e(){t(this,e),this._obj={}}return i(e,[{key:"set",value:function(e,t){return this._obj[e]=t,this}},{key:"delete",value:function(e){return this._obj[e]=void 0,this}},{key:"clear",value:function(){this._obj={}}},{key:"has",value:function(e){return void 0!==this._obj[e]}},{key:"get",value:function(e){return this._obj[e]}}]),e}(),nt=function(){function e(n){if(t(this,e),this._obj=Object.create(null),this.size=0,null!=n){var r;r=null!=n.instanceString&&n.instanceString()===this.instanceString()?n.toArray():n;for(var i=0;i<r.length;i++)this.add(r[i])}}return i(e,[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(e){var t=this._obj;1!==t[e]&&(t[e]=1,this.size++)}},{key:"delete",value:function(e){var t=this._obj;1===t[e]&&(t[e]=0,this.size--)}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(e){return 1===this._obj[e]}},{key:"toArray",value:function(){var e=this;return Object.keys(this._obj).filter((function(t){return e.has(t)}))}},{key:"forEach",value:function(e,t){return this.toArray().forEach(e,t)}}]),e}(),rt="undefined"!==("undefined"==typeof Set?"undefined":e(Set))?Set:nt,it=function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(void 0!==e&&void 0!==t&&T(e)){var r=t.group;if(null==r&&(r=t.data&&null!=t.data.source&&null!=t.data.target?"edges":"nodes"),"nodes"===r||"edges"===r){this.length=1,this[0]=this;var i=this._private={cy:e,single:!0,data:t.data||{},position:t.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:r,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!t.selected,selectable:void 0===t.selectable||!!t.selectable,locked:!!t.locked,grabbed:!1,grabbable:void 0===t.grabbable||!!t.grabbable,pannable:void 0===t.pannable?"edges"===r:!!t.pannable,active:!1,classes:new rt,animation:{current:[],queue:[]},rscratch:{},scratch:t.scratch||{},edges:[],children:[],parent:t.parent&&t.parent.isNode()?t.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(null==i.position.x&&(i.position.x=0),null==i.position.y&&(i.position.y=0),t.renderedPosition){var a=t.renderedPosition,o=e.pan(),s=e.zoom();i.position={x:(a.x-o.x)/s,y:(a.y-o.y)/s}}var l=[];y(t.classes)?l=t.classes:f(t.classes)&&(l=t.classes.split(/\s+/));for(var u=0,c=l.length;u<c;u++){var h=l[u];h&&""!==h&&i.classes.add(h)}this.createEmitter();var d=t.style||t.css;d&&(je("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(d)),(void 0===n||n)&&this.restore()}else Ve("An element must be of type `nodes` or `edges`; you specified `"+r+"`")}else Ve("An element must have a core reference and parameters set")},at=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(t,n,r){var i;m(t)&&!w(t)&&(t=(i=t).roots||i.root,n=i.visit,r=i.directed),r=2!==arguments.length||v(n)?r:n,n=v(n)?n:function(){};for(var a,o=this._private.cy,s=t=f(t)?this.filter(t):t,l=[],u=[],c={},h={},d={},p=0,g=this.byGroup(),y=g.nodes,b=g.edges,x=0;x<s.length;x++){var E=s[x],_=E.id();E.isNode()&&(l.unshift(E),e.bfs&&(d[_]=!0,u.push(E)),h[_]=0)}for(var T=function(){var t=e.bfs?l.shift():l.pop(),i=t.id();if(e.dfs){if(d[i])return"continue";d[i]=!0,u.push(t)}var o,s=h[i],g=c[i],f=null!=g?g.source():null,v=null!=g?g.target():null,m=null==g?void 0:t.same(f)?v[0]:f[0];if(!0===(o=n(t,g,m,p++,s)))return a=t,"break";if(!1===o)return"break";for(var x=t.connectedEdges().filter((function(e){return(!r||e.source().same(t))&&b.has(e)})),w=0;w<x.length;w++){var E=x[w],_=E.connectedNodes().filter((function(e){return!e.same(t)&&y.has(e)})),T=_.id();0===_.length||d[T]||(_=_[0],l.push(_),e.bfs&&(d[T]=!0,u.push(_)),c[T]=E,h[T]=h[i]+1)}};0!==l.length;){var D=T();if("continue"!==D&&"break"===D)break}for(var C=o.collection(),N=0;N<u.length;N++){var A=u[N],L=c[A.id()];null!=L&&C.push(L),C.push(A)}return{path:o.collection(C),found:o.collection(a)}}},ot={breadthFirstSearch:at({bfs:!0}),depthFirstSearch:at({dfs:!0})};ot.bfs=ot.breadthFirstSearch,ot.dfs=ot.depthFirstSearch;var st=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e,t){(function(){var t,n,r,i,a,o,s,l,u,c,h,d,p,g,f;r=Math.floor,c=Math.min,n=function(e,t){return e<t?-1:e>t?1:0},u=function(e,t,i,a,o){var s;if(null==i&&(i=0),null==o&&(o=n),i<0)throw new Error("lo must be non-negative");for(null==a&&(a=e.length);i<a;)o(t,e[s=r((i+a)/2)])<0?a=s:i=s+1;return[].splice.apply(e,[i,i-i].concat(t)),t},o=function(e,t,r){return null==r&&(r=n),e.push(t),g(e,0,e.length-1,r)},a=function(e,t){var r,i;return null==t&&(t=n),r=e.pop(),e.length?(i=e[0],e[0]=r,f(e,0,t)):i=r,i},l=function(e,t,r){var i;return null==r&&(r=n),i=e[0],e[0]=t,f(e,0,r),i},s=function(e,t,r){var i;return null==r&&(r=n),e.length&&r(e[0],t)<0&&(t=(i=[e[0],t])[0],e[0]=i[1],f(e,0,r)),t},i=function(e,t){var i,a,o,s,l,u;for(null==t&&(t=n),l=[],a=0,o=(s=function(){u=[];for(var t=0,n=r(e.length/2);0<=n?t<n:t>n;0<=n?t++:t--)u.push(t);return u}.apply(this).reverse()).length;a<o;a++)i=s[a],l.push(f(e,i,t));return l},p=function(e,t,r){var i;if(null==r&&(r=n),-1!==(i=e.indexOf(t)))return g(e,0,i,r),f(e,i,r)},h=function(e,t,r){var a,o,l,u,c;if(null==r&&(r=n),!(o=e.slice(0,t)).length)return o;for(i(o,r),l=0,u=(c=e.slice(t)).length;l<u;l++)a=c[l],s(o,a,r);return o.sort(r).reverse()},d=function(e,t,r){var o,s,l,h,d,p,g,f,v;if(null==r&&(r=n),10*t<=e.length){if(!(l=e.slice(0,t).sort(r)).length)return l;for(s=l[l.length-1],h=0,p=(g=e.slice(t)).length;h<p;h++)r(o=g[h],s)<0&&(u(l,o,0,null,r),l.pop(),s=l[l.length-1]);return l}for(i(e,r),v=[],d=0,f=c(t,e.length);0<=f?d<f:d>f;0<=f?++d:--d)v.push(a(e,r));return v},g=function(e,t,r,i){var a,o,s;for(null==i&&(i=n),a=e[r];r>t&&i(a,o=e[s=r-1>>1])<0;)e[r]=o,r=s;return e[r]=a},f=function(e,t,r){var i,a,o,s,l;for(null==r&&(r=n),a=e.length,l=t,o=e[t],i=2*t+1;i<a;)(s=i+1)<a&&!(r(e[i],e[s])<0)&&(i=s),e[t]=e[i],i=2*(t=i)+1;return e[t]=o,g(e,l,t,r)},t=function(){function e(e){this.cmp=null!=e?e:n,this.nodes=[]}return e.push=o,e.pop=a,e.replace=l,e.pushpop=s,e.heapify=i,e.updateItem=p,e.nlargest=h,e.nsmallest=d,e.prototype.push=function(e){return o(this.nodes,e,this.cmp)},e.prototype.pop=function(){return a(this.nodes,this.cmp)},e.prototype.peek=function(){return this.nodes[0]},e.prototype.contains=function(e){return-1!==this.nodes.indexOf(e)},e.prototype.replace=function(e){return l(this.nodes,e,this.cmp)},e.prototype.pushpop=function(e){return s(this.nodes,e,this.cmp)},e.prototype.heapify=function(){return i(this.nodes,this.cmp)},e.prototype.updateItem=function(e){return p(this.nodes,e,this.cmp)},e.prototype.clear=function(){return this.nodes=[]},e.prototype.empty=function(){return 0===this.nodes.length},e.prototype.size=function(){return this.nodes.length},e.prototype.clone=function(){var t;return(t=new e).nodes=this.nodes.slice(0),t},e.prototype.toArray=function(){return this.nodes.slice(0)},e.prototype.insert=e.prototype.push,e.prototype.top=e.prototype.peek,e.prototype.front=e.prototype.peek,e.prototype.has=e.prototype.contains,e.prototype.copy=e.prototype.clone,e}(),e.exports=t}).call(j)})),lt=st,ut=Ke({root:null,weight:function(e){return 1},directed:!1}),ct={dijkstra:function(e){if(!m(e)){var t=arguments;e={root:t[0],weight:t[1],directed:t[2]}}var n=ut(e),r=n.root,i=n.weight,a=n.directed,o=this,s=i,l=f(r)?this.filter(r)[0]:r[0],u={},c={},h={},d=this.byGroup(),p=d.nodes,g=d.edges;g.unmergeBy((function(e){return e.isLoop()}));for(var v=function(e){return u[e.id()]},y=function(e,t){u[e.id()]=t,b.updateItem(e)},b=new lt((function(e,t){return v(e)-v(t)})),x=0;x<p.length;x++){var w=p[x];u[w.id()]=w.same(l)?0:1/0,b.push(w)}for(var E=function(e,t){for(var n,r=(a?e.edgesTo(t):e.edgesWith(t)).intersect(g),i=1/0,o=0;o<r.length;o++){var l=r[o],u=s(l);(u<i||!n)&&(i=u,n=l)}return{edge:n,dist:i}};b.size()>0;){var _=b.pop(),T=v(_),D=_.id();if(h[D]=T,T!==1/0)for(var C=_.neighborhood().intersect(p),N=0;N<C.length;N++){var A=C[N],L=A.id(),k=E(_,A),S=T+k.dist;S<v(A)&&(y(A,S),c[L]={node:_,edge:k.edge})}}return{distanceTo:function(e){var t=f(e)?p.filter(e)[0]:e[0];return h[t.id()]},pathTo:function(e){var t=f(e)?p.filter(e)[0]:e[0],n=[],r=t,i=r.id();if(t.length>0)for(n.unshift(t);c[i];){var a=c[i];n.unshift(a.edge),n.unshift(a.node),i=(r=a.node).id()}return o.spawn(n)}}}},ht={kruskal:function(e){e=e||function(e){return 1};for(var t=this.byGroup(),n=t.nodes,r=t.edges,i=n.length,a=new Array(i),o=n,s=function(e){for(var t=0;t<a.length;t++)if(a[t].has(e))return t},l=0;l<i;l++)a[l]=this.spawn(n[l]);for(var u=r.sort((function(t,n){return e(t)-e(n)})),c=0;c<u.length;c++){var h=u[c],d=h.source()[0],p=h.target()[0],g=s(d),f=s(p),v=a[g],y=a[f];g!==f&&(o.merge(h),v.merge(y),a.splice(f,1))}return o}},dt=Ke({root:null,goal:null,weight:function(e){return 1},heuristic:function(e){return 0},directed:!1}),pt={aStar:function(e){var t=this.cy(),n=dt(e),r=n.root,i=n.goal,a=n.heuristic,o=n.directed,s=n.weight;r=t.collection(r)[0],i=t.collection(i)[0];var l,u,c,h=r.id(),d=i.id(),p={},g={},f={},v=new lt((function(e,t){return g[e.id()]-g[t.id()]})),y=new rt,m={},b={},x=function(e,t){v.push(e),y.add(t)};x(r,h),p[h]=0,g[h]=a(r);for(var w=0;v.size()>0;){if(l=v.pop(),u=l.id(),y.delete(u),w++,u===d){for(var E=[],_=i,T=d,D=b[T];E.unshift(_),null!=D&&E.unshift(D),null!=(_=m[T]);)D=b[T=_.id()];return{found:!0,distance:p[u],path:this.spawn(E),steps:w}}f[u]=!0;for(var C=l._private.edges,N=0;N<C.length;N++){var A=C[N];if(this.hasElementWithId(A.id())&&(!o||A.data("source")===u)){var L=A.source(),k=A.target(),S=L.id()!==u?L:k,I=S.id();if(this.hasElementWithId(I)&&!f[I]){var M=p[u]+s(A);c=I,y.has(c)?M<p[I]&&(p[I]=M,g[I]=M+a(S),m[I]=l,b[I]=A):(p[I]=M,g[I]=M+a(S),x(S,I),m[I]=l,b[I]=A)}}}}return{found:!1,distance:void 0,path:void 0,steps:w}}},gt=Ke({weight:function(e){return 1},directed:!1}),ft={floydWarshall:function(e){for(var t=this.cy(),n=gt(e),r=n.weight,i=n.directed,a=r,o=this.byGroup(),s=o.nodes,l=o.edges,u=s.length,c=u*u,h=function(e){return s.indexOf(e)},d=function(e){return s[e]},p=new Array(c),g=0;g<c;g++){var v=g%u,y=(g-v)/u;p[g]=y===v?0:1/0}for(var m=new Array(c),b=new Array(c),x=0;x<l.length;x++){var w=l[x],E=w.source()[0],_=w.target()[0];if(E!==_){var T=h(E),D=h(_),C=T*u+D,N=a(w);if(p[C]>N&&(p[C]=N,m[C]=D,b[C]=w),!i){var A=D*u+T;!i&&p[A]>N&&(p[A]=N,m[A]=T,b[A]=w)}}}for(var L=0;L<u;L++)for(var k=0;k<u;k++)for(var S=k*u+L,I=0;I<u;I++){var M=k*u+I,O=L*u+I;p[S]+p[O]<p[M]&&(p[M]=p[S]+p[O],m[M]=m[S])}var P=function(e){return h(function(e){return(f(e)?t.filter(e):e)[0]}(e))},R={distance:function(e,t){var n=P(e),r=P(t);return p[n*u+r]},path:function(e,n){var r=P(e),i=P(n),a=d(r);if(r===i)return a.collection();if(null==m[r*u+i])return t.collection();var o,s=t.collection(),l=r;for(s.merge(a);r!==i;)l=r,r=m[r*u+i],o=b[l*u+r],s.merge(o),s.merge(d(r));return s}};return R}},vt=Ke({weight:function(e){return 1},directed:!1,root:null}),yt={bellmanFord:function(e){var t=this,n=vt(e),r=n.weight,i=n.directed,a=n.root,o=r,s=this,l=this.cy(),u=this.byGroup(),c=u.edges,h=u.nodes,d=h.length,p=new tt,g=!1,v=[];a=l.collection(a)[0],c.unmergeBy((function(e){return e.isLoop()}));for(var y=c.length,m=function(e){var t=p.get(e.id());return t||(t={},p.set(e.id(),t)),t},b=function(e){return(f(e)?l.$(e):e)[0]},x=0;x<d;x++){var w=h[x],E=m(w);w.same(a)?E.dist=0:E.dist=1/0,E.pred=null,E.edge=null}for(var _=!1,T=function(e,t,n,r,i,a){var o=r.dist+a;o<i.dist&&!n.same(r.edge)&&(i.dist=o,i.pred=e,i.edge=n,_=!0)},D=1;D<d;D++){_=!1;for(var C=0;C<y;C++){var N=c[C],A=N.source(),L=N.target(),k=o(N),S=m(A),I=m(L);T(A,0,N,S,I,k),i||T(L,0,N,I,S,k)}if(!_)break}if(_)for(var M=[],O=0;O<y;O++){var P=c[O],R=P.source(),B=P.target(),F=o(P),z=m(R).dist,G=m(B).dist;if(z+F<G||!i&&G+F<z){if(g||(je("Graph contains a negative weight cycle for Bellman-Ford"),g=!0),!1===e.findNegativeWeightCycles)break;var Y=[];z+F<G&&Y.push(R),!i&&G+F<z&&Y.push(B);for(var X=Y.length,V=0;V<X;V++){var U=Y[V],j=[U];j.push(m(U).edge);for(var q=m(U).pred;-1===j.indexOf(q);)j.push(q),j.push(m(q).edge),q=m(q).pred;for(var H=(j=j.slice(j.indexOf(q)))[0].id(),W=0,$=2;$<j.length;$+=2)j[$].id()<H&&(H=j[$].id(),W=$);(j=j.slice(W).concat(j.slice(0,W))).push(j[0]);var K=j.map((function(e){return e.id()})).join(",");-1===M.indexOf(K)&&(v.push(s.spawn(j)),M.push(K))}}}return{distanceTo:function(e){return m(b(e)).dist},pathTo:function(e){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,r=[],i=b(e);;){if(null==i)return t.spawn();var o=m(i),l=o.edge,u=o.pred;if(r.unshift(i[0]),i.same(n)&&r.length>0)break;null!=l&&r.unshift(l),i=u}return s.spawn(r)},hasNegativeWeightCycle:g,negativeWeightCycles:v}}},mt=Math.sqrt(2),bt=function(e,t,n){0===n.length&&Ve("Karger-Stein must be run on a connected (sub)graph");for(var r=n[e],i=r[1],a=r[2],o=t[i],s=t[a],l=n,u=l.length-1;u>=0;u--){var c=l[u],h=c[1],d=c[2];(t[h]===o&&t[d]===s||t[h]===s&&t[d]===o)&&l.splice(u,1)}for(var p=0;p<l.length;p++){var g=l[p];g[1]===s?(l[p]=g.slice(),l[p][1]=o):g[2]===s&&(l[p]=g.slice(),l[p][2]=o)}for(var f=0;f<t.length;f++)t[f]===s&&(t[f]=o);return l},xt=function(e,t,n,r){for(;n>r;){var i=Math.floor(Math.random()*t.length);t=bt(i,e,t),n--}return t},wt={kargerStein:function(){var e=this,t=this.byGroup(),n=t.nodes,r=t.edges;r.unmergeBy((function(e){return e.isLoop()}));var i=n.length,a=r.length,o=Math.ceil(Math.pow(Math.log(i)/Math.LN2,2)),s=Math.floor(i/mt);if(!(i<2)){for(var l=[],u=0;u<a;u++){var c=r[u];l.push([u,n.indexOf(c.source()),n.indexOf(c.target())])}for(var h=1/0,d=[],p=new Array(i),g=new Array(i),f=new Array(i),v=function(e,t){for(var n=0;n<i;n++)t[n]=e[n]},y=0;y<=o;y++){for(var m=0;m<i;m++)g[m]=m;var b=xt(g,l.slice(),i,s),x=b.slice();v(g,f);var w=xt(g,b,s,2),E=xt(f,x,s,2);w.length<=E.length&&w.length<h?(h=w.length,d=w,v(g,p)):E.length<=w.length&&E.length<h&&(h=E.length,d=E,v(f,p))}for(var _=this.spawn(d.map((function(e){return r[e[0]]}))),T=this.spawn(),D=this.spawn(),C=p[0],N=0;N<p.length;N++){var A=p[N],L=n[N];A===C?T.merge(L):D.merge(L)}var k=function(t){var n=e.spawn();return t.forEach((function(t){n.merge(t),t.connectedEdges().forEach((function(t){e.contains(t)&&!_.contains(t)&&n.merge(t)}))})),n},S=[k(T),k(D)];return{cut:_,components:S,partition1:T,partition2:D}}Ve("At least 2 nodes are required for Karger-Stein algorithm")}},Et=function(e,t,n){return{x:e.x*t+n.x,y:e.y*t+n.y}},_t=function(e,t,n){return{x:(e.x-n.x)/t,y:(e.y-n.y)/t}},Tt=function(e){return{x:e[0],y:e[1]}},Dt=function(e,t){return Math.atan2(t,e)-Math.PI/2},Ct=Math.log2||function(e){return Math.log(e)/Math.log(2)},Nt=function(e){return e>0?1:e<0?-1:0},At=function(e,t){return Math.sqrt(Lt(e,t))},Lt=function(e,t){var n=t.x-e.x,r=t.y-e.y;return n*n+r*r},kt=function(e){for(var t=e.length,n=0,r=0;r<t;r++)n+=e[r];for(var i=0;i<t;i++)e[i]=e[i]/n;return e},St=function(e,t,n,r){return(1-r)*(1-r)*e+2*(1-r)*r*t+r*r*n},It=function(e,t,n,r){return{x:St(e.x,t.x,n.x,r),y:St(e.y,t.y,n.y,r)}},Mt=function(e,t,n){return Math.max(e,Math.min(n,t))},Ot=function(e){if(null==e)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(null!=e.x1&&null!=e.y1){if(null!=e.x2&&null!=e.y2&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(null!=e.w&&null!=e.h&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},Pt=function(e,t,n){e.x1=Math.min(e.x1,t),e.x2=Math.max(e.x2,t),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,n),e.y2=Math.max(e.y2,n),e.h=e.y2-e.y1},Rt=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e.x1-=t,e.x2+=t,e.y1-=t,e.y2+=t,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},Bt=function(e){var t,n,r,i,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[0];if(1===a.length)t=n=r=i=a[0];else if(2===a.length)t=r=a[0],i=n=a[1];else if(4===a.length){var s=o(a,4);t=s[0],n=s[1],r=s[2],i=s[3]}return e.x1-=i,e.x2+=n,e.y1-=t,e.y2+=r,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},Ft=function(e,t){e.x1=t.x1,e.y1=t.y1,e.x2=t.x2,e.y2=t.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},zt=function(e,t){return!(e.x1>t.x2||t.x1>e.x2||e.x2<t.x1||t.x2<e.x1||e.y2<t.y1||t.y2<e.y1||e.y1>t.y2||t.y1>e.y2)},Gt=function(e,t,n){return e.x1<=t&&t<=e.x2&&e.y1<=n&&n<=e.y2},Yt=function(e,t){return Gt(e,t.x1,t.y1)&&Gt(e,t.x2,t.y2)},Xt=function(e,t,n,r,i,a,o){var s,l=sn(i,a),u=i/2,c=a/2,h=r-c-o;if((s=en(e,t,n,r,n-u+l-o,h,n+u-l+o,h,!1)).length>0)return s;var d=n+u+o;if((s=en(e,t,n,r,d,r-c+l-o,d,r+c-l+o,!1)).length>0)return s;var p=r+c+o;if((s=en(e,t,n,r,n-u+l-o,p,n+u-l+o,p,!1)).length>0)return s;var g,f=n-u-o;if((s=en(e,t,n,r,f,r-c+l-o,f,r+c-l+o,!1)).length>0)return s;var v=n-u+l,y=r-c+l;if((g=Qt(e,t,n,r,v,y,l+o)).length>0&&g[0]<=v&&g[1]<=y)return[g[0],g[1]];var m=n+u-l,b=r-c+l;if((g=Qt(e,t,n,r,m,b,l+o)).length>0&&g[0]>=m&&g[1]<=b)return[g[0],g[1]];var x=n+u-l,w=r+c-l;if((g=Qt(e,t,n,r,x,w,l+o)).length>0&&g[0]>=x&&g[1]>=w)return[g[0],g[1]];var E=n-u+l,_=r+c-l;return(g=Qt(e,t,n,r,E,_,l+o)).length>0&&g[0]<=E&&g[1]>=_?[g[0],g[1]]:[]},Vt=function(e,t,n,r,i,a,o){var s=o,l=Math.min(n,i),u=Math.max(n,i),c=Math.min(r,a),h=Math.max(r,a);return l-s<=e&&e<=u+s&&c-s<=t&&t<=h+s},Ut=function(e,t,n,r,i,a,o,s,l){var u=Math.min(n,o,i)-l,c=Math.max(n,o,i)+l,h=Math.min(r,s,a)-l,d=Math.max(r,s,a)+l;return!(e<u||e>c||t<h||t>d)},jt=function(e,t,n,r,i,a,o,s){var l,u,c,h,d,p,g,f,v,y,m,b,x,w=[];u=9*n*i-3*n*n-3*n*o-6*i*i+3*i*o+9*r*a-3*r*r-3*r*s-6*a*a+3*a*s,c=3*n*n-6*n*i+n*o-n*e+2*i*i+2*i*e-o*e+3*r*r-6*r*a+r*s-r*t+2*a*a+2*a*t-s*t,h=1*n*i-n*n+n*e-i*e+r*a-r*r+r*t-a*t,0===(l=1*n*n-4*n*i+2*n*o+4*i*i-4*i*o+o*o+r*r-4*r*a+2*r*s+4*a*a-4*a*s+s*s)&&(l=1e-5),f=-27*(h/=l)+(u/=l)*(9*(c/=l)-u*u*2),p=(g=(3*c-u*u)/9)*g*g+(f/=54)*f,(d=w)[1]=0,b=u/3,p>0?(y=(y=f+Math.sqrt(p))<0?-Math.pow(-y,1/3):Math.pow(y,1/3),m=(m=f-Math.sqrt(p))<0?-Math.pow(-m,1/3):Math.pow(m,1/3),d[0]=-b+y+m,b+=(y+m)/2,d[4]=d[2]=-b,b=Math.sqrt(3)*(-m+y)/2,d[3]=b,d[5]=-b):(d[5]=d[3]=0,0===p?(x=f<0?-Math.pow(-f,1/3):Math.pow(f,1/3),d[0]=2*x-b,d[4]=d[2]=-(x+b)):(v=(g=-g)*g*g,v=Math.acos(f/Math.sqrt(v)),x=2*Math.sqrt(g),d[0]=-b+x*Math.cos(v/3),d[2]=-b+x*Math.cos((v+2*Math.PI)/3),d[4]=-b+x*Math.cos((v+4*Math.PI)/3)));for(var E=[],_=0;_<6;_+=2)Math.abs(w[_+1])<1e-7&&w[_]>=0&&w[_]<=1&&E.push(w[_]);E.push(1),E.push(0);for(var T,D,C,N=-1,A=0;A<E.length;A++)T=Math.pow(1-E[A],2)*n+2*(1-E[A])*E[A]*i+E[A]*E[A]*o,D=Math.pow(1-E[A],2)*r+2*(1-E[A])*E[A]*a+E[A]*E[A]*s,C=Math.pow(T-e,2)+Math.pow(D-t,2),N>=0?C<N&&(N=C):N=C;return N},qt=function(e,t,n,r,i,a){var o=[e-n,t-r],s=[i-n,a-r],l=s[0]*s[0]+s[1]*s[1],u=o[0]*o[0]+o[1]*o[1],c=o[0]*s[0]+o[1]*s[1],h=c*c/l;return c<0?u:h>l?(e-i)*(e-i)+(t-a)*(t-a):u-h},Ht=function(e,t,n){for(var r,i,a,o,s=0,l=0;l<n.length/2;l++)if(r=n[2*l],i=n[2*l+1],l+1<n.length/2?(a=n[2*(l+1)],o=n[2*(l+1)+1]):(a=n[2*(l+1-n.length/2)],o=n[2*(l+1-n.length/2)+1]),r==e&&a==e);else{if(!(r>=e&&e>=a||r<=e&&e<=a))continue;(e-r)/(a-r)*(o-i)+i>t&&s++}return s%2!=0},Wt=function(e,t,n,r,i,a,o,s,l){var u,c=new Array(n.length);null!=s[0]?(u=Math.atan(s[1]/s[0]),s[0]<0?u+=Math.PI/2:u=-u-Math.PI/2):u=s;for(var h,d=Math.cos(-u),p=Math.sin(-u),g=0;g<c.length/2;g++)c[2*g]=a/2*(n[2*g]*d-n[2*g+1]*p),c[2*g+1]=o/2*(n[2*g+1]*d+n[2*g]*p),c[2*g]+=r,c[2*g+1]+=i;if(l>0){var f=Kt(c,-l);h=$t(f)}else h=c;return Ht(e,t,h)},$t=function(e){for(var t,n,r,i,a,o,s,l,u=new Array(e.length/2),c=0;c<e.length/4;c++){t=e[4*c],n=e[4*c+1],r=e[4*c+2],i=e[4*c+3],c<e.length/4-1?(a=e[4*(c+1)],o=e[4*(c+1)+1],s=e[4*(c+1)+2],l=e[4*(c+1)+3]):(a=e[0],o=e[1],s=e[2],l=e[3]);var h=en(t,n,r,i,a,o,s,l,!0);u[2*c]=h[0],u[2*c+1]=h[1]}return u},Kt=function(e,t){for(var n,r,i,a,o=new Array(2*e.length),s=0;s<e.length/2;s++){n=e[2*s],r=e[2*s+1],s<e.length/2-1?(i=e[2*(s+1)],a=e[2*(s+1)+1]):(i=e[0],a=e[1]);var l=a-r,u=-(i-n),c=Math.sqrt(l*l+u*u),h=l/c,d=u/c;o[4*s]=n+h*t,o[4*s+1]=r+d*t,o[4*s+2]=i+h*t,o[4*s+3]=a+d*t}return o},Zt=function(e,t,n,r,i,a,o){return e-=i,t-=a,(e/=n/2+o)*e+(t/=r/2+o)*t<=1},Qt=function(e,t,n,r,i,a,o){var s=[n-e,r-t],l=[e-i,t-a],u=s[0]*s[0]+s[1]*s[1],c=2*(l[0]*s[0]+l[1]*s[1]),h=c*c-4*u*(l[0]*l[0]+l[1]*l[1]-o*o);if(h<0)return[];var d=(-c+Math.sqrt(h))/(2*u),p=(-c-Math.sqrt(h))/(2*u),g=Math.min(d,p),f=Math.max(d,p),v=[];if(g>=0&&g<=1&&v.push(g),f>=0&&f<=1&&v.push(f),0===v.length)return[];var y=v[0]*s[0]+e,m=v[0]*s[1]+t;return v.length>1?v[0]==v[1]?[y,m]:[y,m,v[1]*s[0]+e,v[1]*s[1]+t]:[y,m]},Jt=function(e,t,n){return t<=e&&e<=n||n<=e&&e<=t?e:e<=t&&t<=n||n<=t&&t<=e?t:n},en=function(e,t,n,r,i,a,o,s,l){var u=e-i,c=n-e,h=o-i,d=t-a,p=r-t,g=s-a,f=h*d-g*u,v=c*d-p*u,y=g*c-h*p;if(0!==y){var m=f/y,b=v/y,x=-.001;return x<=m&&m<=1.001&&x<=b&&b<=1.001||l?[e+m*c,t+m*p]:[]}return 0===f||0===v?Jt(e,n,o)===o?[o,s]:Jt(e,n,i)===i?[i,a]:Jt(i,o,n)===n?[n,r]:[]:[]},tn=function(e,t,n,r,i,a,o,s){var l,u,c,h,d,p,g=[],f=new Array(n.length),v=!0;if(null==a&&(v=!1),v){for(var y=0;y<f.length/2;y++)f[2*y]=n[2*y]*a+r,f[2*y+1]=n[2*y+1]*o+i;if(s>0){var m=Kt(f,-s);u=$t(m)}else u=f}else u=n;for(var b=0;b<u.length/2;b++)c=u[2*b],h=u[2*b+1],b<u.length/2-1?(d=u[2*(b+1)],p=u[2*(b+1)+1]):(d=u[0],p=u[1]),0!==(l=en(e,t,r,i,c,h,d,p)).length&&g.push(l[0],l[1]);return g},nn=function(e,t,n){var r=[e[0]-t[0],e[1]-t[1]],i=Math.sqrt(r[0]*r[0]+r[1]*r[1]),a=(i-n)/i;return a<0&&(a=1e-5),[t[0]+a*r[0],t[1]+a*r[1]]},rn=function(e,t){var n=on(e,t);return an(n)},an=function(e){for(var t,n,r=e.length/2,i=1/0,a=1/0,o=-1/0,s=-1/0,l=0;l<r;l++)t=e[2*l],n=e[2*l+1],i=Math.min(i,t),o=Math.max(o,t),a=Math.min(a,n),s=Math.max(s,n);for(var u=2/(o-i),c=2/(s-a),h=0;h<r;h++)t=e[2*h]=e[2*h]*u,n=e[2*h+1]=e[2*h+1]*c,i=Math.min(i,t),o=Math.max(o,t),a=Math.min(a,n),s=Math.max(s,n);if(a<-1)for(var d=0;d<r;d++)n=e[2*d+1]=e[2*d+1]+(-1-a);return e},on=function(e,t){var n=1/e*2*Math.PI,r=e%2==0?Math.PI/2+n/2:Math.PI/2;r+=t;for(var i,a=new Array(2*e),o=0;o<e;o++)i=o*n+r,a[2*o]=Math.cos(i),a[2*o+1]=Math.sin(-i);return a},sn=function(e,t){return Math.min(e/4,t/4,8)},ln=function(e,t){return Math.min(e/10,t/10,8)},un=function(e,t){return{heightOffset:Math.min(15,.05*t),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}},cn=Ke({dampingFactor:.8,precision:1e-6,iterations:200,weight:function(e){return 1}}),hn={pageRank:function(e){for(var t=cn(e),n=t.dampingFactor,r=t.precision,i=t.iterations,a=t.weight,o=this._private.cy,s=this.byGroup(),l=s.nodes,u=s.edges,c=l.length,h=c*c,d=u.length,p=new Array(h),g=new Array(c),f=(1-n)/c,v=0;v<c;v++){for(var y=0;y<c;y++)p[v*c+y]=0;g[v]=0}for(var m=0;m<d;m++){var b=u[m],x=b.data("source"),w=b.data("target");if(x!==w){var E=l.indexOfId(x),_=l.indexOfId(w),T=a(b);p[_*c+E]+=T,g[E]+=T}}for(var D=1/c+f,C=0;C<c;C++)if(0===g[C])for(var N=0;N<c;N++)p[N*c+C]=D;else for(var A=0;A<c;A++){var L=A*c+C;p[L]=p[L]/g[C]+f}for(var k,S=new Array(c),I=new Array(c),M=0;M<c;M++)S[M]=1;for(var O=0;O<i;O++){for(var P=0;P<c;P++)I[P]=0;for(var R=0;R<c;R++)for(var B=0;B<c;B++){var F=R*c+B;I[R]+=p[F]*S[B]}kt(I),k=S,S=I,I=k;for(var z=0,G=0;G<c;G++){var Y=k[G]-S[G];z+=Y*Y}if(z<r)break}return{rank:function(e){return e=o.collection(e)[0],S[l.indexOf(e)]}}}},dn=Ke({root:null,weight:function(e){return 1},directed:!1,alpha:0}),pn={degreeCentralityNormalized:function(e){e=dn(e);var t=this.cy(),n=this.nodes(),r=n.length;if(e.directed){for(var i={},a={},o=0,s=0,l=0;l<r;l++){var u=n[l],c=u.id();e.root=u;var h=this.degreeCentrality(e);o<h.indegree&&(o=h.indegree),s<h.outdegree&&(s=h.outdegree),i[c]=h.indegree,a[c]=h.outdegree}return{indegree:function(e){return 0==o?0:(f(e)&&(e=t.filter(e)),i[e.id()]/o)},outdegree:function(e){return 0===s?0:(f(e)&&(e=t.filter(e)),a[e.id()]/s)}}}for(var d={},p=0,g=0;g<r;g++){var v=n[g];e.root=v;var y=this.degreeCentrality(e);p<y.degree&&(p=y.degree),d[v.id()]=y.degree}return{degree:function(e){return 0===p?0:(f(e)&&(e=t.filter(e)),d[e.id()]/p)}}},degreeCentrality:function(e){e=dn(e);var t=this.cy(),n=this,r=e,i=r.root,a=r.weight,o=r.directed,s=r.alpha;if(i=t.collection(i)[0],o){for(var l=i.connectedEdges(),u=l.filter((function(e){return e.target().same(i)&&n.has(e)})),c=l.filter((function(e){return e.source().same(i)&&n.has(e)})),h=u.length,d=c.length,p=0,g=0,f=0;f<u.length;f++)p+=a(u[f]);for(var v=0;v<c.length;v++)g+=a(c[v]);return{indegree:Math.pow(h,1-s)*Math.pow(p,s),outdegree:Math.pow(d,1-s)*Math.pow(g,s)}}for(var y=i.connectedEdges().intersection(n),m=y.length,b=0,x=0;x<y.length;x++)b+=a(y[x]);return{degree:Math.pow(m,1-s)*Math.pow(b,s)}}};pn.dc=pn.degreeCentrality,pn.dcn=pn.degreeCentralityNormalised=pn.degreeCentralityNormalized;var gn=Ke({harmonic:!0,weight:function(){return 1},directed:!1,root:null}),fn={closenessCentralityNormalized:function(e){for(var t=gn(e),n=t.harmonic,r=t.weight,i=t.directed,a=this.cy(),o={},s=0,l=this.nodes(),u=this.floydWarshall({weight:r,directed:i}),c=0;c<l.length;c++){for(var h=0,d=l[c],p=0;p<l.length;p++)if(c!==p){var g=u.distance(d,l[p]);h+=n?1/g:g}n||(h=1/h),s<h&&(s=h),o[d.id()]=h}return{closeness:function(e){return 0==s?0:(e=f(e)?a.filter(e)[0].id():e.id(),o[e]/s)}}},closenessCentrality:function(e){var t=gn(e),n=t.root,r=t.weight,i=t.directed,a=t.harmonic;n=this.filter(n)[0];for(var o=this.dijkstra({root:n,weight:r,directed:i}),s=0,l=this.nodes(),u=0;u<l.length;u++){var c=l[u];if(!c.same(n)){var h=o.distanceTo(c);s+=a?1/h:h}}return a?s:1/s}};fn.cc=fn.closenessCentrality,fn.ccn=fn.closenessCentralityNormalised=fn.closenessCentralityNormalized;var vn=Ke({weight:null,directed:!1}),yn={betweennessCentrality:function(e){for(var t=vn(e),n=t.directed,r=t.weight,i=null!=r,a=this.cy(),o=this.nodes(),s={},l={},u=0,c={set:function(e,t){l[e]=t,t>u&&(u=t)},get:function(e){return l[e]}},h=0;h<o.length;h++){var d=o[h],p=d.id();s[p]=n?d.outgoers().nodes():d.openNeighborhood().nodes(),c.set(p,0)}for(var g=function(e){for(var t=o[e].id(),n=[],l={},u={},h={},d=new lt((function(e,t){return h[e]-h[t]})),p=0;p<o.length;p++){var g=o[p].id();l[g]=[],u[g]=0,h[g]=1/0}for(u[t]=1,h[t]=0,d.push(t);!d.empty();){var f=d.pop();if(n.push(f),i)for(var v=0;v<s[f].length;v++){var y,m=s[f][v],b=a.getElementById(f);y=b.edgesTo(m).length>0?b.edgesTo(m)[0]:m.edgesTo(b)[0];var x=r(y);m=m.id(),h[m]>h[f]+x&&(h[m]=h[f]+x,d.nodes.indexOf(m)<0?d.push(m):d.updateItem(m),u[m]=0,l[m]=[]),h[m]==h[f]+x&&(u[m]=u[m]+u[f],l[m].push(f))}else for(var w=0;w<s[f].length;w++){var E=s[f][w].id();h[E]==1/0&&(d.push(E),h[E]=h[f]+1),h[E]==h[f]+1&&(u[E]=u[E]+u[f],l[E].push(f))}}for(var _={},T=0;T<o.length;T++)_[o[T].id()]=0;for(;n.length>0;){for(var D=n.pop(),C=0;C<l[D].length;C++){var N=l[D][C];_[N]=_[N]+u[N]/u[D]*(1+_[D])}D!=o[e].id()&&c.set(D,c.get(D)+_[D])}},f=0;f<o.length;f++)g(f);var v={betweenness:function(e){var t=a.collection(e).id();return c.get(t)},betweennessNormalized:function(e){if(0==u)return 0;var t=a.collection(e).id();return c.get(t)/u}};return v.betweennessNormalised=v.betweennessNormalized,v}};yn.bc=yn.betweennessCentrality;var mn=Ke({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(e){return 1}]}),bn=function(e,t){for(var n=0,r=0;r<t.length;r++)n+=t[r](e);return n},xn=function(e,t){for(var n,r=0;r<t;r++){n=0;for(var i=0;i<t;i++)n+=e[i*t+r];for(var a=0;a<t;a++)e[a*t+r]=e[a*t+r]/n}},wn=function(e,t,n){for(var r=new Array(n*n),i=0;i<n;i++){for(var a=0;a<n;a++)r[i*n+a]=0;for(var o=0;o<n;o++)for(var s=0;s<n;s++)r[i*n+s]+=e[i*n+o]*t[o*n+s]}return r},En=function(e,t,n){for(var r=e.slice(0),i=1;i<n;i++)e=wn(e,r,t);return e},_n=function(e,t,n){for(var r=new Array(t*t),i=0;i<t*t;i++)r[i]=Math.pow(e[i],n);return xn(r,t),r},Tn=function(e,t,n,r){for(var i=0;i<n;i++)if(Math.round(e[i]*Math.pow(10,r))/Math.pow(10,r)!=Math.round(t[i]*Math.pow(10,r))/Math.pow(10,r))return!1;return!0},Dn=function(e,t){for(var n=0;n<e.length;n++)if(!t[n]||e[n].id()!==t[n].id())return!1;return!0},Cn=function(e){for(var t=this.nodes(),n=this.edges(),r=this.cy(),i=function(e){return mn(e)}(e),a={},o=0;o<t.length;o++)a[t[o].id()]=o;for(var s,l=t.length,u=l*l,c=new Array(u),h=0;h<u;h++)c[h]=0;for(var d=0;d<n.length;d++){var p=n[d],g=a[p.source().id()],f=a[p.target().id()],v=bn(p,i.attributes);c[g*l+f]+=v,c[f*l+g]+=v}(function(e,t,n){for(var r=0;r<t;r++)e[r*t+r]=n})(c,l,i.multFactor),xn(c,l);for(var y=!0,m=0;y&&m<i.maxIterations;)y=!1,s=En(c,l,i.expandFactor),c=_n(s,l,i.inflateFactor),Tn(c,s,u,4)||(y=!0),m++;var b=function(e,t,n,r){for(var i=[],a=0;a<t;a++){for(var o=[],s=0;s<t;s++)Math.round(1e3*e[a*t+s])/1e3>0&&o.push(n[s]);0!==o.length&&i.push(r.collection(o))}return i}(c,l,t,r);return function(e){for(var t=0;t<e.length;t++)for(var n=0;n<e.length;n++)t!=n&&Dn(e[t],e[n])&&e.splice(n,1);return e}(b)},Nn={markovClustering:Cn,mcl:Cn},An=function(e){return e},Ln=function(e,t){return Math.abs(t-e)},kn=function(e,t,n){return e+Ln(t,n)},Sn=function(e,t,n){return e+Math.pow(n-t,2)},In=function(e){return Math.sqrt(e)},Mn=function(e,t,n){return Math.max(e,Ln(t,n))},On=function(e,t,n,r,i){for(var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:An,o=r,s=0;s<e;s++)o=i(o,t(s),n(s));return a(o)},Pn={euclidean:function(e,t,n){return e>=2?On(e,t,n,0,Sn,In):On(e,t,n,0,kn)},squaredEuclidean:function(e,t,n){return On(e,t,n,0,Sn)},manhattan:function(e,t,n){return On(e,t,n,0,kn)},max:function(e,t,n){return On(e,t,n,-1/0,Mn)}};function Rn(e,t,n,r,i,a){var o;return o=v(e)?e:Pn[e]||Pn.euclidean,0===t&&v(e)?o(i,a):o(t,n,r,i,a)}Pn["squared-euclidean"]=Pn.squaredEuclidean,Pn.squaredeuclidean=Pn.squaredEuclidean;var Bn=Ke({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),Fn=function(e){return Bn(e)},zn=function(e,t,n,r,i){var a="kMedoids"!==i?function(e){return n[e]}:function(e){return r[e](n)},o=n,s=t;return Rn(e,r.length,a,(function(e){return r[e](t)}),o,s)},Gn=function(e,t,n){for(var r=n.length,i=new Array(r),a=new Array(r),o=new Array(t),s=null,l=0;l<r;l++)i[l]=e.min(n[l]).value,a[l]=e.max(n[l]).value;for(var u=0;u<t;u++){s=[];for(var c=0;c<r;c++)s[c]=Math.random()*(a[c]-i[c])+i[c];o[u]=s}return o},Yn=function(e,t,n,r,i){for(var a=1/0,o=0,s=0;s<t.length;s++){var l=zn(n,e,t[s],r,i);l<a&&(a=l,o=s)}return o},Xn=function(e,t,n){for(var r=[],i=null,a=0;a<t.length;a++)n[(i=t[a]).id()]===e&&r.push(i);return r},Vn=function(e,t,n){for(var r=0;r<e.length;r++)for(var i=0;i<e[r].length;i++)if(Math.abs(e[r][i]-t[r][i])>n)return!1;return!0},Un=function(e,t,n){for(var r=0;r<n;r++)if(e===t[r])return!0;return!1},jn=function(e,t){var n=new Array(t);if(e.length<50)for(var r=0;r<t;r++){for(var i=e[Math.floor(Math.random()*e.length)];Un(i,n,r);)i=e[Math.floor(Math.random()*e.length)];n[r]=i}else for(var a=0;a<t;a++)n[a]=e[Math.floor(Math.random()*e.length)];return n},qn=function(e,t,n){for(var r=0,i=0;i<t.length;i++)r+=zn("manhattan",t[i],e,n,"kMedoids");return r},Hn=function(e,t,n,r,i){for(var a,o,s=0;s<t.length;s++)for(var l=0;l<e.length;l++)r[s][l]=Math.pow(n[s][l],i.m);for(var u=0;u<e.length;u++)for(var c=0;c<i.attributes.length;c++){a=0,o=0;for(var h=0;h<t.length;h++)a+=r[h][u]*i.attributes[c](t[h]),o+=r[h][u];e[u][c]=a/o}},Wn=function(e,t,n,r,i){for(var a=0;a<e.length;a++)t[a]=e[a].slice();for(var o,s,l,u=2/(i.m-1),c=0;c<n.length;c++)for(var h=0;h<r.length;h++){o=0;for(var d=0;d<n.length;d++)s=zn(i.distance,r[h],n[c],i.attributes,"cmeans"),l=zn(i.distance,r[h],n[d],i.attributes,"cmeans"),o+=Math.pow(s/l,u);e[h][c]=1/o}},$n=function(e,t,n,r){for(var i,a,o=new Array(n.k),s=0;s<o.length;s++)o[s]=[];for(var l=0;l<t.length;l++){i=-1/0,a=-1;for(var u=0;u<t[0].length;u++)t[l][u]>i&&(i=t[l][u],a=u);o[a].push(e[l])}for(var c=0;c<o.length;c++)o[c]=r.collection(o[c]);return o},Kn=function(e){var t,n,r,i,a=this.cy(),o=this.nodes(),s=Fn(e);r=new Array(o.length);for(var l=0;l<o.length;l++)r[l]=new Array(s.k);n=new Array(o.length);for(var u=0;u<o.length;u++)n[u]=new Array(s.k);for(var c=0;c<o.length;c++){for(var h=0,d=0;d<s.k;d++)n[c][d]=Math.random(),h+=n[c][d];for(var p=0;p<s.k;p++)n[c][p]=n[c][p]/h}t=new Array(s.k);for(var g=0;g<s.k;g++)t[g]=new Array(s.attributes.length);i=new Array(o.length);for(var f=0;f<o.length;f++)i[f]=new Array(s.k);for(var v=!0,y=0;v&&y<s.maxIterations;)v=!1,Hn(t,o,n,i,s),Wn(n,r,t,o,s),Vn(n,r,s.sensitivityThreshold)||(v=!0),y++;return{clusters:$n(o,n,s,a),degreeOfMembership:n}},Zn={kMeans:function(t){var n,r,i,a,o=this.cy(),s=this.nodes(),l=null,u=Fn(t),c=new Array(u.k),h={};u.testMode?"number"==typeof u.testCentroids?(u.testCentroids,n=Gn(s,u.k,u.attributes)):n="object"===e(u.testCentroids)?u.testCentroids:Gn(s,u.k,u.attributes):n=Gn(s,u.k,u.attributes);for(var d=!0,p=0;d&&p<u.maxIterations;){for(var g=0;g<s.length;g++)h[(l=s[g]).id()]=Yn(l,n,u.distance,u.attributes,"kMeans");d=!1;for(var f=0;f<u.k;f++){var v=Xn(f,s,h);if(0!==v.length){for(var y=u.attributes.length,m=n[f],b=new Array(y),x=new Array(y),w=0;w<y;w++){x[w]=0;for(var E=0;E<v.length;E++)l=v[E],x[w]+=u.attributes[w](l);b[w]=x[w]/v.length,r=b[w],i=m[w],a=u.sensitivityThreshold,Math.abs(i-r)<=a||(d=!0)}n[f]=b,c[f]=o.collection(v)}}p++}return c},kMedoids:function(t){var n,r,i=this.cy(),a=this.nodes(),o=null,s=Fn(t),l=new Array(s.k),u={},c=new Array(s.k);s.testMode?"number"==typeof s.testCentroids||(n="object"===e(s.testCentroids)?s.testCentroids:jn(a,s.k)):n=jn(a,s.k);for(var h=!0,d=0;h&&d<s.maxIterations;){for(var p=0;p<a.length;p++)u[(o=a[p]).id()]=Yn(o,n,s.distance,s.attributes,"kMedoids");h=!1;for(var g=0;g<n.length;g++){var f=Xn(g,a,u);if(0!==f.length){c[g]=qn(n[g],f,s.attributes);for(var v=0;v<f.length;v++)(r=qn(f[v],f,s.attributes))<c[g]&&(c[g]=r,n[g]=f[v],h=!0);l[g]=i.collection(f)}}d++}return l},fuzzyCMeans:Kn,fcm:Kn},Qn=Ke({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),Jn={single:"min",complete:"max"},er=function(e,t,n,r,i){for(var a,o=0,s=1/0,l=i.attributes,u=function(e,t){return Rn(i.distance,l.length,(function(t){return l[t](e)}),(function(e){return l[e](t)}),e,t)},c=0;c<e.length;c++){var h=e[c].key,d=n[h][r[h]];d<s&&(o=h,s=d)}if("threshold"===i.mode&&s>=i.threshold||"dendrogram"===i.mode&&1===e.length)return!1;var p,g=t[o],f=t[r[o]];p="dendrogram"===i.mode?{left:g,right:f,key:g.key}:{value:g.value.concat(f.value),key:g.key},e[g.index]=p,e.splice(f.index,1),t[g.key]=p;for(var v=0;v<e.length;v++){var y=e[v];g.key===y.key?a=1/0:"min"===i.linkage?(a=n[g.key][y.key],n[g.key][y.key]>n[f.key][y.key]&&(a=n[f.key][y.key])):"max"===i.linkage?(a=n[g.key][y.key],n[g.key][y.key]<n[f.key][y.key]&&(a=n[f.key][y.key])):a="mean"===i.linkage?(n[g.key][y.key]*g.size+n[f.key][y.key]*f.size)/(g.size+f.size):"dendrogram"===i.mode?u(y.value,g.value):u(y.value[0],g.value[0]),n[g.key][y.key]=n[y.key][g.key]=a}for(var m=0;m<e.length;m++){var b=e[m].key;if(r[b]===g.key||r[b]===f.key){for(var x=b,w=0;w<e.length;w++){var E=e[w].key;n[b][E]<n[b][x]&&(x=E)}r[b]=x}e[m].index=m}return g.key=f.key=g.index=f.index=null,!0},tr=function e(t,n,r){t&&(t.value?n.push(t.value):(t.left&&e(t.left,n),t.right&&e(t.right,n)))},nr=function e(t,n){if(!t)return"";if(t.left&&t.right){var r=e(t.left,n),i=e(t.right,n),a=n.add({group:"nodes",data:{id:r+","+i}});return n.add({group:"edges",data:{source:r,target:a.id()}}),n.add({group:"edges",data:{source:i,target:a.id()}}),a.id()}return t.value?t.value.id():void 0},rr=function e(t,n,r){if(!t)return[];var i=[],a=[],o=[];return 0===n?(t.left&&tr(t.left,i),t.right&&tr(t.right,a),o=i.concat(a),[r.collection(o)]):1===n?t.value?[r.collection(t.value)]:(t.left&&tr(t.left,i),t.right&&tr(t.right,a),[r.collection(i),r.collection(a)]):t.value?[r.collection(t.value)]:(t.left&&(i=e(t.left,n-1,r)),t.right&&(a=e(t.right,n-1,r)),i.concat(a))},ir=function(e){for(var t=this.cy(),n=this.nodes(),r=function(e){var t=Qn(e),n=Jn[t.linkage];return null!=n&&(t.linkage=n),t}(e),i=r.attributes,a=function(e,t){return Rn(r.distance,i.length,(function(t){return i[t](e)}),(function(e){return i[e](t)}),e,t)},o=[],s=[],l=[],u=[],c=0;c<n.length;c++){var h={value:"dendrogram"===r.mode?n[c]:[n[c]],key:c,index:c};o[c]=h,u[c]=h,s[c]=[],l[c]=0}for(var d=0;d<o.length;d++)for(var p=0;p<=d;p++){var g;g="dendrogram"===r.mode?d===p?1/0:a(o[d].value,o[p].value):d===p?1/0:a(o[d].value[0],o[p].value[0]),s[d][p]=g,s[p][d]=g,g<s[d][l[d]]&&(l[d]=p)}for(var f,v=er(o,u,s,l,r);v;)v=er(o,u,s,l,r);return"dendrogram"===r.mode?(f=rr(o[0],r.dendrogramDepth,t),r.addDendrogram&&nr(o[0],t)):(f=new Array(o.length),o.forEach((function(e,n){e.key=e.index=null,f[n]=t.collection(e.value)}))),f},ar={hierarchicalClustering:ir,hca:ir},or=Ke({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),sr=function(e,t,n,r){var i=function(e,t){return r[t](e)};return-Rn(e,r.length,(function(e){return i(t,e)}),(function(e){return i(n,e)}),t,n)},lr=function(e,t){return"median"===t?function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],i=!(arguments.length>5&&void 0!==arguments[5])||arguments[5];arguments.length>3&&void 0!==arguments[3]&&!arguments[3]?(n<e.length&&e.splice(n,e.length-n),t>0&&e.splice(0,t)):e=e.slice(t,n);for(var a=0,o=e.length-1;o>=0;o--){var s=e[o];i?isFinite(s)||(e[o]=-1/0,a++):e.splice(o,1)}r&&e.sort((function(e,t){return e-t}));var l=e.length,u=Math.floor(l/2);return l%2!=0?e[u+1+a]:(e[u-1+a]+e[u+a])/2}(e):"mean"===t?function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=0,i=0,a=t;a<n;a++){var o=e[a];isFinite(o)&&(r+=o,i++)}return r/i}(e):"min"===t?function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=1/0,i=t;i<n;i++){var a=e[i];isFinite(a)&&(r=Math.min(a,r))}return r}(e):"max"===t?function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=-1/0,i=t;i<n;i++){var a=e[i];isFinite(a)&&(r=Math.max(a,r))}return r}(e):t},ur=function(e,t,n){for(var r=[],i=0;i<e;i++){for(var a=-1,o=-1/0,s=0;s<n.length;s++){var l=n[s];t[i*e+l]>o&&(a=l,o=t[i*e+l])}a>0&&r.push(a)}for(var u=0;u<n.length;u++)r[n[u]]=n[u];return r},cr=function(e){for(var t,n,r,i,a,o,s=this.cy(),l=this.nodes(),u=function(e){var t=e.damping,n=e.preference;.5<=t&&t<1||Ve("Damping must range on [0.5, 1).  Got: ".concat(t));var r=["median","mean","min","max"];return r.some((function(e){return e===n}))||b(n)||Ve("Preference must be one of [".concat(r.map((function(e){return"'".concat(e,"'")})).join(", "),"] or a number.  Got: ").concat(n)),or(e)}(e),c={},h=0;h<l.length;h++)c[l[h].id()]=h;n=(t=l.length)*t,r=new Array(n);for(var d=0;d<n;d++)r[d]=-1/0;for(var p=0;p<t;p++)for(var g=0;g<t;g++)p!==g&&(r[p*t+g]=sr(u.distance,l[p],l[g],u.attributes));i=lr(r,u.preference);for(var f=0;f<t;f++)r[f*t+f]=i;a=new Array(n);for(var v=0;v<n;v++)a[v]=0;o=new Array(n);for(var y=0;y<n;y++)o[y]=0;for(var m=new Array(t),x=new Array(t),w=new Array(t),E=0;E<t;E++)m[E]=0,x[E]=0,w[E]=0;for(var _,T=new Array(t*u.minIterations),D=0;D<T.length;D++)T[D]=0;for(_=0;_<u.maxIterations;_++){for(var C=0;C<t;C++){for(var N=-1/0,A=-1/0,L=-1,k=0,S=0;S<t;S++)m[S]=a[C*t+S],(k=o[C*t+S]+r[C*t+S])>=N?(A=N,N=k,L=S):k>A&&(A=k);for(var I=0;I<t;I++)a[C*t+I]=(1-u.damping)*(r[C*t+I]-N)+u.damping*m[I];a[C*t+L]=(1-u.damping)*(r[C*t+L]-A)+u.damping*m[L]}for(var M=0;M<t;M++){for(var O=0,P=0;P<t;P++)m[P]=o[P*t+M],x[P]=Math.max(0,a[P*t+M]),O+=x[P];O-=x[M],x[M]=a[M*t+M],O+=x[M];for(var R=0;R<t;R++)o[R*t+M]=(1-u.damping)*Math.min(0,O-x[R])+u.damping*m[R];o[M*t+M]=(1-u.damping)*(O-x[M])+u.damping*m[M]}for(var B=0,F=0;F<t;F++){var z=o[F*t+F]+a[F*t+F]>0?1:0;T[_%u.minIterations*t+F]=z,B+=z}if(B>0&&(_>=u.minIterations-1||_==u.maxIterations-1)){for(var G=0,Y=0;Y<t;Y++){w[Y]=0;for(var X=0;X<u.minIterations;X++)w[Y]+=T[X*t+Y];0!==w[Y]&&w[Y]!==u.minIterations||G++}if(G===t)break}}for(var V=function(e,t,n){for(var r=[],i=0;i<e;i++)t[i*e+i]+n[i*e+i]>0&&r.push(i);return r}(t,a,o),U=function(e,t,n){for(var r=ur(e,t,n),i=0;i<n.length;i++){for(var a=[],o=0;o<r.length;o++)r[o]===n[i]&&a.push(o);for(var s=-1,l=-1/0,u=0;u<a.length;u++){for(var c=0,h=0;h<a.length;h++)c+=t[a[h]*e+a[u]];c>l&&(s=u,l=c)}n[i]=a[s]}return ur(e,t,n)}(t,r,V),j={},q=0;q<V.length;q++)j[V[q]]=[];for(var H=0;H<l.length;H++){var W=U[c[l[H].id()]];null!=W&&j[W].push(l[H])}for(var $=new Array(V.length),K=0;K<V.length;K++)$[K]=s.collection(j[V[K]]);return $},hr={affinityPropagation:cr,ap:cr},dr=Ke({root:void 0,directed:!1}),pr={hierholzer:function(e){if(!m(e)){var t=arguments;e={root:t[0],directed:t[1]}}var n,r,i,a=dr(e),o=a.root,s=a.directed,l=this,u=!1;o&&(i=f(o)?this.filter(o)[0].id():o[0].id());var c={},h={};s?l.forEach((function(e){var t=e.id();if(e.isNode()){var i=e.indegree(!0),a=e.outdegree(!0),o=i-a,s=a-i;1==o?n?u=!0:n=t:1==s?r?u=!0:r=t:(s>1||o>1)&&(u=!0),c[t]=[],e.outgoers().forEach((function(e){e.isEdge()&&c[t].push(e.id())}))}else h[t]=[void 0,e.target().id()]})):l.forEach((function(e){var t=e.id();e.isNode()?(e.degree(!0)%2&&(n?r?u=!0:r=t:n=t),c[t]=[],e.connectedEdges().forEach((function(e){return c[t].push(e.id())}))):h[t]=[e.source().id(),e.target().id()]}));var d={found:!1,trail:void 0};if(u)return d;if(r&&n)if(s){if(i&&r!=i)return d;i=r}else{if(i&&r!=i&&n!=i)return d;i||(i=r)}else i||(i=l[0].id());var p=function(e){for(var t,n,r,i=e,a=[e];c[i].length;)t=c[i].shift(),n=h[t][0],i!=(r=h[t][1])?(c[r]=c[r].filter((function(e){return e!=t})),i=r):s||i==n||(c[n]=c[n].filter((function(e){return e!=t})),i=n),a.unshift(t),a.unshift(i);return a},g=[],v=[];for(v=p(i);1!=v.length;)0==c[v[0]].length?(g.unshift(l.getElementById(v.shift())),g.unshift(l.getElementById(v.shift()))):v=p(v.shift()).concat(v);for(var y in g.unshift(l.getElementById(v.shift())),c)if(c[y].length)return d;return d.found=!0,d.trail=this.spawn(g,!0),d}},gr=function(){var e=this,t={},n=0,r=0,i=[],a=[],o={},s=function s(l,u,c){l===c&&(r+=1),t[u]={id:n,low:n++,cutVertex:!1};var h,d,p,g,f=e.getElementById(u).connectedEdges().intersection(e);0===f.size()?i.push(e.spawn(e.getElementById(u))):f.forEach((function(n){h=n.source().id(),d=n.target().id(),(p=h===u?d:h)!==c&&(g=n.id(),o[g]||(o[g]=!0,a.push({x:u,y:p,edge:n})),p in t?t[u].low=Math.min(t[u].low,t[p].id):(s(l,p,u),t[u].low=Math.min(t[u].low,t[p].low),t[u].id<=t[p].low&&(t[u].cutVertex=!0,function(n,r){for(var o=a.length-1,s=[],l=e.spawn();a[o].x!=n||a[o].y!=r;)s.push(a.pop().edge),o--;s.push(a.pop().edge),s.forEach((function(n){var r=n.connectedNodes().intersection(e);l.merge(n),r.forEach((function(n){var r=n.id(),i=n.connectedEdges().intersection(e);l.merge(n),t[r].cutVertex?l.merge(i.filter((function(e){return e.isLoop()}))):l.merge(i)}))})),i.push(l)}(u,p))))}))};e.forEach((function(e){if(e.isNode()){var n=e.id();n in t||(r=0,s(n,n),t[n].cutVertex=r>1)}}));var l=Object.keys(t).filter((function(e){return t[e].cutVertex})).map((function(t){return e.getElementById(t)}));return{cut:e.spawn(l),components:i}},fr=function(){var e=this,t={},n=0,r=[],i=[],a=e.spawn(e),o=function o(s){if(i.push(s),t[s]={index:n,low:n++,explored:!1},e.getElementById(s).connectedEdges().intersection(e).forEach((function(e){var n=e.target().id();n!==s&&(n in t||o(n),t[n].explored||(t[s].low=Math.min(t[s].low,t[n].low)))})),t[s].index===t[s].low){for(var l=e.spawn();;){var u=i.pop();if(l.merge(e.getElementById(u)),t[u].low=t[s].index,t[u].explored=!0,u===s)break}var c=l.edgesWith(l),h=l.merge(c);r.push(h),a=a.difference(h)}};return e.forEach((function(e){if(e.isNode()){var n=e.id();n in t||o(n)}})),{cut:a,components:r}},vr={};[ot,ct,ht,pt,ft,yt,wt,hn,pn,fn,yn,Nn,Zn,ar,hr,pr,{hopcroftTarjanBiconnected:gr,htbc:gr,htb:gr,hopcroftTarjanBiconnectedComponents:gr},{tarjanStronglyConnected:fr,tsc:fr,tscc:fr,tarjanStronglyConnectedComponents:fr}].forEach((function(e){z(vr,e)}));var yr=function e(t){if(!(this instanceof e))return new e(t);this.id="Thenable/1.0.7",this.state=0,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},"function"==typeof t&&t.call(this,this.fulfill.bind(this),this.reject.bind(this))};yr.prototype={fulfill:function(e){return mr(this,1,"fulfillValue",e)},reject:function(e){return mr(this,2,"rejectReason",e)},then:function(e,t){var n=this,r=new yr;return n.onFulfilled.push(wr(e,r,"fulfill")),n.onRejected.push(wr(t,r,"reject")),br(n),r.proxy}};var mr=function(e,t,n,r){return 0===e.state&&(e.state=t,e[n]=r,br(e)),e},br=function(e){1===e.state?xr(e,"onFulfilled",e.fulfillValue):2===e.state&&xr(e,"onRejected",e.rejectReason)},xr=function(e,t,n){if(0!==e[t].length){var r=e[t];e[t]=[];var i=function(){for(var e=0;e<r.length;e++)r[e](n)};"function"==typeof setImmediate?setImmediate(i):setTimeout(i,0)}},wr=function(e,t,n){return function(r){if("function"!=typeof e)t[n].call(t,r);else{var i;try{i=e(r)}catch(e){return void t.reject(e)}Er(t,i)}}},Er=function t(n,r){if(n!==r&&n.proxy!==r){var i;if("object"===e(r)&&null!==r||"function"==typeof r)try{i=r.then}catch(e){return void n.reject(e)}if("function"!=typeof i)n.fulfill(r);else{var a=!1;try{i.call(r,(function(e){a||(a=!0,e===r?n.reject(new TypeError("circular thenable chain")):t(n,e))}),(function(e){a||(a=!0,n.reject(e))}))}catch(e){a||n.reject(e)}}}else n.reject(new TypeError("cannot resolve promise with itself"))};yr.all=function(e){return new yr((function(t,n){for(var r=new Array(e.length),i=0,a=function(n,a){r[n]=a,++i===e.length&&t(r)},o=0;o<e.length;o++)!function(t){var r=e[t];null!=r&&null!=r.then?r.then((function(e){a(t,e)}),(function(e){n(e)})):a(t,r)}(o)}))},yr.resolve=function(e){return new yr((function(t,n){t(e)}))},yr.reject=function(e){return new yr((function(t,n){n(e)}))};var _r="undefined"!=typeof Promise?Promise:yr,Tr=function(e,t,n){var r=T(e),i=!r,a=this._private=z({duration:1e3},t,n);if(a.target=e,a.style=a.style||a.css,a.started=!1,a.playing=!1,a.hooked=!1,a.applying=!1,a.progress=0,a.completes=[],a.frames=[],a.complete&&v(a.complete)&&a.completes.push(a.complete),i){var o=e.position();a.startPosition=a.startPosition||{x:o.x,y:o.y},a.startStyle=a.startStyle||e.cy().style().getAnimationStartStyle(e,a.style)}if(r){var s=e.pan();a.startPan={x:s.x,y:s.y},a.startZoom=e.zoom()}this.length=1,this[0]=this},Dr=Tr.prototype;z(Dr,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var t=e.target._private.animation;(e.queue?t.queue:t.current).push(this),w(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return 1===e.progress&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var t=this._private;return void 0===e?t.progress*t.duration:this.progress(e/t.duration)},progress:function(e){var t=this._private,n=t.playing;return void 0===e?t.progress:(n&&this.pause(),t.progress=e,t.started=!1,n&&this.play(),this)},completed:function(){return 1===this._private.progress},reverse:function(){var e=this._private,t=e.playing;t&&this.pause(),e.progress=1-e.progress,e.started=!1;var n=function(t,n){var r=e[t];null!=r&&(e[t]=e[n],e[n]=r)};if(n("zoom","startZoom"),n("pan","startPan"),n("position","startPosition"),e.style)for(var r=0;r<e.style.length;r++){var i=e.style[r],a=i.name,o=e.startStyle[a];e.startStyle[a]=i,e.style[r]=o}return t&&this.play(),this},promise:function(e){var t,n=this._private;return t="frame"===e?n.frames:n.completes,new _r((function(e,n){t.push((function(){e()}))}))}}),Dr.complete=Dr.completed,Dr.run=Dr.play,Dr.running=Dr.playing;var Cr={animated:function(){return function(){var e=this,t=void 0!==e.length?e:[e];if(!(this._private.cy||this).styleEnabled())return!1;var n=t[0];return n?n._private.animation.current.length>0:void 0}},clearQueue:function(){return function(){var e=this,t=void 0!==e.length?e:[e];if(!(this._private.cy||this).styleEnabled())return this;for(var n=0;n<t.length;n++)t[n]._private.animation.queue=[];return this}},delay:function(){return function(e,t){return(this._private.cy||this).styleEnabled()?this.animate({delay:e,duration:e,complete:t}):this}},delayAnimation:function(){return function(e,t){return(this._private.cy||this).styleEnabled()?this.animation({delay:e,duration:e,complete:t}):this}},animation:function(){return function(e,t){var n=this,r=void 0!==n.length,i=r?n:[n],a=this._private.cy||this,o=!r,s=!o;if(!a.styleEnabled())return this;var l=a.style();if(e=z({},e,t),0===Object.keys(e).length)return new Tr(i[0],e);switch(void 0===e.duration&&(e.duration=400),e.duration){case"slow":e.duration=600;break;case"fast":e.duration=200}if(s&&(e.style=l.getPropsList(e.style||e.css),e.css=void 0),s&&null!=e.renderedPosition){var u=e.renderedPosition,c=a.pan(),h=a.zoom();e.position=_t(u,h,c)}if(o&&null!=e.panBy){var d=e.panBy,p=a.pan();e.pan={x:p.x+d.x,y:p.y+d.y}}var g=e.center||e.centre;if(o&&null!=g){var f=a.getCenterPan(g.eles,e.zoom);null!=f&&(e.pan=f)}if(o&&null!=e.fit){var v=e.fit,y=a.getFitViewport(v.eles||v.boundingBox,v.padding);null!=y&&(e.pan=y.pan,e.zoom=y.zoom)}if(o&&m(e.zoom)){var b=a.getZoomedViewport(e.zoom);null!=b?(b.zoomed&&(e.zoom=b.zoom),b.panned&&(e.pan=b.pan)):e.zoom=null}return new Tr(i[0],e)}},animate:function(){return function(e,t){var n=this,r=void 0!==n.length?n:[n];if(!(this._private.cy||this).styleEnabled())return this;t&&(e=z({},e,t));for(var i=0;i<r.length;i++){var a=r[i],o=a.animated()&&(void 0===e.queue||e.queue);a.animation(e,o?{queue:!0}:void 0).play()}return this}},stop:function(){return function(e,t){var n=this,r=void 0!==n.length?n:[n],i=this._private.cy||this;if(!i.styleEnabled())return this;for(var a=0;a<r.length;a++){for(var o=r[a]._private,s=o.animation.current,l=0;l<s.length;l++){var u=s[l]._private;t&&(u.duration=0)}e&&(o.animation.queue=[]),t||(o.animation.current=[])}return i.notify("draw"),this}}},Nr=Array.isArray,Ar=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Lr=/^\w*$/;var kr=function(e,t){if(Nr(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!he(e))||Lr.test(e)||!Ar.test(e)||null!=t&&e in Object(t)};var Sr,Ir=function(e){if(!U(e))return!1;var t=ue(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},Mr=W["__core-js_shared__"],Or=(Sr=/[^.]+$/.exec(Mr&&Mr.keys&&Mr.keys.IE_PROTO||""))?"Symbol(src)_1."+Sr:"";var Pr=function(e){return!!Or&&Or in e},Rr=Function.prototype.toString;var Br=function(e){if(null!=e){try{return Rr.call(e)}catch(e){}try{return e+""}catch(e){}}return""},Fr=/^\[object .+?Constructor\]$/,zr=Function.prototype,Gr=Object.prototype,Yr=zr.toString,Xr=Gr.hasOwnProperty,Vr=RegExp("^"+Yr.call(Xr).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var Ur=function(e){return!(!U(e)||Pr(e))&&(Ir(e)?Vr:Fr).test(Br(e))};var jr=function(e,t){return null==e?void 0:e[t]};var qr=function(e,t){var n=jr(e,t);return Ur(n)?n:void 0},Hr=qr(Object,"create");var Wr=function(){this.__data__=Hr?Hr(null):{},this.size=0};var $r=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Kr=Object.prototype.hasOwnProperty;var Zr=function(e){var t=this.__data__;if(Hr){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return Kr.call(t,e)?t[e]:void 0},Qr=Object.prototype.hasOwnProperty;var Jr=function(e){var t=this.__data__;return Hr?void 0!==t[e]:Qr.call(t,e)};var ei=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Hr&&void 0===t?"__lodash_hash_undefined__":t,this};function ti(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}ti.prototype.clear=Wr,ti.prototype.delete=$r,ti.prototype.get=Zr,ti.prototype.has=Jr,ti.prototype.set=ei;var ni=ti;var ri=function(){this.__data__=[],this.size=0};var ii=function(e,t){return e===t||e!=e&&t!=t};var ai=function(e,t){for(var n=e.length;n--;)if(ii(e[n][0],t))return n;return-1},oi=Array.prototype.splice;var si=function(e){var t=this.__data__,n=ai(t,e);return!(n<0||(n==t.length-1?t.pop():oi.call(t,n,1),--this.size,0))};var li=function(e){var t=this.__data__,n=ai(t,e);return n<0?void 0:t[n][1]};var ui=function(e){return ai(this.__data__,e)>-1};var ci=function(e,t){var n=this.__data__,r=ai(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};function hi(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}hi.prototype.clear=ri,hi.prototype.delete=si,hi.prototype.get=li,hi.prototype.has=ui,hi.prototype.set=ci;var di=hi,pi=qr(W,"Map");var gi=function(){this.size=0,this.__data__={hash:new ni,map:new(pi||di),string:new ni}};var fi=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var vi=function(e,t){var n=e.__data__;return fi(t)?n["string"==typeof t?"string":"hash"]:n.map};var yi=function(e){var t=vi(this,e).delete(e);return this.size-=t?1:0,t};var mi=function(e){return vi(this,e).get(e)};var bi=function(e){return vi(this,e).has(e)};var xi=function(e,t){var n=vi(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};function wi(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}wi.prototype.clear=gi,wi.prototype.delete=yi,wi.prototype.get=mi,wi.prototype.has=bi,wi.prototype.set=xi;var Ei=wi;function _i(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],a=n.cache;if(a.has(i))return a.get(i);var o=e.apply(this,r);return n.cache=a.set(i,o)||a,o};return n.cache=new(_i.Cache||Ei),n}_i.Cache=Ei;var Ti=_i;var Di=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Ci=/\\(\\)?/g,Ni=function(e){var t=Ti(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Di,(function(e,n,r,i){t.push(r?i.replace(Ci,"$1"):n||e)})),t}));var Ai=function(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i},Li=ee?ee.prototype:void 0,ki=Li?Li.toString:void 0;var Si=function e(t){if("string"==typeof t)return t;if(Nr(t))return Ai(t,e)+"";if(he(t))return ki?ki.call(t):"";var n=t+"";return"0"==n&&1/t==-Infinity?"-0":n};var Ii=function(e){return null==e?"":Si(e)};var Mi=function(e,t){return Nr(e)?e:kr(e,t)?[e]:Ni(Ii(e))};var Oi=function(e){if("string"==typeof e||he(e))return e;var t=e+"";return"0"==t&&1/e==-Infinity?"-0":t};var Pi=function(e,t){for(var n=0,r=(t=Mi(t,e)).length;null!=e&&n<r;)e=e[Oi(t[n++])];return n&&n==r?e:void 0};var Ri=function(e,t,n){var r=null==e?void 0:Pi(e,t);return void 0===r?n:r},Bi=function(){try{var e=qr(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();var Fi=function(e,t,n){"__proto__"==t&&Bi?Bi(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n},zi=Object.prototype.hasOwnProperty;var Gi=function(e,t,n){var r=e[t];zi.call(e,t)&&ii(r,n)&&(void 0!==n||t in e)||Fi(e,t,n)},Yi=/^(?:0|[1-9]\d*)$/;var Xi=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&Yi.test(e))&&e>-1&&e%1==0&&e<t};var Vi=function(e,t,n,r){if(!U(e))return e;for(var i=-1,a=(t=Mi(t,e)).length,o=a-1,s=e;null!=s&&++i<a;){var l=Oi(t[i]),u=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return e;if(i!=o){var c=s[l];void 0===(u=r?r(c,l,s):void 0)&&(u=U(c)?c:Xi(t[i+1])?[]:{})}Gi(s,l,u),s=s[l]}return e};var Ui=function(e,t,n){return null==e?e:Vi(e,t,n)};var ji=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t};var qi=function(e){return Nr(e)?Ai(e,Oi):he(e)?[e]:ji(Ni(Ii(e)))},Hi={eventAliasesOn:function(e){var t=e;t.addListener=t.listen=t.bind=t.on,t.unlisten=t.unbind=t.off=t.removeListener,t.trigger=t.emit,t.pon=t.promiseOn=function(e,t){var n=this,r=Array.prototype.slice.call(arguments,0);return new _r((function(e,t){var i=r.concat([function(t){n.off.apply(n,a),e(t)}]),a=i.concat([]);n.on.apply(n,i)}))}}},Wi={};[Cr,{data:function(e){return e=z({},{field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(e){},beforeSet:function(e,t){},onSet:function(e){},canSet:function(e){return!0}},e),function(t,n){var r=e,i=this,o=void 0!==i.length,s=o?i:[i],l=o?i[0]:i;if(f(t)){var u,c=-1!==t.indexOf(".")&&qi(t);if(r.allowGetting&&void 0===n)return l&&(r.beforeGet(l),u=c&&void 0===l._private[r.field][t]?Ri(l._private[r.field],c):l._private[r.field][t]),u;if(r.allowSetting&&void 0!==n&&!r.immutableKeys[t]){var h=a({},t,n);r.beforeSet(i,h);for(var d=0,p=s.length;d<p;d++){var g=s[d];r.canSet(g)&&(c&&void 0===l._private[r.field][t]?Ui(g._private[r.field],c,n):g._private[r.field][t]=n)}r.updateStyle&&i.updateStyle(),r.onSet(i),r.settingTriggersEvent&&i[r.triggerFnName](r.settingEvent)}}else if(r.allowSetting&&m(t)){var y,b,x=t,w=Object.keys(x);r.beforeSet(i,x);for(var E=0;E<w.length;E++)if(b=x[y=w[E]],!r.immutableKeys[y])for(var _=0;_<s.length;_++){var T=s[_];r.canSet(T)&&(T._private[r.field][y]=b)}r.updateStyle&&i.updateStyle(),r.onSet(i),r.settingTriggersEvent&&i[r.triggerFnName](r.settingEvent)}else if(r.allowBinding&&v(t)){var D=t;i.on(r.bindingEvent,D)}else if(r.allowGetting&&void 0===t){var C;return l&&(r.beforeGet(l),C=l._private[r.field]),C}return i}},removeData:function(e){return e=z({},{field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}},e),function(t){var n=e,r=this,i=void 0!==r.length?r:[r];if(f(t)){for(var a=t.split(/\s+/),o=a.length,s=0;s<o;s++){var l=a[s];if(!C(l)&&!n.immutableKeys[l])for(var u=0,c=i.length;u<c;u++)i[u]._private[n.field][l]=void 0}n.triggerEvent&&r[n.triggerFnName](n.event)}else if(void 0===t){for(var h=0,d=i.length;h<d;h++)for(var p=i[h]._private[n.field],g=Object.keys(p),v=0;v<g.length;v++){var y=g[v];!n.immutableKeys[y]&&(p[y]=void 0)}n.triggerEvent&&r[n.triggerFnName](n.event)}return r}}},Hi].forEach((function(e){z(Wi,e)}));var $i={animate:Wi.animate(),animation:Wi.animation(),animated:Wi.animated(),clearQueue:Wi.clearQueue(),delay:Wi.delay(),delayAnimation:Wi.delayAnimation(),stop:Wi.stop()},Ki={classes:function(e){var t=this;if(void 0===e){var n=[];return t[0]._private.classes.forEach((function(e){return n.push(e)})),n}y(e)||(e=(e||"").match(/\S+/g)||[]);for(var r=[],i=new rt(e),a=0;a<t.length;a++){for(var o=t[a],s=o._private,l=s.classes,u=!1,c=0;c<e.length;c++){var h=e[c];if(!l.has(h)){u=!0;break}}u||(u=l.size!==e.length),u&&(s.classes=i,r.push(o))}return r.length>0&&this.spawn(r).updateStyle().emit("class"),t},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var t=this[0];return null!=t&&t._private.classes.has(e)},toggleClass:function(e,t){y(e)||(e=e.match(/\S+/g)||[]);for(var n=this,r=void 0===t,i=[],a=0,o=n.length;a<o;a++)for(var s=n[a],l=s._private.classes,u=!1,c=0;c<e.length;c++){var h=e[c],d=l.has(h),p=!1;t||r&&!d?(l.add(h),p=!0):(!t||r&&d)&&(l.delete(h),p=!0),!u&&p&&(i.push(s),u=!0)}return i.length>0&&this.spawn(i).updateStyle().emit("class"),n},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,t){var n=this;if(null==t)t=250;else if(0===t)return n;return n.addClass(e),setTimeout((function(){n.removeClass(e)}),t),n}};Ki.className=Ki.classNames=Ki.classes;var Zi={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:"\"(?:\\\\\"|[^\"])*\"|'(?:\\\\'|[^'])*'",number:M,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};Zi.variable="(?:[\\w-.]|(?:\\\\"+Zi.metaChar+"))+",Zi.className="(?:[\\w-]|(?:\\\\"+Zi.metaChar+"))+",Zi.value=Zi.string+"|"+Zi.number,Zi.id=Zi.variable,function(){var e,t,n;for(e=Zi.comparatorOp.split("|"),n=0;n<e.length;n++)t=e[n],Zi.comparatorOp+="|@"+t;for(e=Zi.comparatorOp.split("|"),n=0;n<e.length;n++)(t=e[n]).indexOf("!")>=0||"="!==t&&(Zi.comparatorOp+="|\\!"+t)}();var Qi=0,Ji=1,ea=2,ta=3,na=4,ra=5,ia=6,aa=7,oa=8,sa=9,la=10,ua=11,ca=12,ha=13,da=14,pa=15,ga=16,fa=17,va=18,ya=19,ma=20,ba=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":compound",matches:function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort((function(e,t){return function(e,t){return-1*F(e,t)}(e.selector,t.selector)})),xa=function(){for(var e,t={},n=0;n<ba.length;n++)t[(e=ba[n]).selector]=e.matches;return t}(),wa="("+ba.map((function(e){return e.selector})).join("|")+")",Ea=function(e){return e.replace(new RegExp("\\\\("+Zi.metaChar+")","g"),(function(e,t){return t}))},_a=function(e,t,n){e[e.length-1]=n},Ta=[{name:"group",query:!0,regex:"("+Zi.group+")",populate:function(e,t,n){var r=o(n,1)[0];t.checks.push({type:Qi,value:"*"===r?r:r+"s"})}},{name:"state",query:!0,regex:wa,populate:function(e,t,n){var r=o(n,1)[0];t.checks.push({type:aa,value:r})}},{name:"id",query:!0,regex:"\\#("+Zi.id+")",populate:function(e,t,n){var r=o(n,1)[0];t.checks.push({type:oa,value:Ea(r)})}},{name:"className",query:!0,regex:"\\.("+Zi.className+")",populate:function(e,t,n){var r=o(n,1)[0];t.checks.push({type:sa,value:Ea(r)})}},{name:"dataExists",query:!0,regex:"\\[\\s*("+Zi.variable+")\\s*\\]",populate:function(e,t,n){var r=o(n,1)[0];t.checks.push({type:na,field:Ea(r)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+Zi.variable+")\\s*("+Zi.comparatorOp+")\\s*("+Zi.value+")\\s*\\]",populate:function(e,t,n){var r=o(n,3),i=r[0],a=r[1],s=r[2];s=null!=new RegExp("^"+Zi.string+"$").exec(s)?s.substring(1,s.length-1):parseFloat(s),t.checks.push({type:ta,field:Ea(i),operator:a,value:s})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+Zi.boolOp+")\\s*("+Zi.variable+")\\s*\\]",populate:function(e,t,n){var r=o(n,2),i=r[0],a=r[1];t.checks.push({type:ra,field:Ea(a),operator:i})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+Zi.meta+")\\s*("+Zi.comparatorOp+")\\s*("+Zi.number+")\\s*\\]\\]",populate:function(e,t,n){var r=o(n,3),i=r[0],a=r[1],s=r[2];t.checks.push({type:ia,field:Ea(i),operator:a,value:parseFloat(s)})}},{name:"nextQuery",separator:!0,regex:Zi.separator,populate:function(e,t){var n=e.currentSubject,r=e.edgeCount,i=e.compoundCount,a=e[e.length-1];return null!=n&&(a.subject=n,e.currentSubject=null),a.edgeCount=r,a.compoundCount=i,e.edgeCount=0,e.compoundCount=0,e[e.length++]={checks:[]}}},{name:"directedEdge",separator:!0,regex:Zi.directedEdge,populate:function(e,t){if(null==e.currentSubject){var n={checks:[]},r=t,i={checks:[]};return n.checks.push({type:ua,source:r,target:i}),_a(e,0,n),e.edgeCount++,i}var a={checks:[]},o=t,s={checks:[]};return a.checks.push({type:ca,source:o,target:s}),_a(e,0,a),e.edgeCount++,s}},{name:"undirectedEdge",separator:!0,regex:Zi.undirectedEdge,populate:function(e,t){if(null==e.currentSubject){var n={checks:[]},r=t,i={checks:[]};return n.checks.push({type:la,nodes:[r,i]}),_a(e,0,n),e.edgeCount++,i}var a={checks:[]},o=t,s={checks:[]};return a.checks.push({type:da,node:o,neighbor:s}),_a(e,0,a),s}},{name:"child",separator:!0,regex:Zi.child,populate:function(e,t){if(null==e.currentSubject){var n={checks:[]},r={checks:[]},i=e[e.length-1];return n.checks.push({type:pa,parent:i,child:r}),_a(e,0,n),e.compoundCount++,r}if(e.currentSubject===t){var a={checks:[]},o=e[e.length-1],s={checks:[]},l={checks:[]},u={checks:[]},c={checks:[]};return a.checks.push({type:ya,left:o,right:s,subject:l}),l.checks=t.checks,t.checks=[{type:ma}],c.checks.push({type:ma}),s.checks.push({type:fa,parent:c,child:u}),_a(e,0,a),e.currentSubject=l,e.compoundCount++,u}var h={checks:[]},d={checks:[]},p=[{type:fa,parent:h,child:d}];return h.checks=t.checks,t.checks=p,e.compoundCount++,d}},{name:"descendant",separator:!0,regex:Zi.descendant,populate:function(e,t){if(null==e.currentSubject){var n={checks:[]},r={checks:[]},i=e[e.length-1];return n.checks.push({type:ga,ancestor:i,descendant:r}),_a(e,0,n),e.compoundCount++,r}if(e.currentSubject===t){var a={checks:[]},o=e[e.length-1],s={checks:[]},l={checks:[]},u={checks:[]},c={checks:[]};return a.checks.push({type:ya,left:o,right:s,subject:l}),l.checks=t.checks,t.checks=[{type:ma}],c.checks.push({type:ma}),s.checks.push({type:va,ancestor:c,descendant:u}),_a(e,0,a),e.currentSubject=l,e.compoundCount++,u}var h={checks:[]},d={checks:[]},p=[{type:va,ancestor:h,descendant:d}];return h.checks=t.checks,t.checks=p,e.compoundCount++,d}},{name:"subject",modifier:!0,regex:Zi.subject,populate:function(e,t){if(null!=e.currentSubject&&e.currentSubject!==t)return je("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=t;var n=e[e.length-1].checks[0],r=null==n?null:n.type;r===ua?n.type=ha:r===la&&(n.type=da,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)}}];Ta.forEach((function(e){return e.regexObj=new RegExp("^"+e.regex)}));var Da=function(e){for(var t,n,r,i=0;i<Ta.length;i++){var a=Ta[i],o=a.name,s=e.match(a.regexObj);if(null!=s){n=s,t=a,r=o;var l=s[0];e=e.substring(l.length);break}}return{expr:t,match:n,name:r,remaining:e}},Ca={parse:function(e){var t=this,n=t.inputText=e,r=t[0]={checks:[]};for(t.length=1,n=function(e){var t=e.match(/^\s+/);if(t){var n=t[0];e=e.substring(n.length)}return e}(n);;){var i=Da(n);if(null==i.expr)return je("The selector `"+e+"`is invalid"),!1;var a=i.match.slice(1),o=i.expr.populate(t,r,a);if(!1===o)return!1;if(null!=o&&(r=o),(n=i.remaining).match(/^\s*$/))break}var s=t[t.length-1];null!=t.currentSubject&&(s.subject=t.currentSubject),s.edgeCount=t.edgeCount,s.compoundCount=t.compoundCount;for(var l=0;l<t.length;l++){var u=t[l];if(u.compoundCount>0&&u.edgeCount>0)return je("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(u.edgeCount>1)return je("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;1===u.edgeCount&&je("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},toString:function(){if(null!=this.toStringCache)return this.toStringCache;for(var e=function(e){return null==e?"":e},t=function(t){return f(t)?'"'+t+'"':e(t)},n=function(e){return" "+e+" "},r=function(i,a){return i.checks.reduce((function(o,s,l){return o+(a===i&&0===l?"$":"")+function(i,a){var o=i.type,s=i.value;switch(o){case Qi:var l=e(s);return l.substring(0,l.length-1);case ta:var u=i.field,c=i.operator;return"["+u+n(e(c))+t(s)+"]";case ra:var h=i.operator,d=i.field;return"["+e(h)+d+"]";case na:return"["+i.field+"]";case ia:var p=i.operator;return"[["+i.field+n(e(p))+t(s)+"]]";case aa:return s;case oa:return"#"+s;case sa:return"."+s;case fa:case pa:return r(i.parent,a)+n(">")+r(i.child,a);case va:case ga:return r(i.ancestor,a)+" "+r(i.descendant,a);case ya:var g=r(i.left,a),f=r(i.subject,a),v=r(i.right,a);return g+(g.length>0?" ":"")+f+v;case ma:return""}}(s,a)}),"")},i="",a=0;a<this.length;a++){var o=this[a];i+=r(o,o.subject),this.length>1&&a<this.length-1&&(i+=", ")}return this.toStringCache=i,i}},Na=function(e,t,n){var r,i,a,o=f(e),s=b(e),l=f(n),u=!1,c=!1,h=!1;switch(t.indexOf("!")>=0&&(t=t.replace("!",""),c=!0),t.indexOf("@")>=0&&(t=t.replace("@",""),u=!0),(o||l||u)&&(i=o||s?""+e:"",a=""+n),u&&(e=i=i.toLowerCase(),n=a=a.toLowerCase()),t){case"*=":r=i.indexOf(a)>=0;break;case"$=":r=i.indexOf(a,i.length-a.length)>=0;break;case"^=":r=0===i.indexOf(a);break;case"=":r=e===n;break;case">":h=!0,r=e>n;break;case">=":h=!0,r=e>=n;break;case"<":h=!0,r=e<n;break;case"<=":h=!0,r=e<=n;break;default:r=!1}return!c||null==e&&h||(r=!r),r},Aa=function(e,t){return e.data(t)},La=[],ka=function(e,t){return e.checks.every((function(e){return La[e.type](e,t)}))};La[Qi]=function(e,t){var n=e.value;return"*"===n||n===t.group()},La[aa]=function(e,t){return function(e,t){return xa[e](t)}(e.value,t)},La[oa]=function(e,t){var n=e.value;return t.id()===n},La[sa]=function(e,t){var n=e.value;return t.hasClass(n)},La[ia]=function(e,t){var n=e.field,r=e.operator,i=e.value;return Na(function(e,t){return e[t]()}(t,n),r,i)},La[ta]=function(e,t){var n=e.field,r=e.operator,i=e.value;return Na(Aa(t,n),r,i)},La[ra]=function(e,t){var n=e.field,r=e.operator;return function(e,t){switch(t){case"?":return!!e;case"!":return!e;case"^":return void 0===e}}(Aa(t,n),r)},La[na]=function(e,t){var n=e.field;return e.operator,void 0!==Aa(t,n)},La[la]=function(e,t){var n=e.nodes[0],r=e.nodes[1],i=t.source(),a=t.target();return ka(n,i)&&ka(r,a)||ka(r,i)&&ka(n,a)},La[da]=function(e,t){return ka(e.node,t)&&t.neighborhood().some((function(t){return t.isNode()&&ka(e.neighbor,t)}))},La[ua]=function(e,t){return ka(e.source,t.source())&&ka(e.target,t.target())},La[ca]=function(e,t){return ka(e.source,t)&&t.outgoers().some((function(t){return t.isNode()&&ka(e.target,t)}))},La[ha]=function(e,t){return ka(e.target,t)&&t.incomers().some((function(t){return t.isNode()&&ka(e.source,t)}))},La[pa]=function(e,t){return ka(e.child,t)&&ka(e.parent,t.parent())},La[fa]=function(e,t){return ka(e.parent,t)&&t.children().some((function(t){return ka(e.child,t)}))},La[ga]=function(e,t){return ka(e.descendant,t)&&t.ancestors().some((function(t){return ka(e.ancestor,t)}))},La[va]=function(e,t){return ka(e.ancestor,t)&&t.descendants().some((function(t){return ka(e.descendant,t)}))},La[ya]=function(e,t){return ka(e.subject,t)&&ka(e.left,t)&&ka(e.right,t)},La[ma]=function(){return!0},La[Ji]=function(e,t){return e.value.has(t)},La[ea]=function(e,t){return(0,e.value)(t)};var Sa={matches:function(e){for(var t=0;t<this.length;t++){var n=this[t];if(ka(n,e))return!0}return!1},filter:function(e){var t=this;if(1===t.length&&1===t[0].checks.length&&t[0].checks[0].type===oa)return e.getElementById(t[0].checks[0].value).collection();var n=function(e){for(var n=0;n<t.length;n++){var r=t[n];if(ka(r,e))return!0}return!1};return null==t.text()&&(n=function(){return!0}),e.filter(n)}},Ia=function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,null==e||f(e)&&e.match(/^\s*$/)||(w(e)?this.addQuery({checks:[{type:Ji,value:e.collection()}]}):v(e)?this.addQuery({checks:[{type:ea,value:e}]}):f(e)?this.parse(e)||(this.invalid=!0):Ve("A selector must be created from a string; found "))},Ma=Ia.prototype;[Ca,Sa].forEach((function(e){return z(Ma,e)})),Ma.text=function(){return this.inputText},Ma.size=function(){return this.length},Ma.eq=function(e){return this[e]},Ma.sameText=function(e){return!this.invalid&&!e.invalid&&this.text()===e.text()},Ma.addQuery=function(e){this[this.length++]=e},Ma.selector=Ma.toString;var Oa={allAre:function(e){var t=new Ia(e);return this.every((function(e){return t.matches(e)}))},is:function(e){var t=new Ia(e);return this.some((function(e){return t.matches(e)}))},some:function(e,t){for(var n=0;n<this.length;n++)if(t?e.apply(t,[this[n],n,this]):e(this[n],n,this))return!0;return!1},every:function(e,t){for(var n=0;n<this.length;n++)if(!(t?e.apply(t,[this[n],n,this]):e(this[n],n,this)))return!1;return!0},same:function(e){if(this===e)return!0;e=this.cy().collection(e);var t=this.length;return t===e.length&&(1===t?this[0]===e[0]:this.every((function(t){return e.hasElementWithId(t.id())})))},anySame:function(e){return e=this.cy().collection(e),this.some((function(t){return e.hasElementWithId(t.id())}))},allAreNeighbors:function(e){e=this.cy().collection(e);var t=this.neighborhood();return e.every((function(e){return t.hasElementWithId(e.id())}))},contains:function(e){e=this.cy().collection(e);var t=this;return e.every((function(e){return t.hasElementWithId(e.id())}))}};Oa.allAreNeighbours=Oa.allAreNeighbors,Oa.has=Oa.contains,Oa.equal=Oa.equals=Oa.same;var Pa,Ra,Ba=function(e,t){return function(n,r,i,a){var o,s=n,l=this;if(null==s?o="":w(s)&&1===s.length&&(o=s.id()),1===l.length&&o){var u=l[0]._private,c=u.traversalCache=u.traversalCache||{},h=c[t]=c[t]||[],d=Ie(o);return h[d]||(h[d]=e.call(l,n,r,i,a))}return e.call(l,n,r,i,a)}},Fa={parent:function(e){var t=[];if(1===this.length){var n=this[0]._private.parent;if(n)return n}for(var r=0;r<this.length;r++){var i=this[r]._private.parent;i&&t.push(i)}return this.spawn(t,!0).filter(e)},parents:function(e){for(var t=[],n=this.parent();n.nonempty();){for(var r=0;r<n.length;r++){var i=n[r];t.push(i)}n=n.parent()}return this.spawn(t,!0).filter(e)},commonAncestors:function(e){for(var t,n=0;n<this.length;n++){var r=this[n].parents();t=(t=t||r).intersect(r)}return t.filter(e)},orphans:function(e){return this.stdFilter((function(e){return e.isOrphan()})).filter(e)},nonorphans:function(e){return this.stdFilter((function(e){return e.isChild()})).filter(e)},children:Ba((function(e){for(var t=[],n=0;n<this.length;n++)for(var r=this[n]._private.children,i=0;i<r.length;i++)t.push(r[i]);return this.spawn(t,!0).filter(e)}),"children"),siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&0!==e._private.children.length},isChildless:function(){var e=this[0];if(e)return e.isNode()&&0===e._private.children.length},isChild:function(){var e=this[0];if(e)return e.isNode()&&null!=e._private.parent},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&null==e._private.parent},descendants:function(e){var t=[];return function e(n){for(var r=0;r<n.length;r++){var i=n[r];t.push(i),i.children().nonempty()&&e(i.children())}}(this.children()),this.spawn(t,!0).filter(e)}};function za(e,t,n,r){for(var i=[],a=new rt,o=e.cy().hasCompoundNodes(),s=0;s<e.length;s++){var l=e[s];n?i.push(l):o&&r(i,a,l)}for(;i.length>0;){var u=i.shift();t(u),a.add(u.id()),o&&r(i,a,u)}return e}function Ga(e,t,n){if(n.isParent())for(var r=n._private.children,i=0;i<r.length;i++){var a=r[i];t.has(a.id())||e.push(a)}}function Ya(e,t,n){if(n.isChild()){var r=n._private.parent;t.has(r.id())||e.push(r)}}function Xa(e,t,n){Ya(e,t,n),Ga(e,t,n)}Fa.forEachDown=function(e){return za(this,e,!(arguments.length>1&&void 0!==arguments[1])||arguments[1],Ga)},Fa.forEachUp=function(e){return za(this,e,!(arguments.length>1&&void 0!==arguments[1])||arguments[1],Ya)},Fa.forEachUpAndDown=function(e){return za(this,e,!(arguments.length>1&&void 0!==arguments[1])||arguments[1],Xa)},Fa.ancestors=Fa.parents,(Pa=Ra={data:Wi.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:Wi.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:Wi.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Wi.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:Wi.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:Wi.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}}).attr=Pa.data,Pa.removeAttr=Pa.removeData;var Va,Ua,ja=Ra,qa={};function Ha(e){return function(t){var n=this;if(void 0===t&&(t=!0),0!==n.length&&n.isNode()&&!n.removed()){for(var r=0,i=n[0],a=i._private.edges,o=0;o<a.length;o++){var s=a[o];!t&&s.isLoop()||(r+=e(i,s))}return r}}}function Wa(e,t){return function(n){for(var r,i=this.nodes(),a=0;a<i.length;a++){var o=i[a][e](n);void 0===o||void 0!==r&&!t(o,r)||(r=o)}return r}}z(qa,{degree:Ha((function(e,t){return t.source().same(t.target())?2:1})),indegree:Ha((function(e,t){return t.target().same(e)?1:0})),outdegree:Ha((function(e,t){return t.source().same(e)?1:0}))}),z(qa,{minDegree:Wa("degree",(function(e,t){return e<t})),maxDegree:Wa("degree",(function(e,t){return e>t})),minIndegree:Wa("indegree",(function(e,t){return e<t})),maxIndegree:Wa("indegree",(function(e,t){return e>t})),minOutdegree:Wa("outdegree",(function(e,t){return e<t})),maxOutdegree:Wa("outdegree",(function(e,t){return e>t}))}),z(qa,{totalDegree:function(e){for(var t=0,n=this.nodes(),r=0;r<n.length;r++)t+=n[r].degree(e);return t}});var $a=function(e,t,n){for(var r=0;r<e.length;r++){var i=e[r];if(!i.locked()){var a=i._private.position,o={x:null!=t.x?t.x-a.x:0,y:null!=t.y?t.y-a.y:0};!i.isParent()||0===o.x&&0===o.y||i.children().shift(o,n),i.dirtyBoundingBoxCache()}}},Ka={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:function(e,t){$a(e,t,!1)},onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}};(Va=Ua={position:Wi.data(Ka),silentPosition:Wi.data(z({},Ka,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:function(e,t){$a(e,t,!0)},onSet:function(e){e.dirtyCompoundBoundsCache()}})),positions:function(e,t){if(m(e))t?this.silentPosition(e):this.position(e);else if(v(e)){var n=e,r=this.cy();r.startBatch();for(var i=0;i<this.length;i++){var a,o=this[i];(a=n(o,i))&&(t?o.silentPosition(a):o.position(a))}r.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,t,n){var r;if(m(e)?(r={x:b(e.x)?e.x:0,y:b(e.y)?e.y:0},n=t):f(e)&&b(t)&&((r={x:0,y:0})[e]=t),null!=r){var i=this.cy();i.startBatch();for(var a=0;a<this.length;a++){var o=this[a];if(!(i.hasCompoundNodes()&&o.isChild()&&o.ancestors().anySame(this))){var s=o.position(),l={x:s.x+r.x,y:s.y+r.y};n?o.silentPosition(l):o.position(l)}}i.endBatch()}return this},silentShift:function(e,t){return m(e)?this.shift(e,!0):f(e)&&b(t)&&this.shift(e,t,!0),this},renderedPosition:function(e,t){var n=this[0],r=this.cy(),i=r.zoom(),a=r.pan(),o=m(e)?e:void 0,s=void 0!==o||void 0!==t&&f(e);if(n&&n.isNode()){if(!s){var l=n.position();return o=Et(l,i,a),void 0===e?o:o[e]}for(var u=0;u<this.length;u++){var c=this[u];void 0!==t?c.position(e,(t-a[e])/i):void 0!==o&&c.position(_t(o,i,a))}}else if(!s)return;return this},relativePosition:function(e,t){var n=this[0],r=this.cy(),i=m(e)?e:void 0,a=void 0!==i||void 0!==t&&f(e),o=r.hasCompoundNodes();if(n&&n.isNode()){if(!a){var s=n.position(),l=o?n.parent():null,u=l&&l.length>0,c=u;u&&(l=l[0]);var h=c?l.position():{x:0,y:0};return i={x:s.x-h.x,y:s.y-h.y},void 0===e?i:i[e]}for(var d=0;d<this.length;d++){var p=this[d],g=o?p.parent():null,v=g&&g.length>0,y=v;v&&(g=g[0]);var b=y?g.position():{x:0,y:0};void 0!==t?p.position(e,t+b[e]):void 0!==i&&p.position({x:i.x+b.x,y:i.y+b.y})}}else if(!a)return;return this}}).modelPosition=Va.point=Va.position,Va.modelPositions=Va.points=Va.positions,Va.renderedPoint=Va.renderedPosition,Va.relativePoint=Va.relativePosition;var Za,Qa,Ja=Ua;Za=Qa={},Qa.renderedBoundingBox=function(e){var t=this.boundingBox(e),n=this.cy(),r=n.zoom(),i=n.pan(),a=t.x1*r+i.x,o=t.x2*r+i.x,s=t.y1*r+i.y,l=t.y2*r+i.y;return{x1:a,x2:o,y1:s,y2:l,w:o-a,h:l-s}},Qa.dirtyCompoundBoundsCache=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.cy();return t.styleEnabled()&&t.hasCompoundNodes()?(this.forEachUp((function(t){if(t.isParent()){var n=t._private;n.compoundBoundsClean=!1,n.bbCache=null,e||t.emitAndNotify("bounds")}})),this):this},Qa.updateCompoundBounds=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.cy();if(!t.styleEnabled()||!t.hasCompoundNodes())return this;if(!e&&t.batching())return this;function n(e){if(e.isParent()){var t=e._private,n=e.children(),r="include"===e.pstyle("compound-sizing-wrt-labels").value,i={width:{val:e.pstyle("min-width").pfValue,left:e.pstyle("min-width-bias-left"),right:e.pstyle("min-width-bias-right")},height:{val:e.pstyle("min-height").pfValue,top:e.pstyle("min-height-bias-top"),bottom:e.pstyle("min-height-bias-bottom")}},a=n.boundingBox({includeLabels:r,includeOverlays:!1,useCache:!1}),o=t.position;0!==a.w&&0!==a.h||((a={w:e.pstyle("width").pfValue,h:e.pstyle("height").pfValue}).x1=o.x-a.w/2,a.x2=o.x+a.w/2,a.y1=o.y-a.h/2,a.y2=o.y+a.h/2);var s=i.width.left.value;"px"===i.width.left.units&&i.width.val>0&&(s=100*s/i.width.val);var l=i.width.right.value;"px"===i.width.right.units&&i.width.val>0&&(l=100*l/i.width.val);var u=i.height.top.value;"px"===i.height.top.units&&i.height.val>0&&(u=100*u/i.height.val);var c=i.height.bottom.value;"px"===i.height.bottom.units&&i.height.val>0&&(c=100*c/i.height.val);var h=y(i.width.val-a.w,s,l),d=h.biasDiff,p=h.biasComplementDiff,g=y(i.height.val-a.h,u,c),f=g.biasDiff,v=g.biasComplementDiff;t.autoPadding=function(e,t,n,r){if("%"!==n.units)return"px"===n.units?n.pfValue:0;switch(r){case"width":return e>0?n.pfValue*e:0;case"height":return t>0?n.pfValue*t:0;case"average":return e>0&&t>0?n.pfValue*(e+t)/2:0;case"min":return e>0&&t>0?e>t?n.pfValue*t:n.pfValue*e:0;case"max":return e>0&&t>0?e>t?n.pfValue*e:n.pfValue*t:0;default:return 0}}(a.w,a.h,e.pstyle("padding"),e.pstyle("padding-relative-to").value),t.autoWidth=Math.max(a.w,i.width.val),o.x=(-d+a.x1+a.x2+p)/2,t.autoHeight=Math.max(a.h,i.height.val),o.y=(-f+a.y1+a.y2+v)/2}function y(e,t,n){var r=0,i=0,a=t+n;return e>0&&a>0&&(r=t/a*e,i=n/a*e),{biasDiff:r,biasComplementDiff:i}}}for(var r=0;r<this.length;r++){var i=this[r],a=i._private;a.compoundBoundsClean&&!e||(n(i),t.batching()||(a.compoundBoundsClean=!0))}return this};var eo=function(e){return e===1/0||e===-1/0?0:e},to=function(e,t,n,r,i){r-t!=0&&i-n!=0&&null!=t&&null!=n&&null!=r&&null!=i&&(e.x1=t<e.x1?t:e.x1,e.x2=r>e.x2?r:e.x2,e.y1=n<e.y1?n:e.y1,e.y2=i>e.y2?i:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},no=function(e,t){return null==t?e:to(e,t.x1,t.y1,t.x2,t.y2)},ro=function(e,t,n){return Je(e,t,n)},io=function(e,t,n){if(!t.cy().headless()){var r,i,a=t._private,o=a.rstyle,s=o.arrowWidth/2;if("none"!==t.pstyle(n+"-arrow-shape").value){"source"===n?(r=o.srcX,i=o.srcY):"target"===n?(r=o.tgtX,i=o.tgtY):(r=o.midX,i=o.midY);var l=a.arrowBounds=a.arrowBounds||{},u=l[n]=l[n]||{};u.x1=r-s,u.y1=i-s,u.x2=r+s,u.y2=i+s,u.w=u.x2-u.x1,u.h=u.y2-u.y1,Rt(u,1),to(e,u.x1,u.y1,u.x2,u.y2)}}},ao=function(e,t,n){if(!t.cy().headless()){var r;r=n?n+"-":"";var i=t._private,a=i.rstyle;if(t.pstyle(r+"label").strValue){var o,s,l,u,c=t.pstyle("text-halign"),h=t.pstyle("text-valign"),d=ro(a,"labelWidth",n),p=ro(a,"labelHeight",n),g=ro(a,"labelX",n),f=ro(a,"labelY",n),v=t.pstyle(r+"text-margin-x").pfValue,y=t.pstyle(r+"text-margin-y").pfValue,m=t.isEdge(),b=t.pstyle(r+"text-rotation"),x=t.pstyle("text-outline-width").pfValue,w=t.pstyle("text-border-width").pfValue/2,E=t.pstyle("text-background-padding").pfValue,_=p,T=d,D=T/2,C=_/2;if(m)o=g-D,s=g+D,l=f-C,u=f+C;else{switch(c.value){case"left":o=g-T,s=g;break;case"center":o=g-D,s=g+D;break;case"right":o=g,s=g+T}switch(h.value){case"top":l=f-_,u=f;break;case"center":l=f-C,u=f+C;break;case"bottom":l=f,u=f+_}}o+=v-Math.max(x,w)-E-2,s+=v+Math.max(x,w)+E+2,l+=y-Math.max(x,w)-E-2,u+=y+Math.max(x,w)+E+2;var N=n||"main",A=i.labelBounds,L=A[N]=A[N]||{};L.x1=o,L.y1=l,L.x2=s,L.y2=u,L.w=s-o,L.h=u-l;var k=m&&"autorotate"===b.strValue,S=null!=b.pfValue&&0!==b.pfValue;if(k||S){var I=k?ro(i.rstyle,"labelAngle",n):b.pfValue,M=Math.cos(I),O=Math.sin(I),P=(o+s)/2,R=(l+u)/2;if(!m){switch(c.value){case"left":P=s;break;case"right":P=o}switch(h.value){case"top":R=u;break;case"bottom":R=l}}var B=function(e,t){return{x:(e-=P)*M-(t-=R)*O+P,y:e*O+t*M+R}},F=B(o,l),z=B(o,u),G=B(s,l),Y=B(s,u);o=Math.min(F.x,z.x,G.x,Y.x),s=Math.max(F.x,z.x,G.x,Y.x),l=Math.min(F.y,z.y,G.y,Y.y),u=Math.max(F.y,z.y,G.y,Y.y)}var X=N+"Rot",V=A[X]=A[X]||{};V.x1=o,V.y1=l,V.x2=s,V.y2=u,V.w=s-o,V.h=u-l,to(e,o,l,s,u),to(i.labelBounds.all,o,l,s,u)}return e}},oo=function(e){var t=0,n=function(e){return(e?1:0)<<t++},r=0;return r+=n(e.incudeNodes),r+=n(e.includeEdges),r+=n(e.includeLabels),r+=n(e.includeMainLabels),r+=n(e.includeSourceLabels),(r+=n(e.includeTargetLabels))+n(e.includeOverlays)},so=function(e){if(e.isEdge()){var t=e.source().position(),n=e.target().position(),r=function(e){return Math.round(e)};return function(e,t){var n={value:0,done:!1},r=0,i=e.length;return Ne({next:function(){return r<i?n.value=e[r++]:n.done=!0,n}},t)}([r(t.x),r(t.y),r(n.x),r(n.y)])}return 0},lo=function(e,t){var n,r=e._private,i=e.isEdge(),a=(null==t?co:oo(t))===co,o=so(e),s=r.bbCachePosKey===o,l=t.useCache&&s,u=function(e){return null==e._private.bbCache||e._private.styleDirty};if(!l||u(e)||i&&u(e.source())||u(e.target())?(s||e.recalculateRenderedStyle(l),n=function(e,t){var n,r,i,a,o,s,l,u=e._private.cy,c=u.styleEnabled(),h=u.headless(),d=Ot(),p=e._private,g=e.isNode(),f=e.isEdge(),v=p.rstyle,y=g&&c?e.pstyle("bounds-expansion").pfValue:[0],m=function(e){return"none"!==e.pstyle("display").value},b=!c||m(e)&&(!f||m(e.source())&&m(e.target()));if(b){var x=0;c&&t.includeOverlays&&0!==e.pstyle("overlay-opacity").value&&(x=e.pstyle("overlay-padding").value);var w=0;c&&t.includeUnderlays&&0!==e.pstyle("underlay-opacity").value&&(w=e.pstyle("underlay-padding").value);var E=Math.max(x,w),_=0;if(c&&(_=e.pstyle("width").pfValue/2),g&&t.includeNodes){var T=e.position();o=T.x,s=T.y;var D=e.outerWidth()/2,C=e.outerHeight()/2;to(d,n=o-D,i=s-C,r=o+D,a=s+C)}else if(f&&t.includeEdges)if(c&&!h){var N=e.pstyle("curve-style").strValue;if(n=Math.min(v.srcX,v.midX,v.tgtX),r=Math.max(v.srcX,v.midX,v.tgtX),i=Math.min(v.srcY,v.midY,v.tgtY),a=Math.max(v.srcY,v.midY,v.tgtY),to(d,n-=_,i-=_,r+=_,a+=_),"haystack"===N){var A=v.haystackPts;if(A&&2===A.length){if(n=A[0].x,i=A[0].y,n>(r=A[1].x)){var L=n;n=r,r=L}if(i>(a=A[1].y)){var k=i;i=a,a=k}to(d,n-_,i-_,r+_,a+_)}}else if("bezier"===N||"unbundled-bezier"===N||"segments"===N||"taxi"===N){var S;switch(N){case"bezier":case"unbundled-bezier":S=v.bezierPts;break;case"segments":case"taxi":S=v.linePts}if(null!=S)for(var I=0;I<S.length;I++){var M=S[I];n=M.x-_,r=M.x+_,i=M.y-_,a=M.y+_,to(d,n,i,r,a)}}}else{var O=e.source().position(),P=e.target().position();if((n=O.x)>(r=P.x)){var R=n;n=r,r=R}if((i=O.y)>(a=P.y)){var B=i;i=a,a=B}to(d,n-=_,i-=_,r+=_,a+=_)}if(c&&t.includeEdges&&f&&(io(d,e,"mid-source"),io(d,e,"mid-target"),io(d,e,"source"),io(d,e,"target")),c&&"yes"===e.pstyle("ghost").value){var F=e.pstyle("ghost-offset-x").pfValue,z=e.pstyle("ghost-offset-y").pfValue;to(d,d.x1+F,d.y1+z,d.x2+F,d.y2+z)}var G=p.bodyBounds=p.bodyBounds||{};Ft(G,d),Bt(G,y),Rt(G,1),c&&(n=d.x1,r=d.x2,i=d.y1,a=d.y2,to(d,n-E,i-E,r+E,a+E));var Y=p.overlayBounds=p.overlayBounds||{};Ft(Y,d),Bt(Y,y),Rt(Y,1);var X=p.labelBounds=p.labelBounds||{};null!=X.all?((l=X.all).x1=1/0,l.y1=1/0,l.x2=-1/0,l.y2=-1/0,l.w=0,l.h=0):X.all=Ot(),c&&t.includeLabels&&(t.includeMainLabels&&ao(d,e,null),f&&(t.includeSourceLabels&&ao(d,e,"source"),t.includeTargetLabels&&ao(d,e,"target")))}return d.x1=eo(d.x1),d.y1=eo(d.y1),d.x2=eo(d.x2),d.y2=eo(d.y2),d.w=eo(d.x2-d.x1),d.h=eo(d.y2-d.y1),d.w>0&&d.h>0&&b&&(Bt(d,y),Rt(d,1)),d}(e,uo),r.bbCache=n,r.bbCachePosKey=o):n=r.bbCache,!a){var c=e.isNode();n=Ot(),(t.includeNodes&&c||t.includeEdges&&!c)&&(t.includeOverlays?no(n,r.overlayBounds):no(n,r.bodyBounds)),t.includeLabels&&(t.includeMainLabels&&(!i||t.includeSourceLabels&&t.includeTargetLabels)?no(n,r.labelBounds.all):(t.includeMainLabels&&no(n,r.labelBounds.mainRot),t.includeSourceLabels&&no(n,r.labelBounds.sourceRot),t.includeTargetLabels&&no(n,r.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},uo={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,useCache:!0},co=oo(uo),ho=Ke(uo);Qa.boundingBox=function(e){var t;if(1!==this.length||null==this[0]._private.bbCache||this[0]._private.styleDirty||void 0!==e&&void 0!==e.useCache&&!0!==e.useCache){t=Ot();var n=ho(e=e||uo),r=this;if(r.cy().styleEnabled())for(var i=0;i<r.length;i++){var a=r[i],o=a._private,s=so(a),l=o.bbCachePosKey===s,u=n.useCache&&l&&!o.styleDirty;a.recalculateRenderedStyle(u)}this.updateCompoundBounds(!e.useCache);for(var c=0;c<r.length;c++){var h=r[c];no(t,lo(h,n))}}else e=void 0===e?uo:ho(e),t=lo(this[0],e);return t.x1=eo(t.x1),t.y1=eo(t.y1),t.x2=eo(t.x2),t.y2=eo(t.y2),t.w=eo(t.x2-t.x1),t.h=eo(t.y2-t.y1),t},Qa.dirtyBoundingBoxCache=function(){for(var e=0;e<this.length;e++){var t=this[e]._private;t.bbCache=null,t.bbCachePosKey=null,t.bodyBounds=null,t.overlayBounds=null,t.labelBounds.all=null,t.labelBounds.source=null,t.labelBounds.target=null,t.labelBounds.main=null,t.labelBounds.sourceRot=null,t.labelBounds.targetRot=null,t.labelBounds.mainRot=null,t.arrowBounds.source=null,t.arrowBounds.target=null,t.arrowBounds["mid-source"]=null,t.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this},Qa.boundingBoxAt=function(e){var t=this.nodes(),n=this.cy(),r=n.hasCompoundNodes(),i=n.collection();if(r&&(i=t.filter((function(e){return e.isParent()})),t=t.not(i)),m(e)){var a=e;e=function(){return a}}n.startBatch(),t.forEach((function(t,n){return t._private.bbAtOldPos=e(t,n)})).silentPositions(e),r&&(i.dirtyCompoundBoundsCache(),i.dirtyBoundingBoxCache(),i.updateCompoundBounds(!0));var o=function(e){return{x1:e.x1,x2:e.x2,w:e.w,y1:e.y1,y2:e.y2,h:e.h}}(this.boundingBox({useCache:!1}));return t.silentPositions((function(e){return e._private.bbAtOldPos})),r&&(i.dirtyCompoundBoundsCache(),i.dirtyBoundingBoxCache(),i.updateCompoundBounds(!0)),n.endBatch(),o},Za.boundingbox=Za.bb=Za.boundingBox,Za.renderedBoundingbox=Za.renderedBoundingBox;var po,go,fo=Qa;po=go={};var vo=function(e){e.uppercaseName=I(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=I(e.outerName),po[e.name]=function(){var t=this[0],n=t._private,r=n.cy._private.styleEnabled;if(t){if(r){if(t.isParent())return t.updateCompoundBounds(),n[e.autoName]||0;var i=t.pstyle(e.name);return"label"===i.strValue?(t.recalculateRenderedStyle(),n.rstyle[e.labelName]||0):i.pfValue}return 1}},po["outer"+e.uppercaseName]=function(){var t=this[0],n=t._private.cy._private.styleEnabled;if(t)return n?t[e.name]()+t.pstyle("border-width").pfValue+2*t.padding():1},po["rendered"+e.uppercaseName]=function(){var t=this[0];if(t)return t[e.name]()*this.cy().zoom()},po["rendered"+e.uppercaseOuterName]=function(){var t=this[0];if(t)return t[e.outerName]()*this.cy().zoom()}};vo({name:"width"}),vo({name:"height"}),go.padding=function(){var e=this[0],t=e._private;return e.isParent()?(e.updateCompoundBounds(),void 0!==t.autoPadding?t.autoPadding:e.pstyle("padding").pfValue):e.pstyle("padding").pfValue},go.paddedHeight=function(){var e=this[0];return e.height()+2*e.padding()},go.paddedWidth=function(){var e=this[0];return e.width()+2*e.padding()};var yo=go,mo={controlPoints:{get:function(e){return e.renderer().getControlPoints(e)},mult:!0},segmentPoints:{get:function(e){return e.renderer().getSegmentPoints(e)},mult:!0},sourceEndpoint:{get:function(e){return e.renderer().getSourceEndpoint(e)}},targetEndpoint:{get:function(e){return e.renderer().getTargetEndpoint(e)}},midpoint:{get:function(e){return e.renderer().getEdgeMidpoint(e)}}},bo=Object.keys(mo).reduce((function(e,t){var n=mo[t],r=function(e){return"rendered"+e[0].toUpperCase()+e.substr(1)}(t);return e[t]=function(){return function(e,t){if(e.isEdge())return t(e)}(this,n.get)},n.mult?e[r]=function(){return function(e,t){if(e.isEdge()){var n=e.cy(),r=n.pan(),i=n.zoom();return t(e).map((function(e){return Et(e,i,r)}))}}(this,n.get)}:e[r]=function(){return function(e,t){if(e.isEdge()){var n=e.cy();return Et(t(e),n.zoom(),n.pan())}}(this,n.get)},e}),{}),xo=z({},Ja,fo,yo,bo),wo=function(e,t){this.recycle(e,t)};function Eo(){return!1}function _o(){return!0}wo.prototype={instanceString:function(){return"event"},recycle:function(e,t){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=Eo,null!=e&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?_o:Eo):null!=e&&e.type?t=e:this.type=e,null!=t&&(this.originalEvent=t.originalEvent,this.type=null!=t.type?t.type:this.type,this.cy=t.cy,this.target=t.target,this.position=t.position,this.renderedPosition=t.renderedPosition,this.namespace=t.namespace,this.layout=t.layout),null!=this.cy&&null!=this.position&&null==this.renderedPosition){var n=this.position,r=this.cy.zoom(),i=this.cy.pan();this.renderedPosition={x:n.x*r+i.x,y:n.y*r+i.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=_o;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=_o;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=_o,this.stopPropagation()},isDefaultPrevented:Eo,isPropagationStopped:Eo,isImmediatePropagationStopped:Eo};var To=/^([^.]+)(\.(?:[^.]+))?$/,Do={qualifierCompare:function(e,t){return e===t},eventMatches:function(){return!0},addEventFields:function(){},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:null},Co=Object.keys(Do),No={};function Ao(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:No,t=arguments.length>1?arguments[1]:void 0,n=0;n<Co.length;n++){var r=Co[n];this[r]=e[r]||Do[r]}this.context=t||this.context,this.listeners=[],this.emitting=0}var Lo=Ao.prototype,ko=function(e,t,n,r,i,a,o){v(r)&&(i=r,r=null),o&&(a=null==a?o:z({},a,o));for(var s=y(n)?n:n.split(/\s+/),l=0;l<s.length;l++){var u=s[l];if(!C(u)){var c=u.match(To);if(c&&!1===t(e,u,c[1],c[2]?c[2]:null,r,i,a))break}}},So=function(e,t){return e.addEventFields(e.context,t),new wo(t.type,t)},Io=function(e,t,n){if("event"===g(n))t(e,n);else if(m(n))t(e,So(e,n));else for(var r=y(n)?n:n.split(/\s+/),i=0;i<r.length;i++){var a=r[i];if(!C(a)){var o=a.match(To);if(o){var s=o[1],l=o[2]?o[2]:null;t(e,So(e,{type:s,namespace:l,target:e.context}))}}}};Lo.on=Lo.addListener=function(e,t,n,r,i){return ko(this,(function(e,t,n,r,i,a,o){v(a)&&e.listeners.push({event:t,callback:a,type:n,namespace:r,qualifier:i,conf:o})}),e,t,n,r,i),this},Lo.one=function(e,t,n,r){return this.on(e,t,n,r,{one:!0})},Lo.removeListener=Lo.off=function(e,t,n,r){var i=this;0!==this.emitting&&(this.listeners=this.listeners.slice());for(var a=this.listeners,o=function(o){var s=a[o];ko(i,(function(t,n,r,i,l,u){if((s.type===r||"*"===e)&&(!i&&".*"!==s.namespace||s.namespace===i)&&(!l||t.qualifierCompare(s.qualifier,l))&&(!u||s.callback===u))return a.splice(o,1),!1}),e,t,n,r)},s=a.length-1;s>=0;s--)o(s);return this},Lo.removeAllListeners=function(){return this.removeListener("*")},Lo.emit=Lo.trigger=function(e,t,n){var r=this.listeners,i=r.length;return this.emitting++,y(t)||(t=[t]),Io(this,(function(e,a){null!=n&&(r=[{event:a.event,type:a.type,namespace:a.namespace,callback:n}],i=r.length);for(var o=function(n){var i=r[n];if(i.type===a.type&&(!i.namespace||i.namespace===a.namespace||".*"===i.namespace)&&e.eventMatches(e.context,i,a)){var o=[a];null!=t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];e.push(r)}}(o,t),e.beforeEmit(e.context,i,a),i.conf&&i.conf.one&&(e.listeners=e.listeners.filter((function(e){return e!==i})));var s=e.callbackContext(e.context,i,a),l=i.callback.apply(s,o);e.afterEmit(e.context,i,a),!1===l&&(a.stopPropagation(),a.preventDefault())}},s=0;s<i;s++)o(s);e.bubble(e.context)&&!a.isPropagationStopped()&&e.parent(e.context).emit(a,t)}),e),this.emitting--,this};var Mo={qualifierCompare:function(e,t){return null==e||null==t?null==e&&null==t:e.sameText(t)},eventMatches:function(e,t,n){var r=t.qualifier;return null==r||e!==n.target&&E(n.target)&&r.matches(n.target)},addEventFields:function(e,t){t.cy=e.cy(),t.target=e},callbackContext:function(e,t,n){return null!=t.qualifier?n.target:e},beforeEmit:function(e,t){t.conf&&t.conf.once&&t.conf.onceCollection.removeListener(t.event,t.qualifier,t.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},Oo=function(e){return f(e)?new Ia(e):e},Po={createEmitter:function(){for(var e=0;e<this.length;e++){var t=this[e],n=t._private;n.emitter||(n.emitter=new Ao(Mo,t))}return this},emitter:function(){return this._private.emitter},on:function(e,t,n){for(var r=Oo(t),i=0;i<this.length;i++)this[i].emitter().on(e,r,n);return this},removeListener:function(e,t,n){for(var r=Oo(t),i=0;i<this.length;i++)this[i].emitter().removeListener(e,r,n);return this},removeAllListeners:function(){for(var e=0;e<this.length;e++)this[e].emitter().removeAllListeners();return this},one:function(e,t,n){for(var r=Oo(t),i=0;i<this.length;i++)this[i].emitter().one(e,r,n);return this},once:function(e,t,n){for(var r=Oo(t),i=0;i<this.length;i++)this[i].emitter().on(e,r,n,{once:!0,onceCollection:this})},emit:function(e,t){for(var n=0;n<this.length;n++)this[n].emitter().emit(e,t);return this},emitAndNotify:function(e,t){if(0!==this.length)return this.cy().notify(e,this),this.emit(e,t),this}};Wi.eventAliasesOn(Po);var Ro={nodes:function(e){return this.filter((function(e){return e.isNode()})).filter(e)},edges:function(e){return this.filter((function(e){return e.isEdge()})).filter(e)},byGroup:function(){for(var e=this.spawn(),t=this.spawn(),n=0;n<this.length;n++){var r=this[n];r.isNode()?e.push(r):t.push(r)}return{nodes:e,edges:t}},filter:function(e,t){if(void 0===e)return this;if(f(e)||w(e))return new Ia(e).filter(this);if(v(e)){for(var n=this.spawn(),r=this,i=0;i<r.length;i++){var a=r[i];(t?e.apply(t,[a,i,r]):e(a,i,r))&&n.push(a)}return n}return this.spawn()},not:function(e){if(e){f(e)&&(e=this.filter(e));for(var t=this.spawn(),n=0;n<this.length;n++){var r=this[n];e.has(r)||t.push(r)}return t}return this},absoluteComplement:function(){return this.cy().mutableElements().not(this)},intersect:function(e){if(f(e)){var t=e;return this.filter(t)}for(var n=this.spawn(),r=e,i=this.length<e.length,a=i?this:r,o=i?r:this,s=0;s<a.length;s++){var l=a[s];o.has(l)&&n.push(l)}return n},xor:function(e){var t=this._private.cy;f(e)&&(e=t.$(e));var n=this.spawn(),r=e,i=function(e,t){for(var r=0;r<e.length;r++){var i=e[r],a=i._private.data.id;t.hasElementWithId(a)||n.push(i)}};return i(this,r),i(r,this),n},diff:function(e){var t=this._private.cy;f(e)&&(e=t.$(e));var n=this.spawn(),r=this.spawn(),i=this.spawn(),a=e,o=function(e,t,n){for(var r=0;r<e.length;r++){var a=e[r],o=a._private.data.id;t.hasElementWithId(o)?i.merge(a):n.push(a)}};return o(this,a,n),o(a,this,r),{left:n,right:r,both:i}},add:function(e){var t=this._private.cy;if(!e)return this;if(f(e)){var n=e;e=t.mutableElements().filter(n)}for(var r=this.spawnSelf(),i=0;i<e.length;i++){var a=e[i];!this.has(a)&&r.push(a)}return r},merge:function(e){var t=this._private,n=t.cy;if(!e)return this;if(e&&f(e)){var r=e;e=n.mutableElements().filter(r)}for(var i=t.map,a=0;a<e.length;a++){var o=e[a],s=o._private.data.id;if(!i.has(s)){var l=this.length++;this[l]=o,i.set(s,{ele:o,index:l})}}return this},unmergeAt:function(e){var t=this[e].id(),n=this._private.map;this[e]=void 0,n.delete(t);var r=e===this.length-1;if(this.length>1&&!r){var i=this.length-1,a=this[i],o=a._private.data.id;this[i]=void 0,this[e]=a,n.set(o,{ele:a,index:e})}return this.length--,this},unmergeOne:function(e){e=e[0];var t=this._private,n=e._private.data.id,r=t.map.get(n);if(!r)return this;var i=r.index;return this.unmergeAt(i),this},unmerge:function(e){var t=this._private.cy;if(!e)return this;if(e&&f(e)){var n=e;e=t.mutableElements().filter(n)}for(var r=0;r<e.length;r++)this.unmergeOne(e[r]);return this},unmergeBy:function(e){for(var t=this.length-1;t>=0;t--)e(this[t])&&this.unmergeAt(t);return this},map:function(e,t){for(var n=[],r=this,i=0;i<r.length;i++){var a=r[i],o=t?e.apply(t,[a,i,r]):e(a,i,r);n.push(o)}return n},reduce:function(e,t){for(var n=t,r=this,i=0;i<r.length;i++)n=e(n,r[i],i,r);return n},max:function(e,t){for(var n,r=-1/0,i=this,a=0;a<i.length;a++){var o=i[a],s=t?e.apply(t,[o,a,i]):e(o,a,i);s>r&&(r=s,n=o)}return{value:r,ele:n}},min:function(e,t){for(var n,r=1/0,i=this,a=0;a<i.length;a++){var o=i[a],s=t?e.apply(t,[o,a,i]):e(o,a,i);s<r&&(r=s,n=o)}return{value:r,ele:n}}},Bo=Ro;Bo.u=Bo["|"]=Bo["+"]=Bo.union=Bo.or=Bo.add,Bo["\\"]=Bo["!"]=Bo["-"]=Bo.difference=Bo.relativeComplement=Bo.subtract=Bo.not,Bo.n=Bo["&"]=Bo["."]=Bo.and=Bo.intersection=Bo.intersect,Bo["^"]=Bo["(+)"]=Bo["(-)"]=Bo.symmetricDifference=Bo.symdiff=Bo.xor,Bo.fnFilter=Bo.filterFn=Bo.stdFilter=Bo.filter,Bo.complement=Bo.abscomp=Bo.absoluteComplement;var Fo,zo=function(e,t){var n=e.cy().hasCompoundNodes();function r(e){var t=e.pstyle("z-compound-depth");return"auto"===t.value?n?e.zDepth():0:"bottom"===t.value?-1:"top"===t.value?Fe:0}var i=r(e)-r(t);if(0!==i)return i;function a(e){return"auto"===e.pstyle("z-index-compare").value&&e.isNode()?1:0}var o=a(e)-a(t);if(0!==o)return o;var s=e.pstyle("z-index").value-t.pstyle("z-index").value;return 0!==s?s:e.poolIndex()-t.poolIndex()},Go={forEach:function(e,t){if(v(e))for(var n=this.length,r=0;r<n;r++){var i=this[r];if(!1===(t?e.apply(t,[i,r,this]):e(i,r,this)))break}return this},toArray:function(){for(var e=[],t=0;t<this.length;t++)e.push(this[t]);return e},slice:function(e,t){var n=[],r=this.length;null==t&&(t=r),null==e&&(e=0),e<0&&(e=r+e),t<0&&(t=r+t);for(var i=e;i>=0&&i<t&&i<r;i++)n.push(this[i]);return this.spawn(n)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return 0===this.length},nonempty:function(){return!this.empty()},sort:function(e){if(!v(e))return this;var t=this.toArray().sort(e);return this.spawn(t)},sortByZIndex:function(){return this.sort(zo)},zDepth:function(){var e=this[0];if(e){var t=e._private;if("nodes"===t.group){var n=t.data.parent?e.parents().size():0;return e.isParent()?n:Fe-1}var r=t.source,i=t.target,a=r.zDepth(),o=i.zDepth();return Math.max(a,o,0)}}};Go.each=Go.forEach,Fo="undefined",("undefined"==typeof Symbol?"undefined":e(Symbol))!=Fo&&e(Symbol.iterator)!=Fo&&(Go[Symbol.iterator]=function(){var e=this,t={value:void 0,done:!1},n=0,r=this.length;return a({next:function(){return n<r?t.value=e[n++]:(t.value=void 0,t.done=!0),t}},Symbol.iterator,(function(){return this}))});var Yo=Ke({nodeDimensionsIncludeLabels:!1}),Xo={layoutDimensions:function(e){var t;if(e=Yo(e),this.takesUpSpace())if(e.nodeDimensionsIncludeLabels){var n=this.boundingBox();t={w:n.w,h:n.h}}else t={w:this.outerWidth(),h:this.outerHeight()};else t={w:0,h:0};return 0!==t.w&&0!==t.h||(t.w=t.h=1),t},layoutPositions:function(e,t,n){var r=this.nodes().filter((function(e){return!e.isParent()})),i=this.cy(),a=t.eles,o=function(e){return e.id()},s=A(n,o);e.emit({type:"layoutstart",layout:e}),e.animations=[];var l=t.spacingFactor&&1!==t.spacingFactor,u=function(){if(!l)return null;for(var e=Ot(),t=0;t<r.length;t++){var n=r[t],i=s(n,t);Pt(e,i.x,i.y)}return e}(),c=A((function(e,n){var r=s(e,n);l&&(r=function(e,t,n){var r=t.x1+t.w/2,i=t.y1+t.h/2;return{x:r+(n.x-r)*e,y:i+(n.y-i)*e}}(Math.abs(t.spacingFactor),u,r));return null!=t.transform&&(r=t.transform(e,r)),r}),o);if(t.animate){for(var h=0;h<r.length;h++){var d=r[h],p=c(d,h);if(null==t.animateFilter||t.animateFilter(d,h)){var g=d.animation({position:p,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(g)}else d.position(p)}if(t.fit){var f=i.animation({fit:{boundingBox:a.boundingBoxAt(c),padding:t.padding},duration:t.animationDuration,easing:t.animationEasing});e.animations.push(f)}else if(void 0!==t.zoom&&void 0!==t.pan){var v=i.animation({zoom:t.zoom,pan:t.pan,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(v)}e.animations.forEach((function(e){return e.play()})),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),_r.all(e.animations.map((function(e){return e.promise()}))).then((function(){e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e})}))}else r.positions(c),t.fit&&i.fit(t.eles,t.padding),null!=t.zoom&&i.zoom(t.zoom),t.pan&&i.pan(t.pan),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){return this.cy().makeLayout(z({},e,{eles:this}))}};function Vo(e,t,n){var r,i=n._private,a=i.styleCache=i.styleCache||[];return null!=(r=a[e])?r:r=a[e]=t(n)}function Uo(e,t){return e=Ie(e),function(n){return Vo(e,t,n)}}function jo(e,t){e=Ie(e);var n=function(e){return t.call(e)};return function(){var t=this[0];if(t)return Vo(e,n,t)}}Xo.createLayout=Xo.makeLayout=Xo.layout;var qo={recalculateRenderedStyle:function(e){var t=this.cy(),n=t.renderer(),r=t.styleEnabled();return n&&r&&n.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e,t=this.cy(),n=function(e){return e._private.styleCache=null};return t.hasCompoundNodes()?((e=this.spawnSelf().merge(this.descendants()).merge(this.parents())).merge(e.connectedEdges()),e.forEach(n)):this.forEach((function(e){n(e),e.connectedEdges().forEach(n)})),this},updateStyle:function(e){var t=this._private.cy;if(!t.styleEnabled())return this;if(t.batching())return t._private.batchStyleEles.merge(this),this;var n=this;e=!(!e&&void 0!==e),t.hasCompoundNodes()&&(n=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var r=n;return e?r.emitAndNotify("style"):r.emit("style"),n.forEach((function(e){return e._private.styleDirty=!0})),this},cleanStyle:function(){var e=this.cy();if(e.styleEnabled())for(var t=0;t<this.length;t++){var n=this[t];n._private.styleDirty&&(n._private.styleDirty=!1,e.style().apply(n))}},parsedStyle:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this[0],r=n.cy();if(r.styleEnabled()&&n){this.cleanStyle();var i=n._private.style[e];return null!=i?i:t?r.style().getDefaultProperty(e):null}},numericStyle:function(e){var t=this[0];if(t.cy().styleEnabled()&&t){var n=t.pstyle(e);return void 0!==n.pfValue?n.pfValue:n.value}},numericStyleUnits:function(e){var t=this[0];if(t.cy().styleEnabled())return t?t.pstyle(e).units:void 0},renderedStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var n=this[0];return n?t.style().getRenderedStyle(n,e):void 0},style:function(e,t){var n=this.cy();if(!n.styleEnabled())return this;var r=n.style();if(m(e)){var i=e;r.applyBypass(this,i,false),this.emitAndNotify("style")}else if(f(e)){if(void 0===t){var a=this[0];return a?r.getStylePropertyValue(a,e):void 0}r.applyBypass(this,e,t,false),this.emitAndNotify("style")}else if(void 0===e){var o=this[0];return o?r.getRawStyle(o):void 0}return this},removeStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var n=t.style(),r=this;if(void 0===e)for(var i=0;i<r.length;i++){var a=r[i];n.removeAllBypasses(a,false)}else{e=e.split(/\s+/);for(var o=0;o<r.length;o++){var s=r[o];n.removeBypasses(s,e,false)}}return this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var t=e.hasCompoundNodes(),n=this[0];if(n){var r=n._private,i=n.pstyle("opacity").value;if(!t)return i;var a=r.data.parent?n.parents():null;if(a)for(var o=0;o<a.length;o++)i*=a[o].pstyle("opacity").value;return i}},transparent:function(){if(!this.cy().styleEnabled())return!1;var e=this[0],t=e.cy().hasCompoundNodes();return e?t?0===e.effectiveOpacity():0===e.pstyle("opacity").value:void 0},backgrounding:function(){return!!this.cy().styleEnabled()&&!!this[0]._private.backgrounding}};function Ho(e,t){var n=e._private.data.parent?e.parents():null;if(n)for(var r=0;r<n.length;r++)if(!t(n[r]))return!1;return!0}function Wo(e){var t=e.ok,n=e.edgeOkViaNode||e.ok,r=e.parentOk||e.ok;return function(){var e=this.cy();if(!e.styleEnabled())return!0;var i=this[0],a=e.hasCompoundNodes();if(i){var o=i._private;if(!t(i))return!1;if(i.isNode())return!a||Ho(i,r);var s=o.source,l=o.target;return n(s)&&(!a||Ho(s,n))&&(s===l||n(l)&&(!a||Ho(l,n)))}}}var $o=Uo("eleTakesUpSpace",(function(e){return"element"===e.pstyle("display").value&&0!==e.width()&&(!e.isNode()||0!==e.height())}));qo.takesUpSpace=jo("takesUpSpace",Wo({ok:$o}));var Ko=Uo("eleInteractive",(function(e){return"yes"===e.pstyle("events").value&&"visible"===e.pstyle("visibility").value&&$o(e)})),Zo=Uo("parentInteractive",(function(e){return"visible"===e.pstyle("visibility").value&&$o(e)}));qo.interactive=jo("interactive",Wo({ok:Ko,parentOk:Zo,edgeOkViaNode:$o})),qo.noninteractive=function(){var e=this[0];if(e)return!e.interactive()};var Qo=Uo("eleVisible",(function(e){return"visible"===e.pstyle("visibility").value&&0!==e.pstyle("opacity").pfValue&&$o(e)})),Jo=$o;qo.visible=jo("visible",Wo({ok:Qo,edgeOkViaNode:Jo})),qo.hidden=function(){var e=this[0];if(e)return!e.visible()},qo.isBundledBezier=jo("isBundledBezier",(function(){return!!this.cy().styleEnabled()&&!this.removed()&&"bezier"===this.pstyle("curve-style").value&&this.takesUpSpace()})),qo.bypass=qo.css=qo.style,qo.renderedCss=qo.renderedStyle,qo.removeBypass=qo.removeCss=qo.removeStyle,qo.pstyle=qo.parsedStyle;var es={};function ts(e){return function(){var t=arguments,n=[];if(2===t.length){var r=t[0],i=t[1];this.on(e.event,r,i)}else if(1===t.length&&v(t[0])){var a=t[0];this.on(e.event,a)}else if(0===t.length||1===t.length&&y(t[0])){for(var o=1===t.length?t[0]:null,s=0;s<this.length;s++){var l=this[s],u=!e.ableField||l._private[e.ableField],c=l._private[e.field]!=e.value;if(e.overrideAble){var h=e.overrideAble(l);if(void 0!==h&&(u=h,!h))return this}u&&(l._private[e.field]=e.value,c&&n.push(l))}var d=this.spawn(n);d.updateStyle(),d.emit(e.event),o&&d.emit(o)}return this}}function ns(e){es[e.field]=function(){var t=this[0];if(t){if(e.overrideField){var n=e.overrideField(t);if(void 0!==n)return n}return t._private[e.field]}},es[e.on]=ts({event:e.on,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:!0}),es[e.off]=ts({event:e.off,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:!1})}ns({field:"locked",overrideField:function(e){return!!e.cy().autolock()||void 0},on:"lock",off:"unlock"}),ns({field:"grabbable",overrideField:function(e){return!e.cy().autoungrabify()&&!e.pannable()&&void 0},on:"grabify",off:"ungrabify"}),ns({field:"selected",ableField:"selectable",overrideAble:function(e){return!e.cy().autounselectify()&&void 0},on:"select",off:"unselect"}),ns({field:"selectable",overrideField:function(e){return!e.cy().autounselectify()&&void 0},on:"selectify",off:"unselectify"}),es.deselect=es.unselect,es.grabbed=function(){var e=this[0];if(e)return e._private.grabbed},ns({field:"active",on:"activate",off:"unactivate"}),ns({field:"pannable",on:"panify",off:"unpanify"}),es.inactive=function(){var e=this[0];if(e)return!e._private.active};var rs={},is=function(e){return function(t){for(var n=[],r=0;r<this.length;r++){var i=this[r];if(i.isNode()){for(var a=!1,o=i.connectedEdges(),s=0;s<o.length;s++){var l=o[s],u=l.source(),c=l.target();if(e.noIncomingEdges&&c===i&&u!==i||e.noOutgoingEdges&&u===i&&c!==i){a=!0;break}}a||n.push(i)}}return this.spawn(n,!0).filter(t)}},as=function(e){return function(t){for(var n=[],r=0;r<this.length;r++){var i=this[r];if(i.isNode())for(var a=i.connectedEdges(),o=0;o<a.length;o++){var s=a[o],l=s.source(),u=s.target();e.outgoing&&l===i?(n.push(s),n.push(u)):e.incoming&&u===i&&(n.push(s),n.push(l))}}return this.spawn(n,!0).filter(t)}},os=function(e){return function(t){for(var n=this,r=[],i={};;){var a=e.outgoing?n.outgoers():n.incomers();if(0===a.length)break;for(var o=!1,s=0;s<a.length;s++){var l=a[s],u=l.id();i[u]||(i[u]=!0,r.push(l),o=!0)}if(!o)break;n=a}return this.spawn(r,!0).filter(t)}};function ss(e){return function(t){for(var n=[],r=0;r<this.length;r++){var i=this[r]._private[e.attr];i&&n.push(i)}return this.spawn(n,!0).filter(t)}}function ls(e){return function(t){var n=[],r=this._private.cy,i=e||{};f(t)&&(t=r.$(t));for(var a=0;a<t.length;a++)for(var o=t[a]._private.edges,s=0;s<o.length;s++){var l=o[s],u=l._private.data,c=this.hasElementWithId(u.source)&&t.hasElementWithId(u.target),h=t.hasElementWithId(u.source)&&this.hasElementWithId(u.target);if(c||h){if(i.thisIsSrc||i.thisIsTgt){if(i.thisIsSrc&&!c)continue;if(i.thisIsTgt&&!h)continue}n.push(l)}}return this.spawn(n,!0)}}function us(e){return e=z({},{codirected:!1},e),function(t){for(var n=[],r=this.edges(),i=e,a=0;a<r.length;a++)for(var o=r[a]._private,s=o.source,l=s._private.data.id,u=o.data.target,c=s._private.edges,h=0;h<c.length;h++){var d=c[h],p=d._private.data,g=p.target,f=p.source,v=g===u&&f===l,y=l===g&&u===f;(i.codirected&&v||!i.codirected&&(v||y))&&n.push(d)}return this.spawn(n,!0).filter(t)}}rs.clearTraversalCache=function(){for(var e=0;e<this.length;e++)this[e]._private.traversalCache=null},z(rs,{roots:is({noIncomingEdges:!0}),leaves:is({noOutgoingEdges:!0}),outgoers:Ba(as({outgoing:!0}),"outgoers"),successors:os({outgoing:!0}),incomers:Ba(as({incoming:!0}),"incomers"),predecessors:os({incoming:!0})}),z(rs,{neighborhood:Ba((function(e){for(var t=[],n=this.nodes(),r=0;r<n.length;r++)for(var i=n[r],a=i.connectedEdges(),o=0;o<a.length;o++){var s=a[o],l=s.source(),u=s.target(),c=i===l?u:l;c.length>0&&t.push(c[0]),t.push(s[0])}return this.spawn(t,!0).filter(e)}),"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}}),rs.neighbourhood=rs.neighborhood,rs.closedNeighbourhood=rs.closedNeighborhood,rs.openNeighbourhood=rs.openNeighborhood,z(rs,{source:Ba((function(e){var t,n=this[0];return n&&(t=n._private.source||n.cy().collection()),t&&e?t.filter(e):t}),"source"),target:Ba((function(e){var t,n=this[0];return n&&(t=n._private.target||n.cy().collection()),t&&e?t.filter(e):t}),"target"),sources:ss({attr:"source"}),targets:ss({attr:"target"})}),z(rs,{edgesWith:Ba(ls(),"edgesWith"),edgesTo:Ba(ls({thisIsSrc:!0}),"edgesTo")}),z(rs,{connectedEdges:Ba((function(e){for(var t=[],n=0;n<this.length;n++){var r=this[n];if(r.isNode())for(var i=r._private.edges,a=0;a<i.length;a++){var o=i[a];t.push(o)}}return this.spawn(t,!0).filter(e)}),"connectedEdges"),connectedNodes:Ba((function(e){for(var t=[],n=0;n<this.length;n++){var r=this[n];r.isEdge()&&(t.push(r.source()[0]),t.push(r.target()[0]))}return this.spawn(t,!0).filter(e)}),"connectedNodes"),parallelEdges:Ba(us(),"parallelEdges"),codirectedEdges:Ba(us({codirected:!0}),"codirectedEdges")}),z(rs,{components:function(e){var t=this,n=t.cy(),r=n.collection(),i=null==e?t.nodes():e.nodes(),a=[];null!=e&&i.empty()&&(i=e.sources());var o=function(e,t){r.merge(e),i.unmerge(e),t.merge(e)};if(i.empty())return t.spawn();var s=function(){var e=n.collection();a.push(e);var r=i[0];o(r,e),t.bfs({directed:!1,roots:r,visit:function(t){return o(t,e)}}),e.forEach((function(n){n.connectedEdges().forEach((function(n){t.has(n)&&e.has(n.source())&&e.has(n.target())&&e.merge(n)}))}))};do{s()}while(i.length>0);return a},component:function(){var e=this[0];return e.cy().mutableElements().components(e)[0]}}),rs.componentsOf=rs.components;var cs=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(void 0!==e){var i=new tt,a=!1;if(t){if(t.length>0&&m(t[0])&&!E(t[0])){a=!0;for(var o=[],s=new rt,l=0,u=t.length;l<u;l++){var c=t[l];null==c.data&&(c.data={});var h=c.data;if(null==h.id)h.id=He();else if(e.hasElementWithId(h.id)||s.has(h.id))continue;var d=new it(e,c,!1);o.push(d),s.add(h.id)}t=o}}else t=[];this.length=0;for(var p=0,g=t.length;p<g;p++){var f=t[p][0];if(null!=f){var v=f._private.data.id;n&&i.has(v)||(n&&i.set(v,{index:this.length,ele:f}),this[this.length]=f,this.length++)}}this._private={eles:this,cy:e,get map(){return null==this.lazyMap&&this.rebuildMap(),this.lazyMap},set map(e){this.lazyMap=e},rebuildMap:function(){for(var e=this.lazyMap=new tt,t=this.eles,n=0;n<t.length;n++){var r=t[n];e.set(r.id(),{index:n,ele:r})}}},n&&(this._private.map=i),a&&!r&&this.restore()}else Ve("A collection must have a reference to the core")},hs=it.prototype=cs.prototype=Object.create(Array.prototype);hs.instanceString=function(){return"collection"},hs.spawn=function(e,t){return new cs(this.cy(),e,t)},hs.spawnSelf=function(){return this.spawn(this)},hs.cy=function(){return this._private.cy},hs.renderer=function(){return this._private.cy.renderer()},hs.element=function(){return this[0]},hs.collection=function(){return _(this)?this:new cs(this._private.cy,[this])},hs.unique=function(){return new cs(this._private.cy,this,!0)},hs.hasElementWithId=function(e){return e=""+e,this._private.map.has(e)},hs.getElementById=function(e){e=""+e;var t=this._private.cy,n=this._private.map.get(e);return n?n.ele:new cs(t)},hs.$id=hs.getElementById,hs.poolIndex=function(){var e=this._private.cy._private.elements,t=this[0]._private.data.id;return e._private.map.get(t).index},hs.indexOf=function(e){var t=e[0]._private.data.id;return this._private.map.get(t).index},hs.indexOfId=function(e){return e=""+e,this._private.map.get(e).index},hs.json=function(e){var t=this.element(),n=this.cy();if(null==t&&e)return this;if(null!=t){var r=t._private;if(m(e)){if(n.startBatch(),e.data){t.data(e.data);var i=r.data;if(t.isEdge()){var a=!1,o={},s=e.data.source,l=e.data.target;null!=s&&s!=i.source&&(o.source=""+s,a=!0),null!=l&&l!=i.target&&(o.target=""+l,a=!0),a&&(t=t.move(o))}else{var u="parent"in e.data,c=e.data.parent;!u||null==c&&null==i.parent||c==i.parent||(void 0===c&&(c=null),null!=c&&(c=""+c),t=t.move({parent:c}))}}e.position&&t.position(e.position);var h=function(n,i,a){var o=e[n];null!=o&&o!==r[n]&&(o?t[i]():t[a]())};return h("removed","remove","restore"),h("selected","select","unselect"),h("selectable","selectify","unselectify"),h("locked","lock","unlock"),h("grabbable","grabify","ungrabify"),h("pannable","panify","unpanify"),null!=e.classes&&t.classes(e.classes),n.endBatch(),this}if(void 0===e){var d={data:qe(r.data),position:qe(r.position),group:r.group,removed:r.removed,selected:r.selected,selectable:r.selectable,locked:r.locked,grabbable:r.grabbable,pannable:r.pannable,classes:null};d.classes="";var p=0;return r.classes.forEach((function(e){return d.classes+=0==p++?e:" "+e})),d}}},hs.jsons=function(){for(var e=[],t=0;t<this.length;t++){var n=this[t].json();e.push(n)}return e},hs.clone=function(){for(var e=this.cy(),t=[],n=0;n<this.length;n++){var r=this[n].json(),i=new it(e,r,!1);t.push(i)}return new cs(e,t)},hs.copy=hs.clone,hs.restore=function(){for(var e,t,n=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this,a=i.cy(),o=a._private,s=[],l=[],u=0,c=i.length;u<c;u++){var h=i[u];r&&!h.removed()||(h.isNode()?s.push(h):l.push(h))}e=s.concat(l);var d=function(){e.splice(t,1),t--};for(t=0;t<e.length;t++){var p=e[t],g=p._private,v=g.data;if(p.clearTraversalCache(),r||g.removed)if(void 0===v.id)v.id=He();else if(b(v.id))v.id=""+v.id;else{if(C(v.id)||!f(v.id)){Ve("Can not create element with invalid string ID `"+v.id+"`"),d();continue}if(a.hasElementWithId(v.id)){Ve("Can not create second element with ID `"+v.id+"`"),d();continue}}var y=v.id;if(p.isNode()){var m=g.position;null==m.x&&(m.x=0),null==m.y&&(m.y=0)}if(p.isEdge()){for(var x=p,w=["source","target"],E=w.length,_=!1,T=0;T<E;T++){var D=w[T],N=v[D];b(N)&&(N=v[D]=""+v[D]),null==N||""===N?(Ve("Can not create edge `"+y+"` with unspecified "+D),_=!0):a.hasElementWithId(N)||(Ve("Can not create edge `"+y+"` with nonexistant "+D+" `"+N+"`"),_=!0)}if(_){d();continue}var A=a.getElementById(v.source),L=a.getElementById(v.target);A.same(L)?A._private.edges.push(x):(A._private.edges.push(x),L._private.edges.push(x)),x._private.source=A,x._private.target=L}g.map=new tt,g.map.set(y,{ele:p,index:0}),g.removed=!1,r&&a.addToPool(p)}for(var k=0;k<s.length;k++){var S=s[k],I=S._private.data;b(I.parent)&&(I.parent=""+I.parent);var M=I.parent;if(null!=M||S._private.parent){var O=S._private.parent?a.collection().merge(S._private.parent):a.getElementById(M);if(O.empty())I.parent=void 0;else if(O[0].removed())je("Node added with missing parent, reference to parent removed"),I.parent=void 0,S._private.parent=null;else{for(var P=!1,R=O;!R.empty();){if(S.same(R)){P=!0,I.parent=void 0;break}R=R.parent()}P||(O[0]._private.children.push(S),S._private.parent=O[0],o.hasCompoundNodes=!0)}}}if(e.length>0){for(var B=e.length===i.length?i:new cs(a,e),F=0;F<B.length;F++){var z=B[F];z.isNode()||(z.parallelEdges().clearTraversalCache(),z.source().clearTraversalCache(),z.target().clearTraversalCache())}(o.hasCompoundNodes?a.collection().merge(B).merge(B.connectedNodes()).merge(B.parent()):B).dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(n),n?B.emitAndNotify("add"):r&&B.emit("add")}return i},hs.removed=function(){var e=this[0];return e&&e._private.removed},hs.inside=function(){var e=this[0];return e&&!e._private.removed},hs.remove=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this,r=[],i={},a=n._private.cy;function o(e){var n=i[e.id()];t&&e.removed()||n||(i[e.id()]=!0,e.isNode()?(r.push(e),function(e){for(var t=e._private.edges,n=0;n<t.length;n++)o(t[n])}(e),function(e){for(var t=e._private.children,n=0;n<t.length;n++)o(t[n])}(e)):r.unshift(e))}for(var s=0,l=n.length;s<l;s++)o(n[s]);function u(e,t){var n=e._private.edges;Ze(n,t),e.clearTraversalCache()}function c(e){e.clearTraversalCache()}var h=[];function d(e,t){t=t[0];var n=(e=e[0])._private.children,r=e.id();Ze(n,t),t._private.parent=null,h.ids[r]||(h.ids[r]=!0,h.push(e))}h.ids={},n.dirtyCompoundBoundsCache(),t&&a.removeFromPool(r);for(var p=0;p<r.length;p++){var g=r[p];if(g.isEdge()){var f=g.source()[0],v=g.target()[0];u(f,g),u(v,g);for(var y=g.parallelEdges(),m=0;m<y.length;m++){var b=y[m];c(b),b.isBundledBezier()&&b.dirtyBoundingBoxCache()}}else{var x=g.parent();0!==x.length&&d(x,g)}t&&(g._private.removed=!0)}var w=a._private.elements;a._private.hasCompoundNodes=!1;for(var E=0;E<w.length;E++)if(w[E].isParent()){a._private.hasCompoundNodes=!0;break}var _=new cs(this.cy(),r);_.size()>0&&(e?_.emitAndNotify("remove"):t&&_.emit("remove"));for(var T=0;T<h.length;T++){var D=h[T];t&&D.removed()||D.updateStyle()}return _},hs.move=function(e){var t=this._private.cy,n=this,r=!1,i=!1,a=function(e){return null==e?e:""+e};if(void 0!==e.source||void 0!==e.target){var o=a(e.source),s=a(e.target),l=null!=o&&t.hasElementWithId(o),u=null!=s&&t.hasElementWithId(s);(l||u)&&(t.batch((function(){n.remove(r,i),n.emitAndNotify("moveout");for(var e=0;e<n.length;e++){var t=n[e],a=t._private.data;t.isEdge()&&(l&&(a.source=o),u&&(a.target=s))}n.restore(r,i)})),n.emitAndNotify("move"))}else if(void 0!==e.parent){var c=a(e.parent);if(null===c||t.hasElementWithId(c)){var h=null===c?void 0:c;t.batch((function(){var e=n.remove(r,i);e.emitAndNotify("moveout");for(var t=0;t<n.length;t++){var a=n[t],o=a._private.data;a.isNode()&&(o.parent=h)}e.restore(r,i)})),n.emitAndNotify("move")}}return this},[vr,$i,Ki,Oa,Fa,ja,qa,xo,Po,Ro,{isNode:function(){return"nodes"===this.group()},isEdge:function(){return"edges"===this.group()},isLoop:function(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function(){var e=this[0];if(e)return e._private.group}},Go,Xo,qo,es,rs].forEach((function(e){z(hs,e)}));var ds={add:function(e){var t,n=this;if(w(e)){var r=e;if(r._private.cy===n)t=r.restore();else{for(var i=[],a=0;a<r.length;a++){var o=r[a];i.push(o.json())}t=new cs(n,i)}}else if(y(e))t=new cs(n,e);else if(m(e)&&(y(e.nodes)||y(e.edges))){for(var s=e,l=[],u=["nodes","edges"],c=0,h=u.length;c<h;c++){var d=u[c],p=s[d];if(y(p))for(var g=0,f=p.length;g<f;g++){var v=z({group:d},p[g]);l.push(v)}}t=new cs(n,l)}else t=new it(n,e).collection();return t},remove:function(e){if(w(e));else if(f(e)){var t=e;e=this.$(t)}return e.remove()}};function ps(e,t,n,r){var i=4,a=1e-7,o=10,s=11,l=1/(s-1),u="undefined"!=typeof Float32Array;if(4!==arguments.length)return!1;for(var c=0;c<4;++c)if("number"!=typeof arguments[c]||isNaN(arguments[c])||!isFinite(arguments[c]))return!1;e=Math.min(e,1),n=Math.min(n,1),e=Math.max(e,0),n=Math.max(n,0);var h=u?new Float32Array(s):new Array(s);function d(e,t){return 1-3*t+3*e}function p(e,t){return 3*t-6*e}function g(e){return 3*e}function f(e,t,n){return((d(t,n)*e+p(t,n))*e+g(t))*e}function v(e,t,n){return 3*d(t,n)*e*e+2*p(t,n)*e+g(t)}function y(t){for(var r=0,u=1,c=s-1;u!==c&&h[u]<=t;++u)r+=l;--u;var d=r+(t-h[u])/(h[u+1]-h[u])*l,p=v(d,e,n);return p>=.001?function(t,r){for(var a=0;a<i;++a){var o=v(r,e,n);if(0===o)return r;r-=(f(r,e,n)-t)/o}return r}(t,d):0===p?d:function(t,r,i){var s,l,u=0;do{(s=f(l=r+(i-r)/2,e,n)-t)>0?i=l:r=l}while(Math.abs(s)>a&&++u<o);return l}(t,r,r+l)}var m=!1;function b(){m=!0,e===t&&n===r||function(){for(var t=0;t<s;++t)h[t]=f(t*l,e,n)}()}var x=function(i){return m||b(),e===t&&n===r?i:0===i?0:1===i?1:f(y(i),t,r)};x.getControlPoints=function(){return[{x:e,y:t},{x:n,y:r}]};var w="generateBezier("+[e,t,n,r]+")";return x.toString=function(){return w},x}var gs=function(){function e(e){return-e.tension*e.x-e.friction*e.v}function t(t,n,r){var i={x:t.x+r.dx*n,v:t.v+r.dv*n,tension:t.tension,friction:t.friction};return{dx:i.v,dv:e(i)}}function n(n,r){var i={dx:n.v,dv:e(n)},a=t(n,.5*r,i),o=t(n,.5*r,a),s=t(n,r,o),l=1/6*(i.dx+2*(a.dx+o.dx)+s.dx),u=1/6*(i.dv+2*(a.dv+o.dv)+s.dv);return n.x=n.x+l*r,n.v=n.v+u*r,n}return function e(t,r,i){var a,o,s,l={x:-1,v:0,tension:null,friction:null},u=[0],c=0,h=1e-4;for(t=parseFloat(t)||500,r=parseFloat(r)||20,i=i||null,l.tension=t,l.friction=r,o=(a=null!==i)?(c=e(t,r))/i*.016:.016;s=n(s||l,o),u.push(1+s.x),c+=16,Math.abs(s.x)>h&&Math.abs(s.v)>h;);return a?function(e){return u[e*(u.length-1)|0]}:c}}(),fs=function(e,t,n,r){var i=ps(e,t,n,r);return function(e,t,n){return e+(t-e)*i(n)}},vs={linear:function(e,t,n){return e+(t-e)*n},ease:fs(.25,.1,.25,1),"ease-in":fs(.42,0,1,1),"ease-out":fs(0,0,.58,1),"ease-in-out":fs(.42,0,.58,1),"ease-in-sine":fs(.47,0,.745,.715),"ease-out-sine":fs(.39,.575,.565,1),"ease-in-out-sine":fs(.445,.05,.55,.95),"ease-in-quad":fs(.55,.085,.68,.53),"ease-out-quad":fs(.25,.46,.45,.94),"ease-in-out-quad":fs(.455,.03,.515,.955),"ease-in-cubic":fs(.55,.055,.675,.19),"ease-out-cubic":fs(.215,.61,.355,1),"ease-in-out-cubic":fs(.645,.045,.355,1),"ease-in-quart":fs(.895,.03,.685,.22),"ease-out-quart":fs(.165,.84,.44,1),"ease-in-out-quart":fs(.77,0,.175,1),"ease-in-quint":fs(.755,.05,.855,.06),"ease-out-quint":fs(.23,1,.32,1),"ease-in-out-quint":fs(.86,0,.07,1),"ease-in-expo":fs(.95,.05,.795,.035),"ease-out-expo":fs(.19,1,.22,1),"ease-in-out-expo":fs(1,0,0,1),"ease-in-circ":fs(.6,.04,.98,.335),"ease-out-circ":fs(.075,.82,.165,1),"ease-in-out-circ":fs(.785,.135,.15,.86),spring:function(e,t,n){if(0===n)return vs.linear;var r=gs(e,t,n);return function(e,t,n){return e+(t-e)*r(n)}},"cubic-bezier":fs};function ys(e,t,n,r,i){if(1===r)return n;if(t===n)return n;var a=i(t,n,r);return null==e||((e.roundValue||e.color)&&(a=Math.round(a)),void 0!==e.min&&(a=Math.max(a,e.min)),void 0!==e.max&&(a=Math.min(a,e.max))),a}function ms(e,t){return null!=e.pfValue||null!=e.value?null==e.pfValue||null!=t&&"%"===t.type.units?e.value:e.pfValue:e}function bs(e,t,n,r,i){var a=null!=i?i.type:null;n<0?n=0:n>1&&(n=1);var o=ms(e,i),s=ms(t,i);if(b(o)&&b(s))return ys(a,o,s,n,r);if(y(o)&&y(s)){for(var l=[],u=0;u<s.length;u++){var c=o[u],h=s[u];if(null!=c&&null!=h){var d=ys(a,c,h,n,r);l.push(d)}else l.push(h)}return l}}function xs(e,t,n,r){var i=!r,a=e._private,o=t._private,s=o.easing,l=o.startTime,u=(r?e:e.cy()).style();if(!o.easingImpl)if(null==s)o.easingImpl=vs.linear;else{var c,h,d;c=f(s)?u.parse("transition-timing-function",s).value:s,f(c)?(h=c,d=[]):(h=c[1],d=c.slice(2).map((function(e){return+e}))),d.length>0?("spring"===h&&d.push(o.duration),o.easingImpl=vs[h].apply(null,d)):o.easingImpl=vs[h]}var p,g=o.easingImpl;if(p=0===o.duration?1:(n-l)/o.duration,o.applying&&(p=o.progress),p<0?p=0:p>1&&(p=1),null==o.delay){var v=o.startPosition,y=o.position;if(y&&i&&!e.locked()){var m={};ws(v.x,y.x)&&(m.x=bs(v.x,y.x,p,g)),ws(v.y,y.y)&&(m.y=bs(v.y,y.y,p,g)),e.position(m)}var b=o.startPan,x=o.pan,w=a.pan,E=null!=x&&r;E&&(ws(b.x,x.x)&&(w.x=bs(b.x,x.x,p,g)),ws(b.y,x.y)&&(w.y=bs(b.y,x.y,p,g)),e.emit("pan"));var _=o.startZoom,T=o.zoom,D=null!=T&&r;D&&(ws(_,T)&&(a.zoom=Mt(a.minZoom,bs(_,T,p,g),a.maxZoom)),e.emit("zoom")),(E||D)&&e.emit("viewport");var C=o.style;if(C&&C.length>0&&i){for(var N=0;N<C.length;N++){var A=C[N],L=A.name,k=A,S=o.startStyle[L],I=bs(S,k,p,g,u.properties[S.name]);u.overrideBypass(e,L,I)}e.emit("style")}}return o.progress=p,p}function ws(e,t){return!(null==e||null==t||!(b(e)&&b(t)||e&&t))}function Es(e,t,n,r){var i=t._private;i.started=!0,i.startTime=n-i.progress*i.duration}function _s(e,t){var n=t._private.aniEles,r=[];function i(t,n){var i=t._private,a=i.animation.current,o=i.animation.queue,s=!1;if(0===a.length){var l=o.shift();l&&a.push(l)}for(var u=function(e){for(var t=e.length-1;t>=0;t--)(0,e[t])();e.splice(0,e.length)},c=a.length-1;c>=0;c--){var h=a[c],d=h._private;d.stopped?(a.splice(c,1),d.hooked=!1,d.playing=!1,d.started=!1,u(d.frames)):(d.playing||d.applying)&&(d.playing&&d.applying&&(d.applying=!1),d.started||Es(0,h,e),xs(t,h,e,n),d.applying&&(d.applying=!1),u(d.frames),null!=d.step&&d.step(e),h.completed()&&(a.splice(c,1),d.hooked=!1,d.playing=!1,d.started=!1,u(d.completes)),s=!0)}return n||0!==a.length||0!==o.length||r.push(t),s}for(var a=!1,o=0;o<n.length;o++){var s=i(n[o]);a=a||s}var l=i(t,!0);(a||l)&&(n.length>0?t.notify("draw",n):t.notify("draw")),n.unmerge(r),t.emit("step")}var Ts={animate:Wi.animate(),animation:Wi.animation(),animated:Wi.animated(),clearQueue:Wi.clearQueue(),delay:Wi.delay(),delayAnimation:Wi.delayAnimation(),stop:Wi.stop(),addToAnimationPool:function(e){this.styleEnabled()&&this._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){var e=this;if(e._private.animationsRunning=!0,e.styleEnabled()){var t=e.renderer();t&&t.beforeRender?t.beforeRender((function(t,n){_s(n,e)}),t.beforeRenderPriorities.animations):function t(){e._private.animationsRunning&&_e((function(n){_s(n,e),t()}))}()}}},Ds={qualifierCompare:function(e,t){return null==e||null==t?null==e&&null==t:e.sameText(t)},eventMatches:function(e,t,n){var r=t.qualifier;return null==r||e!==n.target&&E(n.target)&&r.matches(n.target)},addEventFields:function(e,t){t.cy=e,t.target=e},callbackContext:function(e,t,n){return null!=t.qualifier?n.target:e}},Cs=function(e){return f(e)?new Ia(e):e},Ns={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new Ao(Ds,this)),this},emitter:function(){return this._private.emitter},on:function(e,t,n){return this.emitter().on(e,Cs(t),n),this},removeListener:function(e,t,n){return this.emitter().removeListener(e,Cs(t),n),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},one:function(e,t,n){return this.emitter().one(e,Cs(t),n),this},once:function(e,t,n){return this.emitter().one(e,Cs(t),n),this},emit:function(e,t){return this.emitter().emit(e,t),this},emitAndNotify:function(e,t){return this.emit(e),this.notify(e,t),this}};Wi.eventAliasesOn(Ns);var As={png:function(e){return e=e||{},this._private.renderer.png(e)},jpg:function(e){var t=this._private.renderer;return(e=e||{}).bg=e.bg||"#fff",t.jpg(e)}};As.jpeg=As.jpg;var Ls={layout:function(e){var t=this;if(null!=e)if(null!=e.name){var n,r=e.name,i=t.extension("layout",r);if(null!=i)return n=f(e.eles)?t.$(e.eles):null!=e.eles?e.eles:t.$(),new i(z({},e,{cy:t,eles:n}));Ve("No such layout `"+r+"` found.  Did you forget to import it and `cytoscape.use()` it?")}else Ve("A `name` must be specified to make a layout");else Ve("Layout options must be specified to make a layout")}};Ls.createLayout=Ls.makeLayout=Ls.layout;var ks={notify:function(e,t){var n=this._private;if(this.batching()){n.batchNotifications=n.batchNotifications||{};var r=n.batchNotifications[e]=n.batchNotifications[e]||this.collection();null!=t&&r.merge(t)}else if(n.notificationsEnabled){var i=this.renderer();!this.destroyed()&&i&&i.notify(e,t)}},notifications:function(e){var t=this._private;return void 0===e?t.notificationsEnabled:(t.notificationsEnabled=!!e,this)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return null==e.batchCount&&(e.batchCount=0),0===e.batchCount&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},endBatch:function(){var e=this._private;if(0===e.batchCount)return this;if(e.batchCount--,0===e.batchCount){e.batchStyleEles.updateStyle();var t=this.renderer();Object.keys(e.batchNotifications).forEach((function(n){var r=e.batchNotifications[n];r.empty()?t.notify(n):t.notify(n,r)}))}return this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var t=this;return this.batch((function(){for(var n=Object.keys(e),r=0;r<n.length;r++){var i=n[r],a=e[i];t.getElementById(i).data(a)}}))}},Ss=Ke({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1}),Is={renderTo:function(e,t,n,r){return this._private.renderer.renderTo(e,t,n,r),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify("draw"),this},resize:function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},initRenderer:function(e){var t=this,n=t.extension("renderer",e.name);if(null!=n){void 0!==e.wheelSensitivity&&je("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var r=Ss(e);r.cy=t,t._private.renderer=new n(r),this.notify("init")}else Ve("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"))},destroyRenderer:function(){var e=this;e.notify("destroy");var t=e.container();if(t)for(t._cyreg=null;t.childNodes.length>0;)t.removeChild(t.childNodes[0]);e._private.renderer=null,e.mutableElements().forEach((function(e){var t=e._private;t.rscratch={},t.rstyle={},t.animation.current=[],t.animation.queue=[]}))},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};Is.invalidateDimensions=Is.resize;var Ms={collection:function(e,t){return f(e)?this.$(e):w(e)?e.collection():y(e)?(t||(t={}),new cs(this,e,t.unique,t.removed)):new cs(this)},nodes:function(e){var t=this.$((function(e){return e.isNode()}));return e?t.filter(e):t},edges:function(e){var t=this.$((function(e){return e.isEdge()}));return e?t.filter(e):t},$:function(e){var t=this._private.elements;return e?t.filter(e):t.spawnSelf()},mutableElements:function(){return this._private.elements}};Ms.elements=Ms.filter=Ms.$;var Os={},Ps="t";Os.apply=function(e){for(var t=this,n=t._private.cy.collection(),r=0;r<e.length;r++){var i=e[r],a=t.getContextMeta(i);if(!a.empty){var o=t.getContextStyle(a),s=t.applyContextStyle(a,o,i);i._private.appliedInitStyle?t.updateTransitions(i,s.diffProps):i._private.appliedInitStyle=!0,t.updateStyleHints(i)&&n.push(i)}}return n},Os.getPropertiesDiff=function(e,t){var n=this,r=n._private.propDiffs=n._private.propDiffs||{},i=e+"-"+t,a=r[i];if(a)return a;for(var o=[],s={},l=0;l<n.length;l++){var u=n[l],c=e[l]===Ps,h=t[l]===Ps,d=c!==h,p=u.mappedProperties.length>0;if(d||h&&p){var g=void 0;d&&p||d?g=u.properties:p&&(g=u.mappedProperties);for(var f=0;f<g.length;f++){for(var v=g[f],y=v.name,m=!1,b=l+1;b<n.length;b++){var x=n[b];if(t[b]===Ps&&(m=null!=x.properties[v.name]))break}s[y]||m||(s[y]=!0,o.push(y))}}}return r[i]=o,o},Os.getContextMeta=function(e){for(var t,n=this,r="",i=e._private.styleCxtKey||"",a=0;a<n.length;a++){var o=n[a];r+=o.selector&&o.selector.matches(e)?Ps:"f"}return t=n.getPropertiesDiff(i,r),e._private.styleCxtKey=r,{key:r,diffPropNames:t,empty:0===t.length}},Os.getContextStyle=function(e){var t=e.key,n=this._private.contextStyles=this._private.contextStyles||{};if(n[t])return n[t];for(var r={_private:{key:t}},i=0;i<this.length;i++){var a=this[i];if(t[i]===Ps)for(var o=0;o<a.properties.length;o++){var s=a.properties[o];r[s.name]=s}}return n[t]=r,r},Os.applyContextStyle=function(e,t,n){for(var r=e.diffPropNames,i={},a=this.types,o=0;o<r.length;o++){var s=r[o],l=t[s],u=n.pstyle(s);if(!l){if(!u)continue;l=u.bypass?{name:s,deleteBypassed:!0}:{name:s,delete:!0}}if(u!==l){if(l.mapped===a.fn&&null!=u&&null!=u.mapping&&u.mapping.value===l.value){var c=u.mapping;if((c.fnValue=l.value(n))===c.prevFnValue)continue}var h=i[s]={prev:u};this.applyParsedProperty(n,l),h.next=n.pstyle(s),h.next&&h.next.bypass&&(h.next=h.next.bypassed)}}return{diffProps:i}},Os.updateStyleHints=function(e){var t=e._private,n=this,r=n.propertyGroupNames,i=n.propertyGroupKeys,a=function(e,t,r){return n.getPropertiesHash(e,t,r)},o=t.styleKey;if(e.removed())return!1;var s,l,u,c="nodes"===t.group,h=e._private.style;r=Object.keys(h);for(var d=0;d<i.length;d++){var p=i[d];t.styleKeys[p]=[De,Ce]}for(var g=function(e,n){return t.styleKeys[n][0]=Ae(e,t.styleKeys[n][0])},f=function(e,n){return t.styleKeys[n][1]=Le(e,t.styleKeys[n][1])},v=function(e,t){g(e,t),f(e,t)},y=function(e,t){for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);g(r,t),f(r,t)}},m=0;m<r.length;m++){var b=r[m],x=h[b];if(null!=x){var w=this.properties[b],E=w.type,_=w.groupKey,T=void 0;null!=w.hashOverride?T=w.hashOverride(e,x):null!=x.pfValue&&(T=x.pfValue);var D=null==w.enums?x.value:null,C=null!=T,N=C||null!=D,A=x.units;E.number&&N&&!E.multiple?(v(-128<(s=C?T:D)&&s<128&&Math.floor(s)!==s?2e9-(1024*s|0):s,_),C||null==A||y(A,_)):y(x.strValue,_)}}for(var L=[De,Ce],k=0;k<i.length;k++){var S=i[k],I=t.styleKeys[S];L[0]=Ae(I[0],L[0]),L[1]=Le(I[1],L[1])}t.styleKey=(l=L[0],u=L[1],2097152*l+u);var M=t.styleKeys;t.labelDimsKey=ke(M.labelDimensions);var O=a(e,["label"],M.labelDimensions);if(t.labelKey=ke(O),t.labelStyleKey=ke(Se(M.commonLabel,O)),!c){var P=a(e,["source-label"],M.labelDimensions);t.sourceLabelKey=ke(P),t.sourceLabelStyleKey=ke(Se(M.commonLabel,P));var R=a(e,["target-label"],M.labelDimensions);t.targetLabelKey=ke(R),t.targetLabelStyleKey=ke(Se(M.commonLabel,R))}if(c){var B=t.styleKeys,F=B.nodeBody,z=B.nodeBorder,G=B.backgroundImage,Y=B.compound,X=B.pie,V=[F,z,G,Y,X].filter((function(e){return null!=e})).reduce(Se,[De,Ce]);t.nodeKey=ke(V),t.hasPie=null!=X&&X[0]!==De&&X[1]!==Ce}return o!==t.styleKey},Os.clearStyleHints=function(e){var t=e._private;t.styleCxtKey="",t.styleKeys={},t.styleKey=null,t.labelKey=null,t.labelStyleKey=null,t.sourceLabelKey=null,t.sourceLabelStyleKey=null,t.targetLabelKey=null,t.targetLabelStyleKey=null,t.nodeKey=null,t.hasPie=null},Os.applyParsedProperty=function(e,t){var n,r=this,i=t,a=e._private.style,o=r.types,s=r.properties[i.name].type,l=i.bypass,u=a[i.name],c=u&&u.bypass,h=e._private,d="mapping",p=function(e){return null==e?null:null!=e.pfValue?e.pfValue:e.value},g=function(){var t=p(u),n=p(i);r.checkTriggers(e,i.name,t,n)};if(i&&"pie"===i.name.substr(0,3)&&je("The pie style properties are deprecated.  Create charts using background images instead."),"curve-style"===t.name&&e.isEdge()&&("bezier"!==t.value&&e.isLoop()||"haystack"===t.value&&(e.source().isParent()||e.target().isParent()))&&(i=t=this.parse(t.name,"bezier",l)),i.delete)return a[i.name]=void 0,g(),!0;if(i.deleteBypassed)return u?!!u.bypass&&(u.bypassed=void 0,g(),!0):(g(),!0);if(i.deleteBypass)return u?!!u.bypass&&(a[i.name]=u.bypassed,g(),!0):(g(),!0);var f=function(){je("Do not assign mappings to elements without corresponding data (i.e. ele `"+e.id()+"` has no mapping for property `"+i.name+"` with data field `"+i.field+"`); try a `["+i.field+"]` selector to limit scope to elements with `"+i.field+"` defined")};switch(i.mapped){case o.mapData:for(var v,y=i.field.split("."),m=h.data,x=0;x<y.length&&m;x++)m=m[y[x]];if(null==m)return f(),!1;if(!b(m))return je("Do not use continuous mappers without specifying numeric data (i.e. `"+i.field+": "+m+"` for `"+e.id()+"` is non-numeric)"),!1;var w=i.fieldMax-i.fieldMin;if((v=0===w?0:(m-i.fieldMin)/w)<0?v=0:v>1&&(v=1),s.color){var E=i.valueMin[0],_=i.valueMax[0],T=i.valueMin[1],D=i.valueMax[1],C=i.valueMin[2],N=i.valueMax[2],A=null==i.valueMin[3]?1:i.valueMin[3],L=null==i.valueMax[3]?1:i.valueMax[3],k=[Math.round(E+(_-E)*v),Math.round(T+(D-T)*v),Math.round(C+(N-C)*v),Math.round(A+(L-A)*v)];n={bypass:i.bypass,name:i.name,value:k,strValue:"rgb("+k[0]+", "+k[1]+", "+k[2]+")"}}else{if(!s.number)return!1;var S=i.valueMin+(i.valueMax-i.valueMin)*v;n=this.parse(i.name,S,i.bypass,d)}if(!n)return f(),!1;n.mapping=i,i=n;break;case o.data:for(var I=i.field.split("."),M=h.data,O=0;O<I.length&&M;O++)M=M[I[O]];if(null!=M&&(n=this.parse(i.name,M,i.bypass,d)),!n)return f(),!1;n.mapping=i,i=n;break;case o.fn:var P=i.value,R=null!=i.fnValue?i.fnValue:P(e);if(i.prevFnValue=R,null==R)return je("Custom function mappers may not return null (i.e. `"+i.name+"` for ele `"+e.id()+"` is null)"),!1;if(!(n=this.parse(i.name,R,i.bypass,d)))return je("Custom function mappers may not return invalid values for the property type (i.e. `"+i.name+"` for ele `"+e.id()+"` is invalid)"),!1;n.mapping=qe(i),i=n;break;case void 0:break;default:return!1}return l?(i.bypassed=c?u.bypassed:u,a[i.name]=i):c?u.bypassed=i:a[i.name]=i,g(),!0},Os.cleanElements=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(this.clearStyleHints(r),r.dirtyCompoundBoundsCache(),r.dirtyBoundingBoxCache(),t)for(var i=r._private.style,a=Object.keys(i),o=0;o<a.length;o++){var s=a[o],l=i[s];null!=l&&(l.bypass?l.bypassed=null:i[s]=null)}else r._private.style={}}},Os.update=function(){this._private.cy.mutableElements().updateStyle()},Os.updateTransitions=function(e,t){var n=this,r=e._private,i=e.pstyle("transition-property").value,a=e.pstyle("transition-duration").pfValue,o=e.pstyle("transition-delay").pfValue;if(i.length>0&&a>0){for(var s={},l=!1,u=0;u<i.length;u++){var c=i[u],h=e.pstyle(c),d=t[c];if(d){var p=d.prev,g=null!=d.next?d.next:h,f=!1,v=void 0,m=1e-6;p&&(b(p.pfValue)&&b(g.pfValue)?(f=g.pfValue-p.pfValue,v=p.pfValue+m*f):b(p.value)&&b(g.value)?(f=g.value-p.value,v=p.value+m*f):y(p.value)&&y(g.value)&&(f=p.value[0]!==g.value[0]||p.value[1]!==g.value[1]||p.value[2]!==g.value[2],v=p.strValue),f&&(s[c]=g.strValue,this.applyBypass(e,c,v),l=!0))}}if(!l)return;r.transitioning=!0,new _r((function(t){o>0?e.delayAnimation(o).play().promise().then(t):t()})).then((function(){return e.animation({style:s,duration:a,easing:e.pstyle("transition-timing-function").value,queue:!1}).play().promise()})).then((function(){n.removeBypasses(e,i),e.emitAndNotify("style"),r.transitioning=!1}))}else r.transitioning&&(this.removeBypasses(e,i),e.emitAndNotify("style"),r.transitioning=!1)},Os.checkTrigger=function(e,t,n,r,i,a){var o=this.properties[t],s=i(o);null!=s&&s(n,r)&&a(o)},Os.checkZOrderTrigger=function(e,t,n,r){var i=this;this.checkTrigger(e,t,n,r,(function(e){return e.triggersZOrder}),(function(){i._private.cy.notify("zorder",e)}))},Os.checkBoundsTrigger=function(e,t,n,r){this.checkTrigger(e,t,n,r,(function(e){return e.triggersBounds}),(function(i){e.dirtyCompoundBoundsCache(),e.dirtyBoundingBoxCache(),!i.triggersBoundsOfParallelBeziers||("curve-style"!==t||"bezier"!==n&&"bezier"!==r)&&("display"!==t||"none"!==n&&"none"!==r)||e.parallelEdges().forEach((function(e){e.isBundledBezier()&&e.dirtyBoundingBoxCache()}))}))},Os.checkTriggers=function(e,t,n,r){e.dirtyStyleCache(),this.checkZOrderTrigger(e,t,n,r),this.checkBoundsTrigger(e,t,n,r)};var Rs={applyBypass:function(e,t,n,r){var i=[];if("*"===t||"**"===t){if(void 0!==n)for(var a=0;a<this.properties.length;a++){var o=this.properties[a].name,s=this.parse(o,n,!0);s&&i.push(s)}}else if(f(t)){var l=this.parse(t,n,!0);l&&i.push(l)}else{if(!m(t))return!1;var u=t;r=n;for(var c=Object.keys(u),h=0;h<c.length;h++){var d=c[h],p=u[d];if(void 0===p&&(p=u[k(d)]),void 0!==p){var g=this.parse(d,p,!0);g&&i.push(g)}}}if(0===i.length)return!1;for(var v=!1,y=0;y<e.length;y++){for(var b=e[y],x={},w=void 0,E=0;E<i.length;E++){var _=i[E];if(r){var T=b.pstyle(_.name);w=x[_.name]={prev:T}}v=this.applyParsedProperty(b,qe(_))||v,r&&(w.next=b.pstyle(_.name))}v&&this.updateStyleHints(b),r&&this.updateTransitions(b,x,true)}return v},overrideBypass:function(e,t,n){t=L(t);for(var r=0;r<e.length;r++){var i=e[r],a=i._private.style[t],o=this.properties[t].type,s=o.color,l=o.mutiple,u=a?null!=a.pfValue?a.pfValue:a.value:null;a&&a.bypass?(a.value=n,null!=a.pfValue&&(a.pfValue=n),a.strValue=s?"rgb("+n.join(",")+")":l?n.join(" "):""+n,this.updateStyleHints(i)):this.applyBypass(i,t,n),this.checkTriggers(i,t,u,n)}},removeAllBypasses:function(e,t){return this.removeBypasses(e,this.propertyNames,t)},removeBypasses:function(e,t,n){for(var r=0;r<e.length;r++){for(var i=e[r],a={},o=0;o<t.length;o++){var s=t[o],l=this.properties[s],u=i.pstyle(l.name);if(u&&u.bypass){var c=this.parse(s,"",!0),h=a[l.name]={prev:u};this.applyParsedProperty(i,c),h.next=i.pstyle(l.name)}}this.updateStyleHints(i),n&&this.updateTransitions(i,a,true)}}},Bs={getEmSizeInPixels:function(){var e=this.containerCss("font-size");return null!=e?parseFloat(e):1},containerCss:function(e){var t=this._private.cy.container();if(l&&t&&l.getComputedStyle)return l.getComputedStyle(t).getPropertyValue(e)}},Fs={getRenderedStyle:function(e,t){return t?this.getStylePropertyValue(e,t,!0):this.getRawStyle(e,!0)},getRawStyle:function(e,t){var n=this;if(e=e[0]){for(var r={},i=0;i<n.properties.length;i++){var a=n.properties[i],o=n.getStylePropertyValue(e,a.name,t);null!=o&&(r[a.name]=o,r[k(a.name)]=o)}return r}},getIndexedStyle:function(e,t,n,r){var i=e.pstyle(t)[n][r];return null!=i?i:e.cy().style().getDefaultProperty(t)[n][0]},getStylePropertyValue:function(e,t,n){if(e=e[0]){var r=this.properties[t];r.alias&&(r=r.pointsTo);var i=r.type,a=e.pstyle(r.name);if(a){var o=a.value,s=a.units,l=a.strValue;if(n&&i.number&&null!=o&&b(o)){var u=e.cy().zoom(),c=function(e){return e*u},h=function(e,t){return c(e)+t},d=y(o);return(d?s.every((function(e){return null!=e})):null!=s)?d?o.map((function(e,t){return h(e,s[t])})).join(" "):h(o,s):d?o.map((function(e){return f(e)?e:""+c(e)})).join(" "):""+c(o)}if(null!=l)return l}return null}},getAnimationStartStyle:function(e,t){for(var n={},r=0;r<t.length;r++){var i=t[r].name,a=e.pstyle(i);void 0!==a&&(a=m(a)?this.parse(i,a.strValue):this.parse(i,a)),a&&(n[i]=a)}return n},getPropsList:function(e){var t=[],n=e,r=this.properties;if(n)for(var i=Object.keys(n),a=0;a<i.length;a++){var o=i[a],s=n[o],l=r[o]||r[L(o)],u=this.parse(l.name,s);u&&t.push(u)}return t},getNonDefaultPropertiesHash:function(e,t,n){var r,i,a,o,s,l,u=n.slice();for(s=0;s<t.length;s++)if(r=t[s],null!=(i=e.pstyle(r,!1)))if(null!=i.pfValue)u[0]=Ae(o,u[0]),u[1]=Le(o,u[1]);else for(a=i.strValue,l=0;l<a.length;l++)o=a.charCodeAt(l),u[0]=Ae(o,u[0]),u[1]=Le(o,u[1]);return u}};Fs.getPropertiesHash=Fs.getNonDefaultPropertiesHash;var zs={appendFromJson:function(e){for(var t=this,n=0;n<e.length;n++){var r=e[n],i=r.selector,a=r.style||r.css,o=Object.keys(a);t.selector(i);for(var s=0;s<o.length;s++){var l=o[s],u=a[l];t.css(l,u)}}return t},fromJson:function(e){var t=this;return t.resetToDefault(),t.appendFromJson(e),t},json:function(){for(var e=[],t=this.defaultLength;t<this.length;t++){for(var n=this[t],r=n.selector,i=n.properties,a={},o=0;o<i.length;o++){var s=i[o];a[s.name]=s.strValue}e.push({selector:r?r.toString():"core",style:a})}return e}},Gs={appendFromString:function(e){var t,n,r,i=this,a=""+e;function o(){a=a.length>t.length?a.substr(t.length):""}function s(){n=n.length>r.length?n.substr(r.length):""}for(a=a.replace(/[/][*](\s|.)+?[*][/]/g,"");!a.match(/^\s*$/);){var l=a.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!l){je("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+a);break}t=l[0];var u=l[1];if("core"!==u&&new Ia(u).invalid)je("Skipping parsing of block: Invalid selector found in string stylesheet: "+u),o();else{var c=l[2],h=!1;n=c;for(var d=[];!n.match(/^\s*$/);){var p=n.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!p){je("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+c),h=!0;break}r=p[0];var g=p[1],f=p[2];this.properties[g]?i.parse(g,f)?(d.push({name:g,val:f}),s()):(je("Skipping property: Invalid property definition in: "+r),s()):(je("Skipping property: Invalid property name in: "+r),s())}if(h){o();break}i.selector(u);for(var v=0;v<d.length;v++){var y=d[v];i.css(y.name,y.val)}o()}}return i},fromString:function(e){var t=this;return t.resetToDefault(),t.appendFromString(e),t}},Ys={};(function(){var e=M,t=P,n=B,r=function(e){return"^"+e+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},i=function(r){var i=e+"|\\w+|"+t+"|"+n+"|\\#[0-9a-fA-F]{3}|\\#[0-9a-fA-F]{6}";return"^"+r+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+e+")\\s*\\,\\s*("+e+")\\s*,\\s*("+i+")\\s*\\,\\s*("+i+")\\)$"},a=["^url\\s*\\(\\s*['\"]?(.+?)['\"]?\\s*\\)$","^(none)$","^(.+)$"];Ys.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi"]},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:r("data")},layoutData:{mapping:!0,regex:r("layoutData")},scratch:{mapping:!0,regex:r("scratch")},mapData:{mapping:!0,regex:i("mapData")},mapLayoutData:{mapping:!0,regex:i("mapLayoutData")},mapScratch:{mapping:!0,regex:i("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:a,singleRegexMatchValue:!0},urls:{regexes:a,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:function(e,t){switch(e.length){case 2:return"deg"!==t[0]&&"rad"!==t[0]&&"deg"!==t[1]&&"rad"!==t[1];case 1:return f(e[0])||"deg"===t[0]||"rad"===t[0];default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+e+")\\s*,\\s*("+e+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+e+")\\s*,\\s*("+e+")\\s*,\\s*("+e+")\\s*,\\s*("+e+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:function(e){var t=e.length;return 1===t||2===t||4===t}}};var o={zeroNonZero:function(e,t){return(null==e||null==t)&&e!==t||0==e&&0!=t||0!=e&&0==t},any:function(e,t){return e!=t},emptyNonEmpty:function(e,t){var n=C(e),r=C(t);return n&&!r||!n&&r}},s=Ys.types,l=[{name:"label",type:s.text,triggersBounds:o.any,triggersZOrder:o.emptyNonEmpty},{name:"text-rotation",type:s.textRotation,triggersBounds:o.any},{name:"text-margin-x",type:s.bidirectionalSize,triggersBounds:o.any},{name:"text-margin-y",type:s.bidirectionalSize,triggersBounds:o.any}],u=[{name:"source-label",type:s.text,triggersBounds:o.any},{name:"source-text-rotation",type:s.textRotation,triggersBounds:o.any},{name:"source-text-margin-x",type:s.bidirectionalSize,triggersBounds:o.any},{name:"source-text-margin-y",type:s.bidirectionalSize,triggersBounds:o.any},{name:"source-text-offset",type:s.size,triggersBounds:o.any}],c=[{name:"target-label",type:s.text,triggersBounds:o.any},{name:"target-text-rotation",type:s.textRotation,triggersBounds:o.any},{name:"target-text-margin-x",type:s.bidirectionalSize,triggersBounds:o.any},{name:"target-text-margin-y",type:s.bidirectionalSize,triggersBounds:o.any},{name:"target-text-offset",type:s.size,triggersBounds:o.any}],h=[{name:"font-family",type:s.fontFamily,triggersBounds:o.any},{name:"font-style",type:s.fontStyle,triggersBounds:o.any},{name:"font-weight",type:s.fontWeight,triggersBounds:o.any},{name:"font-size",type:s.size,triggersBounds:o.any},{name:"text-transform",type:s.textTransform,triggersBounds:o.any},{name:"text-wrap",type:s.textWrap,triggersBounds:o.any},{name:"text-overflow-wrap",type:s.textOverflowWrap,triggersBounds:o.any},{name:"text-max-width",type:s.size,triggersBounds:o.any},{name:"text-outline-width",type:s.size,triggersBounds:o.any},{name:"line-height",type:s.positiveNumber,triggersBounds:o.any}],d=[{name:"text-valign",type:s.valign,triggersBounds:o.any},{name:"text-halign",type:s.halign,triggersBounds:o.any},{name:"color",type:s.color},{name:"text-outline-color",type:s.color},{name:"text-outline-opacity",type:s.zeroOneNumber},{name:"text-background-color",type:s.color},{name:"text-background-opacity",type:s.zeroOneNumber},{name:"text-background-padding",type:s.size,triggersBounds:o.any},{name:"text-border-opacity",type:s.zeroOneNumber},{name:"text-border-color",type:s.color},{name:"text-border-width",type:s.size,triggersBounds:o.any},{name:"text-border-style",type:s.borderStyle,triggersBounds:o.any},{name:"text-background-shape",type:s.textBackgroundShape,triggersBounds:o.any},{name:"text-justification",type:s.justification}],p=[{name:"events",type:s.bool},{name:"text-events",type:s.bool}],g=[{name:"display",type:s.display,triggersZOrder:o.any,triggersBounds:o.any,triggersBoundsOfParallelBeziers:!0},{name:"visibility",type:s.visibility,triggersZOrder:o.any},{name:"opacity",type:s.zeroOneNumber,triggersZOrder:o.zeroNonZero},{name:"text-opacity",type:s.zeroOneNumber},{name:"min-zoomed-font-size",type:s.size},{name:"z-compound-depth",type:s.zCompoundDepth,triggersZOrder:o.any},{name:"z-index-compare",type:s.zIndexCompare,triggersZOrder:o.any},{name:"z-index",type:s.nonNegativeInt,triggersZOrder:o.any}],v=[{name:"overlay-padding",type:s.size,triggersBounds:o.any},{name:"overlay-color",type:s.color},{name:"overlay-opacity",type:s.zeroOneNumber,triggersBounds:o.zeroNonZero},{name:"overlay-shape",type:s.overlayShape,triggersBounds:o.any}],y=[{name:"underlay-padding",type:s.size,triggersBounds:o.any},{name:"underlay-color",type:s.color},{name:"underlay-opacity",type:s.zeroOneNumber,triggersBounds:o.zeroNonZero},{name:"underlay-shape",type:s.overlayShape,triggersBounds:o.any}],m=[{name:"transition-property",type:s.propList},{name:"transition-duration",type:s.time},{name:"transition-delay",type:s.time},{name:"transition-timing-function",type:s.easing}],b=function(e,t){return"label"===t.value?-e.poolIndex():t.pfValue},x=[{name:"height",type:s.nodeSize,triggersBounds:o.any,hashOverride:b},{name:"width",type:s.nodeSize,triggersBounds:o.any,hashOverride:b},{name:"shape",type:s.nodeShape,triggersBounds:o.any},{name:"shape-polygon-points",type:s.polygonPointList,triggersBounds:o.any},{name:"background-color",type:s.color},{name:"background-fill",type:s.fill},{name:"background-opacity",type:s.zeroOneNumber},{name:"background-blacken",type:s.nOneOneNumber},{name:"background-gradient-stop-colors",type:s.colors},{name:"background-gradient-stop-positions",type:s.percentages},{name:"background-gradient-direction",type:s.gradientDirection},{name:"padding",type:s.sizeMaybePercent,triggersBounds:o.any},{name:"padding-relative-to",type:s.paddingRelativeTo,triggersBounds:o.any},{name:"bounds-expansion",type:s.boundsExpansion,triggersBounds:o.any}],w=[{name:"border-color",type:s.color},{name:"border-opacity",type:s.zeroOneNumber},{name:"border-width",type:s.size,triggersBounds:o.any},{name:"border-style",type:s.borderStyle}],E=[{name:"background-image",type:s.urls},{name:"background-image-crossorigin",type:s.bgCrossOrigin},{name:"background-image-opacity",type:s.zeroOneNumbers},{name:"background-image-containment",type:s.bgContainment},{name:"background-image-smoothing",type:s.bools},{name:"background-position-x",type:s.bgPos},{name:"background-position-y",type:s.bgPos},{name:"background-width-relative-to",type:s.bgRelativeTo},{name:"background-height-relative-to",type:s.bgRelativeTo},{name:"background-repeat",type:s.bgRepeat},{name:"background-fit",type:s.bgFit},{name:"background-clip",type:s.bgClip},{name:"background-width",type:s.bgWH},{name:"background-height",type:s.bgWH},{name:"background-offset-x",type:s.bgPos},{name:"background-offset-y",type:s.bgPos}],_=[{name:"position",type:s.position,triggersBounds:o.any},{name:"compound-sizing-wrt-labels",type:s.compoundIncludeLabels,triggersBounds:o.any},{name:"min-width",type:s.size,triggersBounds:o.any},{name:"min-width-bias-left",type:s.sizeMaybePercent,triggersBounds:o.any},{name:"min-width-bias-right",type:s.sizeMaybePercent,triggersBounds:o.any},{name:"min-height",type:s.size,triggersBounds:o.any},{name:"min-height-bias-top",type:s.sizeMaybePercent,triggersBounds:o.any},{name:"min-height-bias-bottom",type:s.sizeMaybePercent,triggersBounds:o.any}],T=[{name:"line-style",type:s.lineStyle},{name:"line-color",type:s.color},{name:"line-fill",type:s.fill},{name:"line-cap",type:s.lineCap},{name:"line-opacity",type:s.zeroOneNumber},{name:"line-dash-pattern",type:s.numbers},{name:"line-dash-offset",type:s.number},{name:"line-gradient-stop-colors",type:s.colors},{name:"line-gradient-stop-positions",type:s.percentages},{name:"curve-style",type:s.curveStyle,triggersBounds:o.any,triggersBoundsOfParallelBeziers:!0},{name:"haystack-radius",type:s.zeroOneNumber,triggersBounds:o.any},{name:"source-endpoint",type:s.edgeEndpoint,triggersBounds:o.any},{name:"target-endpoint",type:s.edgeEndpoint,triggersBounds:o.any},{name:"control-point-step-size",type:s.size,triggersBounds:o.any},{name:"control-point-distances",type:s.bidirectionalSizes,triggersBounds:o.any},{name:"control-point-weights",type:s.numbers,triggersBounds:o.any},{name:"segment-distances",type:s.bidirectionalSizes,triggersBounds:o.any},{name:"segment-weights",type:s.numbers,triggersBounds:o.any},{name:"taxi-turn",type:s.bidirectionalSizeMaybePercent,triggersBounds:o.any},{name:"taxi-turn-min-distance",type:s.size,triggersBounds:o.any},{name:"taxi-direction",type:s.axisDirection,triggersBounds:o.any},{name:"edge-distances",type:s.edgeDistances,triggersBounds:o.any},{name:"arrow-scale",type:s.positiveNumber,triggersBounds:o.any},{name:"loop-direction",type:s.angle,triggersBounds:o.any},{name:"loop-sweep",type:s.angle,triggersBounds:o.any},{name:"source-distance-from-node",type:s.size,triggersBounds:o.any},{name:"target-distance-from-node",type:s.size,triggersBounds:o.any}],D=[{name:"ghost",type:s.bool,triggersBounds:o.any},{name:"ghost-offset-x",type:s.bidirectionalSize,triggersBounds:o.any},{name:"ghost-offset-y",type:s.bidirectionalSize,triggersBounds:o.any},{name:"ghost-opacity",type:s.zeroOneNumber}],N=[{name:"selection-box-color",type:s.color},{name:"selection-box-opacity",type:s.zeroOneNumber},{name:"selection-box-border-color",type:s.color},{name:"selection-box-border-width",type:s.size},{name:"active-bg-color",type:s.color},{name:"active-bg-opacity",type:s.zeroOneNumber},{name:"active-bg-size",type:s.size},{name:"outside-texture-bg-color",type:s.color},{name:"outside-texture-bg-opacity",type:s.zeroOneNumber}],A=[];Ys.pieBackgroundN=16,A.push({name:"pie-size",type:s.sizeMaybePercent});for(var L=1;L<=Ys.pieBackgroundN;L++)A.push({name:"pie-"+L+"-background-color",type:s.color}),A.push({name:"pie-"+L+"-background-size",type:s.percent}),A.push({name:"pie-"+L+"-background-opacity",type:s.zeroOneNumber});var k=[],S=Ys.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:s.arrowShape,triggersBounds:o.any},{name:"arrow-color",type:s.color},{name:"arrow-fill",type:s.arrowFill}].forEach((function(e){S.forEach((function(t){var n=t+"-"+e.name,r=e.type,i=e.triggersBounds;k.push({name:n,type:r,triggersBounds:i})}))}),{});var I=Ys.properties=[].concat(p,m,g,v,y,D,d,h,l,u,c,x,w,E,A,_,T,k,N),O=Ys.propertyGroups={behavior:p,transition:m,visibility:g,overlay:v,underlay:y,ghost:D,commonLabel:d,labelDimensions:h,mainLabel:l,sourceLabel:u,targetLabel:c,nodeBody:x,nodeBorder:w,backgroundImage:E,pie:A,compound:_,edgeLine:T,edgeArrow:k,core:N},R=Ys.propertyGroupNames={};(Ys.propertyGroupKeys=Object.keys(O)).forEach((function(e){R[e]=O[e].map((function(e){return e.name})),O[e].forEach((function(t){return t.groupKey=e}))}));var F=Ys.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];Ys.propertyNames=I.map((function(e){return e.name}));for(var z=0;z<I.length;z++){var G=I[z];I[G.name]=G}for(var Y=0;Y<F.length;Y++){var X=F[Y],V=I[X.pointsTo],U={name:X.name,alias:!0,pointsTo:V};I.push(U),I[X.name]=U}})(),Ys.getDefaultProperty=function(e){return this.getDefaultProperties()[e]},Ys.getDefaultProperties=function(){var e=this._private;if(null!=e.defaultProperties)return e.defaultProperties;for(var t=z({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce((function(e,t){for(var n=1;n<=Ys.pieBackgroundN;n++){var r=t.name.replace("{{i}}",n),i=t.value;e[r]=i}return e}),{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"taxi-turn":"50%","taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"}].reduce((function(e,t){return Ys.arrowPrefixes.forEach((function(n){var r=n+"-"+t.name,i=t.value;e[r]=i})),e}),{})),n={},r=0;r<this.properties.length;r++){var i=this.properties[r];if(!i.pointsTo){var a=i.name,o=t[a],s=this.parse(a,o);n[a]=s}}return e.defaultProperties=n,e.defaultProperties},Ys.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var Xs={parse:function(e,t,n,r){var i=this;if(v(t))return i.parseImplWarn(e,t,n,r);var a,o=Me(e,""+t,n?"t":"f","mapping"===r||!0===r||!1===r||null==r?"dontcare":r),s=i.propCache=i.propCache||[];return(a=s[o])||(a=s[o]=i.parseImplWarn(e,t,n,r)),(n||"mapping"===r)&&(a=qe(a))&&(a.value=qe(a.value)),a},parseImplWarn:function(e,t,n,r){var i=this.parseImpl(e,t,n,r);return i||null==t||je("The style property `".concat(e,": ").concat(t,"` is invalid")),!i||"width"!==i.name&&"height"!==i.name||"label"!==t||je("The style value of `label` is deprecated for `"+i.name+"`"),i},parseImpl:function(e,t,n,r){var i=this;e=L(e);var a=i.properties[e],o=t,s=i.types;if(!a)return null;if(void 0===t)return null;a.alias&&(a=a.pointsTo,e=a.name);var l=f(t);l&&(t=t.trim());var u,c,h=a.type;if(!h)return null;if(n&&(""===t||null===t))return{name:e,value:t,bypass:!0,deleteBypass:!0};if(v(t))return{name:e,value:t,strValue:"fn",mapped:s.fn,bypass:n};if(!l||r||t.length<7||"a"!==t[1]);else{if(t.length>=7&&"d"===t[0]&&(u=new RegExp(s.data.regex).exec(t))){if(n)return!1;var d=s.data;return{name:e,value:u,strValue:""+t,mapped:d,field:u[1],bypass:n}}if(t.length>=10&&"m"===t[0]&&(c=new RegExp(s.mapData.regex).exec(t))){if(n)return!1;if(h.multiple)return!1;var p=s.mapData;if(!h.color&&!h.number)return!1;var g=this.parse(e,c[4]);if(!g||g.mapped)return!1;var m=this.parse(e,c[5]);if(!m||m.mapped)return!1;if(g.pfValue===m.pfValue||g.strValue===m.strValue)return je("`"+e+": "+t+"` is not a valid mapper because the output range is zero; converting to `"+e+": "+g.strValue+"`"),this.parse(e,g.strValue);if(h.color){var x=g.value,w=m.value;if(!(x[0]!==w[0]||x[1]!==w[1]||x[2]!==w[2]||x[3]!==w[3]&&(null!=x[3]&&1!==x[3]||null!=w[3]&&1!==w[3])))return!1}return{name:e,value:c,strValue:""+t,mapped:p,field:c[1],fieldMin:parseFloat(c[2]),fieldMax:parseFloat(c[3]),valueMin:g.value,valueMax:m.value,bypass:n}}}if(h.multiple&&"multiple"!==r){var E;if(E=l?t.split(/\s+/):y(t)?t:[t],h.evenMultiple&&E.length%2!=0)return null;for(var _=[],T=[],D=[],C="",N=!1,A=0;A<E.length;A++){var k=i.parse(e,E[A],n,"multiple");N=N||f(k.value),_.push(k.value),D.push(null!=k.pfValue?k.pfValue:k.value),T.push(k.units),C+=(A>0?" ":"")+k.strValue}return h.validate&&!h.validate(_,T)?null:h.singleEnum&&N?1===_.length&&f(_[0])?{name:e,value:_[0],strValue:_[0],bypass:n}:null:{name:e,value:_,pfValue:D,strValue:C,bypass:n,units:T}}var S,I,O=function(){for(var r=0;r<h.enums.length;r++)if(h.enums[r]===t)return{name:e,value:t,strValue:""+t,bypass:n};return null};if(h.number){var P,R="px";if(h.units&&(P=h.units),h.implicitUnits&&(R=h.implicitUnits),!h.unitless)if(l){var B="px|em"+(h.allowPercent?"|\\%":"");P&&(B=P);var F=t.match("^("+M+")("+B+")?$");F&&(t=F[1],P=F[2]||R)}else P&&!h.implicitUnits||(P=R);if(t=parseFloat(t),isNaN(t)&&void 0===h.enums)return null;if(isNaN(t)&&void 0!==h.enums)return t=o,O();if(h.integer&&(!b(I=t)||Math.floor(I)!==I))return null;if(void 0!==h.min&&(t<h.min||h.strictMin&&t===h.min)||void 0!==h.max&&(t>h.max||h.strictMax&&t===h.max))return null;var z={name:e,value:t,strValue:""+t+(P||""),units:P,bypass:n};return h.unitless||"px"!==P&&"em"!==P?z.pfValue=t:z.pfValue="px"!==P&&P?this.getEmSizeInPixels()*t:t,"ms"!==P&&"s"!==P||(z.pfValue="ms"===P?t:1e3*t),"deg"!==P&&"rad"!==P||(z.pfValue="rad"===P?t:(S=t,Math.PI*S/180)),"%"===P&&(z.pfValue=t/100),z}if(h.propList){var Y=[],X=""+t;if("none"===X);else{for(var V=X.split(/\s*,\s*|\s+/),U=0;U<V.length;U++){var j=V[U].trim();i.properties[j]?Y.push(j):je("`"+j+"` is not a valid property name")}if(0===Y.length)return null}return{name:e,value:Y,strValue:0===Y.length?"none":Y.join(" "),bypass:n}}if(h.color){var q=G(t);return q?{name:e,value:q,pfValue:q,strValue:"rgb("+q[0]+","+q[1]+","+q[2]+")",bypass:n}:null}if(h.regex||h.regexes){if(h.enums){var H=O();if(H)return H}for(var W=h.regexes?h.regexes:[h.regex],$=0;$<W.length;$++){var K=new RegExp(W[$]).exec(t);if(K)return{name:e,value:h.singleRegexMatchValue?K[1]:K,strValue:""+t,bypass:n}}return null}return h.string?{name:e,value:""+t,strValue:""+t,bypass:n}:h.enums?O():null}},Vs=function e(t){if(!(this instanceof e))return new e(t);T(t)?(this._private={cy:t,coreStyle:{}},this.length=0,this.resetToDefault()):Ve("A style must have a core reference")},Us=Vs.prototype;Us.instanceString=function(){return"style"},Us.clear=function(){for(var e=this._private,t=e.cy.elements(),n=0;n<this.length;n++)this[n]=void 0;return this.length=0,e.contextStyles={},e.propDiffs={},this.cleanElements(t,!0),t.forEach((function(e){var t=e[0]._private;t.styleDirty=!0,t.appliedInitStyle=!1})),this},Us.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this},Us.core=function(e){return this._private.coreStyle[e]||this.getDefaultProperty(e)},Us.selector=function(e){var t="core"===e?null:new Ia(e),n=this.length++;return this[n]={selector:t,properties:[],mappedProperties:[],index:n},this},Us.css=function(){var e=arguments;if(1===e.length)for(var t=e[0],n=0;n<this.properties.length;n++){var r=this.properties[n],i=t[r.name];void 0===i&&(i=t[k(r.name)]),void 0!==i&&this.cssRule(r.name,i)}else 2===e.length&&this.cssRule(e[0],e[1]);return this},Us.style=Us.css,Us.cssRule=function(e,t){var n=this.parse(e,t);if(n){var r=this.length-1;this[r].properties.push(n),this[r].properties[n.name]=n,n.name.match(/pie-(\d+)-background-size/)&&n.value&&(this._private.hasPie=!0),n.mapped&&this[r].mappedProperties.push(n),!this[r].selector&&(this._private.coreStyle[n.name]=n)}return this},Us.append=function(e){return D(e)?e.appendToStyle(this):y(e)?this.appendFromJson(e):f(e)&&this.appendFromString(e),this},Vs.fromJson=function(e,t){var n=new Vs(e);return n.fromJson(t),n},Vs.fromString=function(e,t){return new Vs(e).fromString(t)},[Os,Rs,Bs,Fs,zs,Gs,Ys,Xs].forEach((function(e){z(Us,e)})),Vs.types=Us.types,Vs.properties=Us.properties,Vs.propertyGroups=Us.propertyGroups,Vs.propertyGroupNames=Us.propertyGroupNames,Vs.propertyGroupKeys=Us.propertyGroupKeys;var js={style:function(e){return e&&this.setStyle(e).update(),this._private.style},setStyle:function(e){var t=this._private;return D(e)?t.style=e.generateStyle(this):y(e)?t.style=Vs.fromJson(this,e):f(e)?t.style=Vs.fromString(this,e):t.style=Vs(this),t.style},updateStyle:function(){this.mutableElements().updateStyle()}},qs={autolock:function(e){return void 0===e?this._private.autolock:(this._private.autolock=!!e,this)},autoungrabify:function(e){return void 0===e?this._private.autoungrabify:(this._private.autoungrabify=!!e,this)},autounselectify:function(e){return void 0===e?this._private.autounselectify:(this._private.autounselectify=!!e,this)},selectionType:function(e){var t=this._private;return null==t.selectionType&&(t.selectionType="single"),void 0===e?t.selectionType:("additive"!==e&&"single"!==e||(t.selectionType=e),this)},panningEnabled:function(e){return void 0===e?this._private.panningEnabled:(this._private.panningEnabled=!!e,this)},userPanningEnabled:function(e){return void 0===e?this._private.userPanningEnabled:(this._private.userPanningEnabled=!!e,this)},zoomingEnabled:function(e){return void 0===e?this._private.zoomingEnabled:(this._private.zoomingEnabled=!!e,this)},userZoomingEnabled:function(e){return void 0===e?this._private.userZoomingEnabled:(this._private.userZoomingEnabled=!!e,this)},boxSelectionEnabled:function(e){return void 0===e?this._private.boxSelectionEnabled:(this._private.boxSelectionEnabled=!!e,this)},pan:function(){var e,t,n,r,i,a=arguments,o=this._private.pan;switch(a.length){case 0:return o;case 1:if(f(a[0]))return o[e=a[0]];if(m(a[0])){if(!this._private.panningEnabled)return this;r=(n=a[0]).x,i=n.y,b(r)&&(o.x=r),b(i)&&(o.y=i),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;t=a[1],"x"!==(e=a[0])&&"y"!==e||!b(t)||(o[e]=t),this.emit("pan viewport")}return this.notify("viewport"),this},panBy:function(e,t){var n,r,i,a,o,s=arguments,l=this._private.pan;if(!this._private.panningEnabled)return this;switch(s.length){case 1:m(e)&&(a=(i=s[0]).x,o=i.y,b(a)&&(l.x+=a),b(o)&&(l.y+=o),this.emit("pan viewport"));break;case 2:r=t,"x"!==(n=e)&&"y"!==n||!b(r)||(l[n]+=r),this.emit("pan viewport")}return this.notify("viewport"),this},fit:function(e,t){var n=this.getFitViewport(e,t);if(n){var r=this._private;r.zoom=n.zoom,r.pan=n.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},getFitViewport:function(e,t){if(b(e)&&void 0===t&&(t=e,e=void 0),this._private.panningEnabled&&this._private.zoomingEnabled){var n;if(f(e)){var r=e;e=this.$(r)}else if(m(l=e)&&b(l.x1)&&b(l.x2)&&b(l.y1)&&b(l.y2)){var i=e;(n={x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2}).w=n.x2-n.x1,n.h=n.y2-n.y1}else w(e)||(e=this.mutableElements());if(!w(e)||!e.empty()){n=n||e.boundingBox();var a,o=this.width(),s=this.height();if(t=b(t)?t:0,!isNaN(o)&&!isNaN(s)&&o>0&&s>0&&!isNaN(n.w)&&!isNaN(n.h)&&n.w>0&&n.h>0)return{zoom:a=(a=(a=Math.min((o-2*t)/n.w,(s-2*t)/n.h))>this._private.maxZoom?this._private.maxZoom:a)<this._private.minZoom?this._private.minZoom:a,pan:{x:(o-a*(n.x1+n.x2))/2,y:(s-a*(n.y1+n.y2))/2}}}}var l},zoomRange:function(e,t){var n=this._private;if(null==t){var r=e;e=r.min,t=r.max}return b(e)&&b(t)&&e<=t?(n.minZoom=e,n.maxZoom=t):b(e)&&void 0===t&&e<=n.maxZoom?n.minZoom=e:b(t)&&void 0===e&&t>=n.minZoom&&(n.maxZoom=t),this},minZoom:function(e){return void 0===e?this._private.minZoom:this.zoomRange({min:e})},maxZoom:function(e){return void 0===e?this._private.maxZoom:this.zoomRange({max:e})},getZoomedViewport:function(e){var t,n,r=this._private,i=r.pan,a=r.zoom,o=!1;if(r.zoomingEnabled||(o=!0),b(e)?n=e:m(e)&&(n=e.level,null!=e.position?t=Et(e.position,a,i):null!=e.renderedPosition&&(t=e.renderedPosition),null==t||r.panningEnabled||(o=!0)),n=(n=n>r.maxZoom?r.maxZoom:n)<r.minZoom?r.minZoom:n,o||!b(n)||n===a||null!=t&&(!b(t.x)||!b(t.y)))return null;if(null!=t){var s=i,l=a,u=n;return{zoomed:!0,panned:!0,zoom:u,pan:{x:-u/l*(t.x-s.x)+t.x,y:-u/l*(t.y-s.y)+t.y}}}return{zoomed:!0,panned:!1,zoom:n,pan:i}},zoom:function(e){if(void 0===e)return this._private.zoom;var t=this.getZoomedViewport(e),n=this._private;return null!=t&&t.zoomed?(n.zoom=t.zoom,t.panned&&(n.pan.x=t.pan.x,n.pan.y=t.pan.y),this.emit("zoom"+(t.panned?" pan":"")+" viewport"),this.notify("viewport"),this):this},viewport:function(e){var t=this._private,n=!0,r=!0,i=[],a=!1,o=!1;if(!e)return this;if(b(e.zoom)||(n=!1),m(e.pan)||(r=!1),!n&&!r)return this;if(n){var s=e.zoom;s<t.minZoom||s>t.maxZoom||!t.zoomingEnabled?a=!0:(t.zoom=s,i.push("zoom"))}if(r&&(!a||!e.cancelOnFailedZoom)&&t.panningEnabled){var l=e.pan;b(l.x)&&(t.pan.x=l.x,o=!1),b(l.y)&&(t.pan.y=l.y,o=!1),o||i.push("pan")}return i.length>0&&(i.push("viewport"),this.emit(i.join(" ")),this.notify("viewport")),this},center:function(e){var t=this.getCenterPan(e);return t&&(this._private.pan=t,this.emit("pan viewport"),this.notify("viewport")),this},getCenterPan:function(e,t){if(this._private.panningEnabled){if(f(e)){var n=e;e=this.mutableElements().filter(n)}else w(e)||(e=this.mutableElements());if(0!==e.length){var r=e.boundingBox(),i=this.width(),a=this.height();return{x:(i-(t=void 0===t?this._private.zoom:t)*(r.x1+r.x2))/2,y:(a-t*(r.y1+r.y2))/2}}}},reset:function(){return this._private.panningEnabled&&this._private.zoomingEnabled?(this.viewport({pan:{x:0,y:0},zoom:1}),this):this},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e,t,n=this._private,r=n.container;return n.sizeCache=n.sizeCache||(r?(e=l.getComputedStyle(r),t=function(t){return parseFloat(e.getPropertyValue(t))},{width:r.clientWidth-t("padding-left")-t("padding-right"),height:r.clientHeight-t("padding-top")-t("padding-bottom")}):{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,t=this._private.zoom,n=this.renderedExtent(),r={x1:(n.x1-e.x)/t,x2:(n.x2-e.x)/t,y1:(n.y1-e.y)/t,y2:(n.y2-e.y)/t};return r.w=r.x2-r.x1,r.h=r.y2-r.y1,r},renderedExtent:function(){var e=this.width(),t=this.height();return{x1:0,y1:0,x2:e,y2:t,w:e,h:t}},multiClickDebounceTime:function(e){return e?(this._private.multiClickDebounceTime=e,this):this._private.multiClickDebounceTime}};qs.centre=qs.center,qs.autolockNodes=qs.autolock,qs.autoungrabifyNodes=qs.autoungrabify;var Hs={data:Wi.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:Wi.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:Wi.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Wi.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};Hs.attr=Hs.data,Hs.removeAttr=Hs.removeData;var Ws=function(e){var t=this,n=(e=z({},e)).container;n&&!x(n)&&x(n[0])&&(n=n[0]);var r=n?n._cyreg:null;(r=r||{})&&r.cy&&(r.cy.destroy(),r={});var i=r.readies=r.readies||[];n&&(n._cyreg=r),r.cy=t;var a=void 0!==l&&void 0!==n&&!e.headless,o=e;o.layout=z({name:a?"grid":"null"},o.layout),o.renderer=z({name:a?"canvas":"null"},o.renderer);var s=function(e,t,n){return void 0!==t?t:void 0!==n?n:e},u=this._private={container:n,ready:!1,options:o,elements:new cs(this),listeners:[],aniEles:new cs(this),data:o.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:s(!0,o.zoomingEnabled),userZoomingEnabled:s(!0,o.userZoomingEnabled),panningEnabled:s(!0,o.panningEnabled),userPanningEnabled:s(!0,o.userPanningEnabled),boxSelectionEnabled:s(!0,o.boxSelectionEnabled),autolock:s(!1,o.autolock,o.autolockNodes),autoungrabify:s(!1,o.autoungrabify,o.autoungrabifyNodes),autounselectify:s(!1,o.autounselectify),styleEnabled:void 0===o.styleEnabled?a:o.styleEnabled,zoom:b(o.zoom)?o.zoom:1,pan:{x:m(o.pan)&&b(o.pan.x)?o.pan.x:0,y:m(o.pan)&&b(o.pan.y)?o.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:s(250,o.multiClickDebounceTime)};this.createEmitter(),this.selectionType(o.selectionType),this.zoomRange({min:o.minZoom,max:o.maxZoom});u.styleEnabled&&t.setStyle([]);var c=z({},o,o.renderer);t.initRenderer(c);!function(e,t){if(e.some(N))return _r.all(e).then(t);t(e)}([o.style,o.elements],(function(e){var n=e[0],a=e[1];u.styleEnabled&&t.style().append(n),function(e,n,r){t.notifications(!1);var i=t.mutableElements();i.length>0&&i.remove(),null!=e&&(m(e)||y(e))&&t.add(e),t.one("layoutready",(function(e){t.notifications(!0),t.emit(e),t.one("load",n),t.emitAndNotify("load")})).one("layoutstop",(function(){t.one("done",r),t.emit("done")}));var a=z({},t._private.options.layout);a.eles=t.elements(),t.layout(a).run()}(a,(function(){t.startAnimationLoop(),u.ready=!0,v(o.ready)&&t.on("ready",o.ready);for(var e=0;e<i.length;e++){var n=i[e];t.on("ready",n)}r&&(r.readies=[]),t.emit("ready")}),o.done)}))},$s=Ws.prototype;z($s,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},destroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){var e=this;if(!e.destroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return this._private.renderer.isHeadless()},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container||null},mount:function(e){if(null!=e){var t=this,n=t._private,r=n.options;return!x(e)&&x(e[0])&&(e=e[0]),t.stopAnimationLoop(),t.destroyRenderer(),n.container=e,n.styleEnabled=!0,t.invalidateSize(),t.initRenderer(z({},r,r.renderer,{name:"null"===r.renderer.name?"canvas":r.renderer.name})),t.startAnimationLoop(),t.style(r.style),t.emit("mount"),t}},unmount:function(){var e=this;return e.stopAnimationLoop(),e.destroyRenderer(),e.initRenderer({name:"null"}),e.emit("unmount"),e},options:function(){return qe(this._private.options)},json:function(e){var t=this,n=t._private,r=t.mutableElements();if(m(e)){if(t.startBatch(),e.elements){var i={},a=function(e,n){for(var r=[],a=[],o=0;o<e.length;o++){var s=e[o];if(s.data.id){var l=""+s.data.id,u=t.getElementById(l);i[l]=!0,0!==u.length?a.push({ele:u,json:s}):n?(s.group=n,r.push(s)):r.push(s)}else je("cy.json() cannot handle elements without an ID attribute")}t.add(r);for(var c=0;c<a.length;c++){var h=a[c],d=h.ele,p=h.json;d.json(p)}};if(y(e.elements))a(e.elements);else for(var o=["nodes","edges"],s=0;s<o.length;s++){var l=o[s],u=e.elements[l];y(u)&&a(u,l)}var c=t.collection();r.filter((function(e){return!i[e.id()]})).forEach((function(e){e.isParent()?c.merge(e):e.remove()})),c.forEach((function(e){return e.children().move({parent:null})})),c.forEach((function(e){return function(e){return t.getElementById(e.id())}(e).remove()}))}e.style&&t.style(e.style),null!=e.zoom&&e.zoom!==n.zoom&&t.zoom(e.zoom),e.pan&&(e.pan.x===n.pan.x&&e.pan.y===n.pan.y||t.pan(e.pan)),e.data&&t.data(e.data);for(var h=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],d=0;d<h.length;d++){var p=h[d];null!=e[p]&&t[p](e[p])}return t.endBatch(),this}var g={};e?g.elements=this.elements().map((function(e){return e.json()})):(g.elements={},r.forEach((function(e){var t=e.group();g.elements[t]||(g.elements[t]=[]),g.elements[t].push(e.json())}))),this._private.styleEnabled&&(g.style=t.style().json()),g.data=qe(t.data());var f=n.options;return g.zoomingEnabled=n.zoomingEnabled,g.userZoomingEnabled=n.userZoomingEnabled,g.zoom=n.zoom,g.minZoom=n.minZoom,g.maxZoom=n.maxZoom,g.panningEnabled=n.panningEnabled,g.userPanningEnabled=n.userPanningEnabled,g.pan=qe(n.pan),g.boxSelectionEnabled=n.boxSelectionEnabled,g.renderer=qe(f.renderer),g.hideEdgesOnViewport=f.hideEdgesOnViewport,g.textureOnViewport=f.textureOnViewport,g.wheelSensitivity=f.wheelSensitivity,g.motionBlur=f.motionBlur,g.multiClickDebounceTime=f.multiClickDebounceTime,g}}),$s.$id=$s.getElementById,[ds,Ts,Ns,As,Ls,ks,Is,Ms,js,qs,Hs].forEach((function(e){z($s,e)}));var Ks={fit:!0,directed:!1,padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}},Zs={maximal:!1,acyclic:!1},Qs=function(e){return e.scratch("breadthfirst")},Js=function(e,t){return e.scratch("breadthfirst",t)};function el(e){this.options=z({},Ks,Zs,e)}el.prototype.run=function(){var e,t=this.options,n=t,r=t.cy,i=n.eles,a=i.nodes().filter((function(e){return!e.isParent()})),o=i,s=n.directed,l=n.acyclic||n.maximal||n.maximalAdjustments>0,u=Ot(n.boundingBox?n.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()});if(w(n.roots))e=n.roots;else if(y(n.roots)){for(var c=[],h=0;h<n.roots.length;h++){var d=n.roots[h],p=r.getElementById(d);c.push(p)}e=r.collection(c)}else if(f(n.roots))e=r.$(n.roots);else if(s)e=a.roots();else{var g=i.components();e=r.collection();for(var v=function(t){var n=g[t],r=n.maxDegree(!1),i=n.filter((function(e){return e.degree(!1)===r}));e=e.add(i)},m=0;m<g.length;m++)v(m)}var b=[],x={},E=function(e,t){null==b[t]&&(b[t]=[]);var n=b[t].length;b[t].push(e),Js(e,{index:n,depth:t})};o.bfs({roots:e,directed:n.directed,visit:function(e,t,n,r,i){var a=e[0],o=a.id();E(a,i),x[o]=!0}});for(var _=[],T=0;T<a.length;T++){var D=a[T];x[D.id()]||_.push(D)}var C=function(e){for(var t=b[e],n=0;n<t.length;n++){var r=t[n];null!=r?Js(r,{depth:e,index:n}):(t.splice(n,1),n--)}},N=function(){for(var e=0;e<b.length;e++)C(e)},A=function(e,t){for(var r=Qs(e),a=e.incomers().filter((function(e){return e.isNode()&&i.has(e)})),o=-1,s=e.id(),l=0;l<a.length;l++){var u=a[l],c=Qs(u);o=Math.max(o,c.depth)}if(r.depth<=o){if(!n.acyclic&&t[s])return null;var h=o+1;return function(e,t){var n=Qs(e),r=n.depth,i=n.index;b[r][i]=null,E(e,t)}(e,h),t[s]=h,!0}return!1};if(s&&l){var L=[],k={},S=function(e){return L.push(e)};for(a.forEach((function(e){return L.push(e)}));L.length>0;){var I=L.shift(),M=A(I,k);if(M)I.outgoers().filter((function(e){return e.isNode()&&i.has(e)})).forEach(S);else if(null===M){je("Detected double maximal shift for node `"+I.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}N();var O=0;if(n.avoidOverlap)for(var P=0;P<a.length;P++){var R=a[P].layoutDimensions(n),B=R.w,z=R.h;O=Math.max(O,B,z)}var G={},Y=function(e){if(G[e.id()])return G[e.id()];for(var t=Qs(e).depth,n=e.neighborhood(),r=0,i=0,o=0;o<n.length;o++){var s=n[o];if(!s.isEdge()&&!s.isParent()&&a.has(s)){var l=Qs(s);if(null!=l){var u=l.index,c=l.depth;if(null!=u&&null!=c){var h=b[c].length;c<t&&(r+=u/h,i++)}}}}return r/=i=Math.max(1,i),0===i&&(r=0),G[e.id()]=r,r},X=function(e,t){var n=Y(e)-Y(t);return 0===n?F(e.id(),t.id()):n};void 0!==n.depthSort&&(X=n.depthSort);for(var V=0;V<b.length;V++)b[V].sort(X),C(V);for(var U=[],j=0;j<_.length;j++)U.push(_[j]);b.unshift(U),N();for(var q=0,H=0;H<b.length;H++)q=Math.max(b[H].length,q);var W=u.x1+u.w/2,$=u.x1+u.h/2,K=b.reduce((function(e,t){return Math.max(e,t.length)}),0);return i.nodes().layoutPositions(this,n,(function(e){var t=Qs(e),r=t.depth,i=t.index,a=b[r].length,o=Math.max(u.w/((n.grid?K:a)+1),O),s=Math.max(u.h/(b.length+1),O),l=Math.min(u.w/2/b.length,u.h/2/b.length);if(l=Math.max(l,O),n.circle){var c=l*r+l-(b.length>0&&b[0].length<=3?l/2:0),h=2*Math.PI/b[r].length*i;return 0===r&&1===b[0].length&&(c=1),{x:W+c*Math.cos(h),y:$+c*Math.sin(h)}}return{x:W+(i+1-(a+1)/2)*o,y:(r+1)*s}})),this};var tl={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:1.5*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function nl(e){this.options=z({},tl,e)}nl.prototype.run=function(){var e=this.options,t=e,n=e.cy,r=t.eles,i=void 0!==t.counterclockwise?!t.counterclockwise:t.clockwise,a=r.nodes().not(":parent");t.sort&&(a=a.sort(t.sort));for(var o,s=Ot(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()}),l={x:s.x1+s.w/2,y:s.y1+s.h/2},u=(void 0===t.sweep?2*Math.PI-2*Math.PI/a.length:t.sweep)/Math.max(1,a.length-1),c=0,h=0;h<a.length;h++){var d=a[h].layoutDimensions(t),p=d.w,g=d.h;c=Math.max(c,p,g)}if(o=b(t.radius)?t.radius:a.length<=1?0:Math.min(s.h,s.w)/2-c,a.length>1&&t.avoidOverlap){c*=1.75;var f=Math.cos(u)-Math.cos(0),v=Math.sin(u)-Math.sin(0),y=Math.sqrt(c*c/(f*f+v*v));o=Math.max(y,o)}return r.nodes().layoutPositions(this,t,(function(e,n){var r=t.startAngle+n*u*(i?1:-1),a=o*Math.cos(r),s=o*Math.sin(r);return{x:l.x+a,y:l.y+s}})),this};var rl,il={fit:!0,padding:30,startAngle:1.5*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function al(e){this.options=z({},il,e)}al.prototype.run=function(){for(var e=this.options,t=e,n=void 0!==t.counterclockwise?!t.counterclockwise:t.clockwise,r=e.cy,i=t.eles,a=i.nodes().not(":parent"),o=Ot(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()}),s={x:o.x1+o.w/2,y:o.y1+o.h/2},l=[],u=0,c=0;c<a.length;c++){var h,d=a[c];h=t.concentric(d),l.push({value:h,node:d}),d._private.scratch.concentric=h}a.updateStyle();for(var p=0;p<a.length;p++){var g=a[p].layoutDimensions(t);u=Math.max(u,g.w,g.h)}l.sort((function(e,t){return t.value-e.value}));for(var f=t.levelWidth(a),v=[[]],y=v[0],m=0;m<l.length;m++){var b=l[m];y.length>0&&Math.abs(y[0].value-b.value)>=f&&(y=[],v.push(y)),y.push(b)}var x=u+t.minNodeSpacing;if(!t.avoidOverlap){var w=v.length>0&&v[0].length>1,E=(Math.min(o.w,o.h)/2-x)/(v.length+w?1:0);x=Math.min(x,E)}for(var _=0,T=0;T<v.length;T++){var D=v[T],C=void 0===t.sweep?2*Math.PI-2*Math.PI/D.length:t.sweep,N=D.dTheta=C/Math.max(1,D.length-1);if(D.length>1&&t.avoidOverlap){var A=Math.cos(N)-Math.cos(0),L=Math.sin(N)-Math.sin(0),k=Math.sqrt(x*x/(A*A+L*L));_=Math.max(k,_)}D.r=_,_+=x}if(t.equidistant){for(var S=0,I=0,M=0;M<v.length;M++){var O=v[M].r-I;S=Math.max(S,O)}I=0;for(var P=0;P<v.length;P++){var R=v[P];0===P&&(I=R.r),R.r=I,I+=S}}for(var B={},F=0;F<v.length;F++)for(var z=v[F],G=z.dTheta,Y=z.r,X=0;X<z.length;X++){var V=z[X],U=t.startAngle+(n?1:-1)*G*X,j={x:s.x+Y*Math.cos(U),y:s.y+Y*Math.sin(U)};B[V.node.id()]=j}return i.nodes().layoutPositions(this,t,(function(e){var t=e.id();return B[t]})),this};var ol={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,t){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function sl(e){this.options=z({},ol,e),this.options.layout=this}sl.prototype.run=function(){var e=this.options,t=e.cy,n=this;n.stopped=!1,!0!==e.animate&&!1!==e.animate||n.emit({type:"layoutstart",layout:n}),rl=!0===e.debug;var r=ll(t,n,e);rl&&undefined(r),e.randomize&&hl(r);var i=Te(),a=function(){pl(r,t,e),!0===e.fit&&t.fit(e.padding)},o=function(t){return!(n.stopped||t>=e.numIter||(gl(r,e),r.temperature=r.temperature*e.coolingFactor,r.temperature<e.minTemp))},s=function(){if(!0===e.animate||!1===e.animate)a(),n.one("layoutstop",e.stop),n.emit({type:"layoutstop",layout:n});else{var t=e.eles.nodes(),i=dl(r,e,t);t.layoutPositions(n,e,i)}},l=0,u=!0;if(!0===e.animate)!function t(){for(var n=0;u&&n<e.refresh;)u=o(l),l++,n++;u?(Te()-i>=e.animationThreshold&&a(),_e(t)):(Cl(r,e),s())}();else{for(;u;)u=o(l),l++;Cl(r,e),s()}return this},sl.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this},sl.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var ll=function(e,t,n){for(var r=n.eles.edges(),i=n.eles.nodes(),a=Ot(n.boundingBox?n.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),o={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:r.size(),temperature:n.initialTemp,clientWidth:a.w,clientHeight:a.h,boundingBox:a},s=n.eles.components(),l={},u=0;u<s.length;u++)for(var c=s[u],h=0;h<c.length;h++)l[c[h].id()]=u;for(u=0;u<o.nodeSize;u++){var d=(m=i[u]).layoutDimensions(n);(O={}).isLocked=m.locked(),O.id=m.data("id"),O.parentId=m.data("parent"),O.cmptId=l[m.id()],O.children=[],O.positionX=m.position("x"),O.positionY=m.position("y"),O.offsetX=0,O.offsetY=0,O.height=d.w,O.width=d.h,O.maxX=O.positionX+O.width/2,O.minX=O.positionX-O.width/2,O.maxY=O.positionY+O.height/2,O.minY=O.positionY-O.height/2,O.padLeft=parseFloat(m.style("padding")),O.padRight=parseFloat(m.style("padding")),O.padTop=parseFloat(m.style("padding")),O.padBottom=parseFloat(m.style("padding")),O.nodeRepulsion=v(n.nodeRepulsion)?n.nodeRepulsion(m):n.nodeRepulsion,o.layoutNodes.push(O),o.idToIndex[O.id]=u}var p=[],g=0,f=-1,y=[];for(u=0;u<o.nodeSize;u++){var m,b=(m=o.layoutNodes[u]).parentId;null!=b?o.layoutNodes[o.idToIndex[b]].children.push(m.id):(p[++f]=m.id,y.push(m.id))}for(o.graphSet.push(y);g<=f;){var x=p[g++],w=o.idToIndex[x],E=o.layoutNodes[w].children;if(E.length>0)for(o.graphSet.push(E),u=0;u<E.length;u++)p[++f]=E[u]}for(u=0;u<o.graphSet.length;u++){var _=o.graphSet[u];for(h=0;h<_.length;h++){var T=o.idToIndex[_[h]];o.indexToGraph[T]=u}}for(u=0;u<o.edgeSize;u++){var D=r[u],C={};C.id=D.data("id"),C.sourceId=D.data("source"),C.targetId=D.data("target");var N=v(n.idealEdgeLength)?n.idealEdgeLength(D):n.idealEdgeLength,A=v(n.edgeElasticity)?n.edgeElasticity(D):n.edgeElasticity,L=o.idToIndex[C.sourceId],k=o.idToIndex[C.targetId];if(o.indexToGraph[L]!=o.indexToGraph[k]){for(var S=ul(C.sourceId,C.targetId,o),I=o.graphSet[S],M=0,O=o.layoutNodes[L];-1===I.indexOf(O.id);)O=o.layoutNodes[o.idToIndex[O.parentId]],M++;for(O=o.layoutNodes[k];-1===I.indexOf(O.id);)O=o.layoutNodes[o.idToIndex[O.parentId]],M++;N*=M*n.nestingFactor}C.idealLength=N,C.elasticity=A,o.layoutEdges.push(C)}return o},ul=function(e,t,n){var r=cl(e,t,0,n);return 2>r.count?0:r.graph},cl=function e(t,n,r,i){var a=i.graphSet[r];if(-1<a.indexOf(t)&&-1<a.indexOf(n))return{count:2,graph:r};for(var o=0,s=0;s<a.length;s++){var l=a[s],u=i.idToIndex[l],c=i.layoutNodes[u].children;if(0!==c.length){var h=e(t,n,i.indexToGraph[i.idToIndex[c[0]]],i);if(0!==h.count){if(1!==h.count)return h;if(2==++o)break}}}return{count:o,graph:r}},hl=function(e,t){for(var n=e.clientWidth,r=e.clientHeight,i=0;i<e.nodeSize;i++){var a=e.layoutNodes[i];0!==a.children.length||a.isLocked||(a.positionX=Math.random()*n,a.positionY=Math.random()*r)}},dl=function(e,t,n){var r=e.boundingBox,i={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return t.boundingBox&&(n.forEach((function(t){var n=e.layoutNodes[e.idToIndex[t.data("id")]];i.x1=Math.min(i.x1,n.positionX),i.x2=Math.max(i.x2,n.positionX),i.y1=Math.min(i.y1,n.positionY),i.y2=Math.max(i.y2,n.positionY)})),i.w=i.x2-i.x1,i.h=i.y2-i.y1),function(n,a){var o=e.layoutNodes[e.idToIndex[n.data("id")]];if(t.boundingBox){var s=(o.positionX-i.x1)/i.w,l=(o.positionY-i.y1)/i.h;return{x:r.x1+s*r.w,y:r.y1+l*r.h}}return{x:o.positionX,y:o.positionY}}},pl=function(e,t,n){var r=n.layout,i=n.eles.nodes(),a=dl(e,n,i);i.positions(a),!0!==e.ready&&(e.ready=!0,r.one("layoutready",n.ready),r.emit({type:"layoutready",layout:this}))},gl=function(e,t,n){fl(e,t),xl(e),wl(e,t),El(e),_l(e)},fl=function(e,t){for(var n=0;n<e.graphSet.length;n++)for(var r=e.graphSet[n],i=r.length,a=0;a<i;a++)for(var o=e.layoutNodes[e.idToIndex[r[a]]],s=a+1;s<i;s++){var l=e.layoutNodes[e.idToIndex[r[s]]];yl(o,l,e,t)}},vl=function(e){return-e+2*e*Math.random()},yl=function(e,t,n,r){if(e.cmptId===t.cmptId||n.isCompound){var i=t.positionX-e.positionX,a=t.positionY-e.positionY;0===i&&0===a&&(i=vl(1),a=vl(1));var o=ml(e,t,i,a);if(o>0)var s=(u=r.nodeOverlap*o)*i/(f=Math.sqrt(i*i+a*a)),l=u*a/f;else{var u,c=bl(e,i,a),h=bl(t,-1*i,-1*a),d=h.x-c.x,p=h.y-c.y,g=d*d+p*p,f=Math.sqrt(g);s=(u=(e.nodeRepulsion+t.nodeRepulsion)/g)*d/f,l=u*p/f}e.isLocked||(e.offsetX-=s,e.offsetY-=l),t.isLocked||(t.offsetX+=s,t.offsetY+=l)}},ml=function(e,t,n,r){if(n>0)var i=e.maxX-t.minX;else i=t.maxX-e.minX;if(r>0)var a=e.maxY-t.minY;else a=t.maxY-e.minY;return i>=0&&a>=0?Math.sqrt(i*i+a*a):0},bl=function(e,t,n){var r=e.positionX,i=e.positionY,a=e.height||1,o=e.width||1,s=n/t,l=a/o,u={};return 0===t&&0<n||0===t&&0>n?(u.x=r,u.y=i+a/2,u):0<t&&-1*l<=s&&s<=l?(u.x=r+o/2,u.y=i+o*n/2/t,u):0>t&&-1*l<=s&&s<=l?(u.x=r-o/2,u.y=i-o*n/2/t,u):0<n&&(s<=-1*l||s>=l)?(u.x=r+a*t/2/n,u.y=i+a/2,u):0>n&&(s<=-1*l||s>=l)?(u.x=r-a*t/2/n,u.y=i-a/2,u):u},xl=function(e,t){for(var n=0;n<e.edgeSize;n++){var r=e.layoutEdges[n],i=e.idToIndex[r.sourceId],a=e.layoutNodes[i],o=e.idToIndex[r.targetId],s=e.layoutNodes[o],l=s.positionX-a.positionX,u=s.positionY-a.positionY;if(0!==l||0!==u){var c=bl(a,l,u),h=bl(s,-1*l,-1*u),d=h.x-c.x,p=h.y-c.y,g=Math.sqrt(d*d+p*p),f=Math.pow(r.idealLength-g,2)/r.elasticity;if(0!==g)var v=f*d/g,y=f*p/g;else v=0,y=0;a.isLocked||(a.offsetX+=v,a.offsetY+=y),s.isLocked||(s.offsetX-=v,s.offsetY-=y)}}},wl=function(e,t){if(0!==t.gravity)for(var n=0;n<e.graphSet.length;n++){var r=e.graphSet[n],i=r.length;if(0===n)var a=e.clientHeight/2,o=e.clientWidth/2;else{var s=e.layoutNodes[e.idToIndex[r[0]]],l=e.layoutNodes[e.idToIndex[s.parentId]];a=l.positionX,o=l.positionY}for(var u=0;u<i;u++){var c=e.layoutNodes[e.idToIndex[r[u]]];if(!c.isLocked){var h=a-c.positionX,d=o-c.positionY,p=Math.sqrt(h*h+d*d);if(p>1){var g=t.gravity*h/p,f=t.gravity*d/p;c.offsetX+=g,c.offsetY+=f}}}}},El=function(e,t){var n=[],r=0,i=-1;for(n.push.apply(n,e.graphSet[0]),i+=e.graphSet[0].length;r<=i;){var a=n[r++],o=e.idToIndex[a],s=e.layoutNodes[o],l=s.children;if(0<l.length&&!s.isLocked){for(var u=s.offsetX,c=s.offsetY,h=0;h<l.length;h++){var d=e.layoutNodes[e.idToIndex[l[h]]];d.offsetX+=u,d.offsetY+=c,n[++i]=l[h]}s.offsetX=0,s.offsetY=0}}},_l=function(e,t){for(var n=0;n<e.nodeSize;n++)0<(i=e.layoutNodes[n]).children.length&&(i.maxX=void 0,i.minX=void 0,i.maxY=void 0,i.minY=void 0);for(n=0;n<e.nodeSize;n++)if(!(0<(i=e.layoutNodes[n]).children.length||i.isLocked)){var r=Tl(i.offsetX,i.offsetY,e.temperature);i.positionX+=r.x,i.positionY+=r.y,i.offsetX=0,i.offsetY=0,i.minX=i.positionX-i.width,i.maxX=i.positionX+i.width,i.minY=i.positionY-i.height,i.maxY=i.positionY+i.height,Dl(i,e)}for(n=0;n<e.nodeSize;n++){var i;0<(i=e.layoutNodes[n]).children.length&&!i.isLocked&&(i.positionX=(i.maxX+i.minX)/2,i.positionY=(i.maxY+i.minY)/2,i.width=i.maxX-i.minX,i.height=i.maxY-i.minY)}},Tl=function(e,t,n){var r=Math.sqrt(e*e+t*t);if(r>n)var i={x:n*e/r,y:n*t/r};else i={x:e,y:t};return i},Dl=function e(t,n){var r=t.parentId;if(null!=r){var i=n.layoutNodes[n.idToIndex[r]],a=!1;return(null==i.maxX||t.maxX+i.padRight>i.maxX)&&(i.maxX=t.maxX+i.padRight,a=!0),(null==i.minX||t.minX-i.padLeft<i.minX)&&(i.minX=t.minX-i.padLeft,a=!0),(null==i.maxY||t.maxY+i.padBottom>i.maxY)&&(i.maxY=t.maxY+i.padBottom,a=!0),(null==i.minY||t.minY-i.padTop<i.minY)&&(i.minY=t.minY-i.padTop,a=!0),a?e(i,n):void 0}},Cl=function(e,t){for(var n=e.layoutNodes,r=[],i=0;i<n.length;i++){var a=n[i],o=a.cmptId;(r[o]=r[o]||[]).push(a)}var s=0;for(i=0;i<r.length;i++)if(f=r[i]){f.x1=1/0,f.x2=-1/0,f.y1=1/0,f.y2=-1/0;for(var l=0;l<f.length;l++){var u=f[l];f.x1=Math.min(f.x1,u.positionX-u.width/2),f.x2=Math.max(f.x2,u.positionX+u.width/2),f.y1=Math.min(f.y1,u.positionY-u.height/2),f.y2=Math.max(f.y2,u.positionY+u.height/2)}f.w=f.x2-f.x1,f.h=f.y2-f.y1,s+=f.w*f.h}r.sort((function(e,t){return t.w*t.h-e.w*e.h}));var c=0,h=0,d=0,p=0,g=Math.sqrt(s)*e.clientWidth/e.clientHeight;for(i=0;i<r.length;i++){var f;if(f=r[i]){for(l=0;l<f.length;l++)(u=f[l]).isLocked||(u.positionX+=c-f.x1,u.positionY+=h-f.y1);c+=f.w+t.componentSpacing,d+=f.w+t.componentSpacing,p=Math.max(p,f.h),d>g&&(h+=p+t.componentSpacing,c=0,d=0,p=0)}}},Nl={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Al(e){this.options=z({},Nl,e)}Al.prototype.run=function(){var e=this.options,t=e,n=e.cy,r=t.eles,i=r.nodes().not(":parent");t.sort&&(i=i.sort(t.sort));var a=Ot(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()});if(0===a.h||0===a.w)r.nodes().layoutPositions(this,t,(function(e){return{x:a.x1,y:a.y1}}));else{var o=i.size(),s=Math.sqrt(o*a.h/a.w),l=Math.round(s),u=Math.round(a.w/a.h*s),c=function(e){if(null==e)return Math.min(l,u);Math.min(l,u)==l?l=e:u=e},h=function(e){if(null==e)return Math.max(l,u);Math.max(l,u)==l?l=e:u=e},d=t.rows,p=null!=t.cols?t.cols:t.columns;if(null!=d&&null!=p)l=d,u=p;else if(null!=d&&null==p)l=d,u=Math.ceil(o/l);else if(null==d&&null!=p)u=p,l=Math.ceil(o/u);else if(u*l>o){var g=c(),f=h();(g-1)*f>=o?c(g-1):(f-1)*g>=o&&h(f-1)}else for(;u*l<o;){var v=c(),y=h();(y+1)*v>=o?h(y+1):c(v+1)}var m=a.w/u,b=a.h/l;if(t.condense&&(m=0,b=0),t.avoidOverlap)for(var x=0;x<i.length;x++){var w=i[x],E=w._private.position;null!=E.x&&null!=E.y||(E.x=0,E.y=0);var _=w.layoutDimensions(t),T=t.avoidOverlapPadding,D=_.w+T,C=_.h+T;m=Math.max(m,D),b=Math.max(b,C)}for(var N={},A=function(e,t){return!!N["c-"+e+"-"+t]},L=function(e,t){N["c-"+e+"-"+t]=!0},k=0,S=0,I=function(){++S>=u&&(S=0,k++)},M={},O=0;O<i.length;O++){var P=i[O],R=t.position(P);if(R&&(void 0!==R.row||void 0!==R.col)){var B={row:R.row,col:R.col};if(void 0===B.col)for(B.col=0;A(B.row,B.col);)B.col++;else if(void 0===B.row)for(B.row=0;A(B.row,B.col);)B.row++;M[P.id()]=B,L(B.row,B.col)}}i.layoutPositions(this,t,(function(e,t){var n,r;if(e.locked()||e.isParent())return!1;var i=M[e.id()];if(i)n=i.col*m+m/2+a.x1,r=i.row*b+b/2+a.y1;else{for(;A(k,S);)I();n=S*m+m/2+a.x1,r=k*b+b/2+a.y1,L(k,S),I()}return{x:n,y:r}}))}return this};var Ll={ready:function(){},stop:function(){}};function kl(e){this.options=z({},Ll,e)}kl.prototype.run=function(){var e=this.options,t=e.eles,n=this;return e.cy,n.emit("layoutstart"),t.nodes().positions((function(){return{x:0,y:0}})),n.one("layoutready",e.ready),n.emit("layoutready"),n.one("layoutstop",e.stop),n.emit("layoutstop"),this},kl.prototype.stop=function(){return this};var Sl={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Il(e){this.options=z({},Sl,e)}Il.prototype.run=function(){var e=this.options,t=e.eles.nodes(),n=v(e.positions);return t.layoutPositions(this,e,(function(t,r){var i=function(t){if(null==e.positions)return function(e){return{x:e.x,y:e.y}}(t.position());if(n)return e.positions(t);var r=e.positions[t._private.data.id];return null==r?null:r}(t);return!t.locked()&&null!=i&&i})),this};var Ml={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Ol(e){this.options=z({},Ml,e)}Ol.prototype.run=function(){var e=this.options,t=e.cy,n=e.eles,r=Ot(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});return n.nodes().layoutPositions(this,e,(function(e,t){return{x:r.x1+Math.round(Math.random()*r.w),y:r.y1+Math.round(Math.random()*r.h)}})),this};var Pl=[{name:"breadthfirst",impl:el},{name:"circle",impl:nl},{name:"concentric",impl:al},{name:"cose",impl:sl},{name:"grid",impl:Al},{name:"null",impl:kl},{name:"preset",impl:Il},{name:"random",impl:Ol}];function Rl(e){this.options=e,this.notifications=0}var Bl=function(){},Fl=function(){throw new Error("A headless instance can not render images")};Rl.prototype={recalculateRenderedStyle:Bl,notify:function(){this.notifications++},init:Bl,isHeadless:function(){return!0},png:Fl,jpg:Fl};var zl={arrowShapeWidth:.3,registerArrowShapes:function(){var e=this.arrowShapes={},t=this,n=function(e,t,n,r,i,a,o){var s=i.x-n/2-o,l=i.x+n/2+o,u=i.y-n/2-o,c=i.y+n/2+o;return s<=e&&e<=l&&u<=t&&t<=c},r=function(e,t,n,r,i){var a=e*Math.cos(r)-t*Math.sin(r),o=(e*Math.sin(r)+t*Math.cos(r))*n;return{x:a*n+i.x,y:o+i.y}},i=function(e,t,n,i){for(var a=[],o=0;o<e.length;o+=2){var s=e[o],l=e[o+1];a.push(r(s,l,t,n,i))}return a},a=function(e){for(var t=[],n=0;n<e.length;n++){var r=e[n];t.push(r.x,r.y)}return t},o=function(e){return e.pstyle("width").pfValue*e.pstyle("arrow-scale").pfValue*2},s=function(r,s){f(s)&&(s=e[s]),e[r]=z({name:r,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(e,t,n,r,o,s){var l=a(i(this.points,n+2*s,r,o));return Ht(e,t,l)},roughCollide:n,draw:function(e,n,r,a){var o=i(this.points,n,r,a);t.arrowShapeImpl("polygon")(e,o)},spacing:function(e){return 0},gap:o},s)};s("none",{collide:Ge,roughCollide:Ge,draw:Xe,spacing:Ye,gap:Ye}),s("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),s("arrow","triangle"),s("triangle-backcurve",{points:e.triangle.points,controlPoint:[0,-.15],roughCollide:n,draw:function(e,n,a,o,s){var l=i(this.points,n,a,o),u=this.controlPoint,c=r(u[0],u[1],n,a,o);t.arrowShapeImpl(this.name)(e,l,c)},gap:function(e){return.8*o(e)}}),s("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(e,t,n,r,o,s,l){var u=a(i(this.points,n+2*l,r,o)),c=a(i(this.pointsTee,n+2*l,r,o));return Ht(e,t,u)||Ht(e,t,c)},draw:function(e,n,r,a,o){var s=i(this.points,n,r,a),l=i(this.pointsTee,n,r,a);t.arrowShapeImpl(this.name)(e,s,l)}}),s("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function(e,t,n,r,o,s,l){var u=o,c=Math.pow(u.x-e,2)+Math.pow(u.y-t,2)<=Math.pow((n+2*l)*this.radius,2),h=a(i(this.points,n+2*l,r,o));return Ht(e,t,h)||c},draw:function(e,n,r,a,o){var s=i(this.pointsTr,n,r,a);t.arrowShapeImpl(this.name)(e,s,a.x,a.y,this.radius*n)},spacing:function(e){return t.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.radius}}),s("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(e,t){var n=this.baseCrossLinePts.slice(),r=t/e;return n[3]=n[3]-r,n[5]=n[5]-r,n},collide:function(e,t,n,r,o,s,l){var u=a(i(this.points,n+2*l,r,o)),c=a(i(this.crossLinePts(n,s),n+2*l,r,o));return Ht(e,t,u)||Ht(e,t,c)},draw:function(e,n,r,a,o){var s=i(this.points,n,r,a),l=i(this.crossLinePts(n,o),n,r,a);t.arrowShapeImpl(this.name)(e,s,l)}}),s("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(e){return.525*o(e)}}),s("circle",{radius:.15,collide:function(e,t,n,r,i,a,o){var s=i;return Math.pow(s.x-e,2)+Math.pow(s.y-t,2)<=Math.pow((n+2*o)*this.radius,2)},draw:function(e,n,r,i,a){t.arrowShapeImpl(this.name)(e,i.x,i.y,this.radius*n)},spacing:function(e){return t.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.radius}}),s("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(e){return 1},gap:function(e){return 1}}),s("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),s("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(e){return e.pstyle("width").pfValue*e.pstyle("arrow-scale").value}}),s("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function(e){return.95*e.pstyle("width").pfValue*e.pstyle("arrow-scale").value}})}},Gl={projectIntoViewport:function(e,t){var n=this.cy,r=this.findContainerClientCoords(),i=r[0],a=r[1],o=r[4],s=n.pan(),l=n.zoom();return[((e-i)/o-s.x)/l,((t-a)/o-s.y)/l]},findContainerClientCoords:function(){if(this.containerBB)return this.containerBB;var e=this.container,t=e.getBoundingClientRect(),n=l.getComputedStyle(e),r=function(e){return parseFloat(n.getPropertyValue(e))},i=r("padding-left"),a=r("padding-right"),o=r("padding-top"),s=r("padding-bottom"),u=r("border-left-width"),c=r("border-right-width"),h=r("border-top-width"),d=(r("border-bottom-width"),e.clientWidth),p=e.clientHeight,g=i+a,f=o+s,v=u+c,y=t.width/(d+v),m=d-g,b=p-f,x=t.left+i+u,w=t.top+o+h;return this.containerBB=[x,w,m,b,y]},invalidateContainerClientCoordsCache:function(){this.containerBB=null},findNearestElement:function(e,t,n,r){return this.findNearestElements(e,t,n,r)[0]},findNearestElements:function(e,t,n,r){var i,a,o=this,s=this,l=s.getCachedZSortedEles(),u=[],c=s.cy.zoom(),h=s.cy.hasCompoundNodes(),d=(r?24:8)/c,p=(r?8:2)/c,g=(r?8:2)/c,f=1/0;function v(e,t){if(e.isNode()){if(a)return;a=e,u.push(e)}if(e.isEdge()&&(null==t||t<f))if(i){if(i.pstyle("z-compound-depth").value===e.pstyle("z-compound-depth").value&&i.pstyle("z-compound-depth").value===e.pstyle("z-compound-depth").value)for(var n=0;n<u.length;n++)if(u[n].isEdge()){u[n]=e,i=e,f=null!=t?t:f;break}}else u.push(e),i=e,f=null!=t?t:f}function y(n){var r=n.outerWidth()+2*p,i=n.outerHeight()+2*p,a=r/2,l=i/2,u=n.position();if(u.x-a<=e&&e<=u.x+a&&u.y-l<=t&&t<=u.y+l&&s.nodeShapes[o.getNodeShape(n)].checkPoint(e,t,0,r,i,u.x,u.y))return v(n,0),!0}function m(n){var r,i=n._private,a=i.rscratch,l=n.pstyle("width").pfValue,c=n.pstyle("arrow-scale").value,p=l/2+d,g=p*p,f=2*p,m=i.source,b=i.target;if("segments"===a.edgeType||"straight"===a.edgeType||"haystack"===a.edgeType){for(var x=a.allpts,w=0;w+3<x.length;w+=2)if(Vt(e,t,x[w],x[w+1],x[w+2],x[w+3],f)&&g>(r=qt(e,t,x[w],x[w+1],x[w+2],x[w+3])))return v(n,r),!0}else if("bezier"===a.edgeType||"multibezier"===a.edgeType||"self"===a.edgeType||"compound"===a.edgeType)for(x=a.allpts,w=0;w+5<a.allpts.length;w+=4)if(Ut(e,t,x[w],x[w+1],x[w+2],x[w+3],x[w+4],x[w+5],f)&&g>(r=jt(e,t,x[w],x[w+1],x[w+2],x[w+3],x[w+4],x[w+5])))return v(n,r),!0;m=m||i.source,b=b||i.target;var E=o.getArrowWidth(l,c),_=[{name:"source",x:a.arrowStartX,y:a.arrowStartY,angle:a.srcArrowAngle},{name:"target",x:a.arrowEndX,y:a.arrowEndY,angle:a.tgtArrowAngle},{name:"mid-source",x:a.midX,y:a.midY,angle:a.midsrcArrowAngle},{name:"mid-target",x:a.midX,y:a.midY,angle:a.midtgtArrowAngle}];for(w=0;w<_.length;w++){var T=_[w],D=s.arrowShapes[n.pstyle(T.name+"-arrow-shape").value],C=n.pstyle("width").pfValue;if(D.roughCollide(e,t,E,T.angle,{x:T.x,y:T.y},C,d)&&D.collide(e,t,E,T.angle,{x:T.x,y:T.y},C,d))return v(n),!0}h&&u.length>0&&(y(m),y(b))}function b(e,t,n){return Je(e,t,n)}function x(n,r){var i,a=n._private,o=g;i=r?r+"-":"",n.boundingBox();var s=a.labelBounds[r||"main"],l=n.pstyle(i+"label").value;if("yes"===n.pstyle("text-events").strValue&&l){var u=b(a.rscratch,"labelX",r),c=b(a.rscratch,"labelY",r),h=b(a.rscratch,"labelAngle",r),d=n.pstyle(i+"text-margin-x").pfValue,p=n.pstyle(i+"text-margin-y").pfValue,f=s.x1-o-d,y=s.x2+o-d,m=s.y1-o-p,x=s.y2+o-p;if(h){var w=Math.cos(h),E=Math.sin(h),_=function(e,t){return{x:(e-=u)*w-(t-=c)*E+u,y:e*E+t*w+c}},T=_(f,m),D=_(f,x),C=_(y,m),N=_(y,x),A=[T.x+d,T.y+p,C.x+d,C.y+p,N.x+d,N.y+p,D.x+d,D.y+p];if(Ht(e,t,A))return v(n),!0}else if(Gt(s,e,t))return v(n),!0}}n&&(l=l.interactive);for(var w=l.length-1;w>=0;w--){var E=l[w];E.isNode()?y(E)||x(E):m(E)||x(E)||x(E,"source")||x(E,"target")}return u},getAllInBox:function(e,t,n,r){for(var i=this.getCachedZSortedEles().interactive,a=[],o=Math.min(e,n),s=Math.max(e,n),l=Math.min(t,r),u=Math.max(t,r),c=Ot({x1:e=o,y1:t=l,x2:n=s,y2:r=u}),h=0;h<i.length;h++){var d=i[h];if(d.isNode()){var p=d,g=p.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:!1});zt(c,g)&&!Yt(g,c)&&a.push(p)}else{var f=d,v=f._private,y=v.rscratch;if(null!=y.startX&&null!=y.startY&&!Gt(c,y.startX,y.startY))continue;if(null!=y.endX&&null!=y.endY&&!Gt(c,y.endX,y.endY))continue;if("bezier"===y.edgeType||"multibezier"===y.edgeType||"self"===y.edgeType||"compound"===y.edgeType||"segments"===y.edgeType||"haystack"===y.edgeType){for(var m=v.rstyle.bezierPts||v.rstyle.linePts||v.rstyle.haystackPts,b=!0,x=0;x<m.length;x++)if(w=c,E=m[x],!Gt(w,E.x,E.y)){b=!1;break}b&&a.push(f)}else"haystack"!==y.edgeType&&"straight"!==y.edgeType||a.push(f)}}var w,E;return a}},Yl={calculateArrowAngles:function(e){var t,n,r,i,a,o,s=e._private.rscratch,l="haystack"===s.edgeType,u="bezier"===s.edgeType,c="multibezier"===s.edgeType,h="segments"===s.edgeType,d="compound"===s.edgeType,p="self"===s.edgeType;if(l?(r=s.haystackPts[0],i=s.haystackPts[1],a=s.haystackPts[2],o=s.haystackPts[3]):(r=s.arrowStartX,i=s.arrowStartY,a=s.arrowEndX,o=s.arrowEndY),f=s.midX,v=s.midY,h)t=r-s.segpts[0],n=i-s.segpts[1];else if(c||d||p||u){var g=s.allpts;t=r-St(g[0],g[2],g[4],.1),n=i-St(g[1],g[3],g[5],.1)}else t=r-f,n=i-v;s.srcArrowAngle=Dt(t,n);var f=s.midX,v=s.midY;if(l&&(f=(r+a)/2,v=(i+o)/2),t=a-r,n=o-i,h)if((g=s.allpts).length/2%2==0){var y=(m=g.length/2)-2;t=g[m]-g[y],n=g[m+1]-g[y+1]}else{y=(m=g.length/2-1)-2;var m,b=m+2;t=g[m]-g[y],n=g[m+1]-g[y+1]}else if(c||d||p){var x,w,E,_;g=s.allpts;if(s.ctrlpts.length/2%2==0){var T=2+(D=2+(C=g.length/2-1));x=St(g[C],g[D],g[T],0),w=St(g[C+1],g[D+1],g[T+1],0),E=St(g[C],g[D],g[T],1e-4),_=St(g[C+1],g[D+1],g[T+1],1e-4)}else{var D,C;T=2+(D=g.length/2-1),x=St(g[C=D-2],g[D],g[T],.4999),w=St(g[C+1],g[D+1],g[T+1],.4999),E=St(g[C],g[D],g[T],.5),_=St(g[C+1],g[D+1],g[T+1],.5)}t=E-x,n=_-w}if(s.midtgtArrowAngle=Dt(t,n),s.midDispX=t,s.midDispY=n,t*=-1,n*=-1,h&&((g=s.allpts).length/2%2==0||(t=-(g[b=2+(m=g.length/2-1)]-g[m]),n=-(g[b+1]-g[m+1]))),s.midsrcArrowAngle=Dt(t,n),h)t=a-s.segpts[s.segpts.length-2],n=o-s.segpts[s.segpts.length-1];else if(c||d||p||u){var N=(g=s.allpts).length;t=a-St(g[N-6],g[N-4],g[N-2],.9),n=o-St(g[N-5],g[N-3],g[N-1],.9)}else t=a-f,n=o-v;s.tgtArrowAngle=Dt(t,n)}};Yl.getArrowWidth=Yl.getArrowHeight=function(e,t){var n=this.arrowWidthCache=this.arrowWidthCache||{},r=n[e+", "+t];return r||(r=Math.max(Math.pow(13.37*e,.9),29)*t,n[e+", "+t]=r,r)};var Xl={};function Vl(e){var t=[];if(null!=e){for(var n=0;n<e.length;n+=2){var r=e[n],i=e[n+1];t.push({x:r,y:i})}return t}}Xl.findHaystackPoints=function(e){for(var t=0;t<e.length;t++){var n=e[t],r=n._private,i=r.rscratch;if(!i.haystack){var a=2*Math.random()*Math.PI;i.source={x:Math.cos(a),y:Math.sin(a)},a=2*Math.random()*Math.PI,i.target={x:Math.cos(a),y:Math.sin(a)}}var o=r.source,s=r.target,l=o.position(),u=s.position(),c=o.width(),h=s.width(),d=o.height(),p=s.height(),g=n.pstyle("haystack-radius").value/2;i.haystackPts=i.allpts=[i.source.x*c*g+l.x,i.source.y*d*g+l.y,i.target.x*h*g+u.x,i.target.y*p*g+u.y],i.midX=(i.allpts[0]+i.allpts[2])/2,i.midY=(i.allpts[1]+i.allpts[3])/2,i.edgeType="haystack",i.haystack=!0,this.storeEdgeProjections(n),this.calculateArrowAngles(n),this.recalculateEdgeLabelProjections(n),this.calculateLabelAngles(n)}},Xl.findSegmentsPoints=function(e,t){var n=e._private.rscratch,r=t.posPts,i=t.intersectionPts,a=t.vectorNormInverse,o=e.pstyle("edge-distances").value,s=e.pstyle("segment-weights"),l=e.pstyle("segment-distances"),u=Math.min(s.pfValue.length,l.pfValue.length);n.edgeType="segments",n.segpts=[];for(var c=0;c<u;c++){var h=s.pfValue[c],d=l.pfValue[c],p=1-h,g=h,f="node-position"===o?r:i,v={x:f.x1*p+f.x2*g,y:f.y1*p+f.y2*g};n.segpts.push(v.x+a.x*d,v.y+a.y*d)}},Xl.findLoopPoints=function(e,t,n,r){var i=e._private.rscratch,a=t.dirCounts,o=t.srcPos,s=e.pstyle("control-point-distances"),l=s?s.pfValue[0]:void 0,u=e.pstyle("loop-direction").pfValue,c=e.pstyle("loop-sweep").pfValue,h=e.pstyle("control-point-step-size").pfValue;i.edgeType="self";var d=n,p=h;r&&(d=0,p=l);var g=u-Math.PI/2,f=g-c/2,v=g+c/2,y=String(u+"_"+c);d=void 0===a[y]?a[y]=0:++a[y],i.ctrlpts=[o.x*****Math.cos(f)*p*(d/3+1),o.y*****Math.sin(f)*p*(d/3+1),o.x*****Math.cos(v)*p*(d/3+1),o.y*****Math.sin(v)*p*(d/3+1)]},Xl.findCompoundLoopPoints=function(e,t,n,r){var i=e._private.rscratch;i.edgeType="compound";var a=t.srcPos,o=t.tgtPos,s=t.srcW,l=t.srcH,u=t.tgtW,c=t.tgtH,h=e.pstyle("control-point-step-size").pfValue,d=e.pstyle("control-point-distances"),p=d?d.pfValue[0]:void 0,g=n,f=h;r&&(g=0,f=p);var v={x:a.x-s/2,y:a.y-l/2},y={x:o.x-u/2,y:o.y-c/2},m={x:Math.min(v.x,y.x),y:Math.min(v.y,y.y)},b=Math.max(.5,Math.log(.01*s)),x=Math.max(.5,Math.log(.01*u));i.ctrlpts=[m.x,m.y-(1+Math.pow(50,1.12)/100)*f*(g/3+1)*b,m.x-(1+Math.pow(50,1.12)/100)*f*(g/3+1)*x,m.y]},Xl.findStraightEdgePoints=function(e){e._private.rscratch.edgeType="straight"},Xl.findBezierPoints=function(e,t,n,r,i){var a=e._private.rscratch,o=t.vectorNormInverse,s=t.posPts,l=t.intersectionPts,u=e.pstyle("edge-distances").value,c=e.pstyle("control-point-step-size").pfValue,h=e.pstyle("control-point-distances"),d=e.pstyle("control-point-weights"),p=h&&d?Math.min(h.value.length,d.value.length):1,g=h?h.pfValue[0]:void 0,f=d.value[0],v=r;a.edgeType=v?"multibezier":"bezier",a.ctrlpts=[];for(var y=0;y<p;y++){var m,b=(.5-t.eles.length/2+n)*c*(i?-1:1),x=Nt(b);v&&(g=h?h.pfValue[y]:c,f=d.value[y]);var w=void 0!==(m=r?g:void 0!==g?x*g:void 0)?m:b,E=1-f,_=f,T="node-position"===u?s:l,D={x:T.x1*E+T.x2*_,y:T.y1*E+T.y2*_};a.ctrlpts.push(D.x+o.x*w,D.y+o.y*w)}},Xl.findTaxiPoints=function(e,t){var n=e._private.rscratch;n.edgeType="segments";var r="vertical",i="horizontal",a="leftward",o="rightward",s="downward",l="upward",u=t.posPts,c=t.srcW,h=t.srcH,d=t.tgtW,p=t.tgtH,g="node-position"!==e.pstyle("edge-distances").value,f=e.pstyle("taxi-direction").value,v=f,y=e.pstyle("taxi-turn"),m="%"===y.units,b=y.pfValue,x=b<0,w=e.pstyle("taxi-turn-min-distance").pfValue,E=g?(c+d)/2:0,_=g?(h+p)/2:0,T=u.x2-u.x1,D=u.y2-u.y1,C=function(e,t){return e>0?Math.max(e-t,0):Math.min(e+t,0)},N=C(T,E),A=C(D,_),L=!1;"auto"===v?f=Math.abs(N)>Math.abs(A)?i:r:v===l||v===s?(f=r,L=!0):v!==a&&v!==o||(f=i,L=!0);var k,S=f===r,I=S?A:N,M=S?D:T,O=Nt(M),P=!1;L&&(m||x)||!(v===s&&M<0||v===l&&M>0||v===a&&M>0||v===o&&M<0)||(I=(O*=-1)*Math.abs(I),P=!0);var R=function(e){return Math.abs(e)<w||Math.abs(e)>=Math.abs(I)},B=R(k=m?(b<0?1+b:b)*I:(b<0?I:0)+b*O),F=R(Math.abs(I)-Math.abs(k));if(!B&&!F||P)if(S){var z=u.y1+k+(g?h/2*O:0),G=u.x1,Y=u.x2;n.segpts=[G,z,Y,z]}else{var X=u.x1+k+(g?c/2*O:0),V=u.y1,U=u.y2;n.segpts=[X,V,X,U]}else if(S){var j=Math.abs(M)<=h/2,q=Math.abs(T)<=d/2;if(j){var H=(u.x1+u.x2)/2,W=u.y1,$=u.y2;n.segpts=[H,W,H,$]}else if(q){var K=(u.y1+u.y2)/2,Z=u.x1,Q=u.x2;n.segpts=[Z,K,Q,K]}else n.segpts=[u.x1,u.y2]}else{var J=Math.abs(M)<=c/2,ee=Math.abs(D)<=p/2;if(J){var te=(u.y1+u.y2)/2,ne=u.x1,re=u.x2;n.segpts=[ne,te,re,te]}else if(ee){var ie=(u.x1+u.x2)/2,ae=u.y1,oe=u.y2;n.segpts=[ie,ae,ie,oe]}else n.segpts=[u.x2,u.y1]}},Xl.tryToCorrectInvalidPoints=function(e,t){var n=e._private.rscratch;if("bezier"===n.edgeType){var r=t.srcPos,i=t.tgtPos,a=t.srcW,o=t.srcH,s=t.tgtW,l=t.tgtH,u=t.srcShape,c=t.tgtShape,h=!b(n.startX)||!b(n.startY),d=!b(n.arrowStartX)||!b(n.arrowStartY),p=!b(n.endX)||!b(n.endY),g=!b(n.arrowEndX)||!b(n.arrowEndY),f=this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth*3,v=At({x:n.ctrlpts[0],y:n.ctrlpts[1]},{x:n.startX,y:n.startY}),y=v<f,m=At({x:n.ctrlpts[0],y:n.ctrlpts[1]},{x:n.endX,y:n.endY}),x=m<f,w=!1;if(h||d||y){w=!0;var E={x:n.ctrlpts[0]-r.x,y:n.ctrlpts[1]-r.y},_=Math.sqrt(E.x*E.x+E.y*E.y),T={x:E.x/_,y:E.y/_},D=Math.max(a,o),C={x:n.ctrlpts[0]+2*T.x*D,y:n.ctrlpts[1]+2*T.y*D},N=u.intersectLine(r.x,r.y,a,o,C.x,C.y,0);y?(n.ctrlpts[0]=n.ctrlpts[0]+T.x*(f-v),n.ctrlpts[1]=n.ctrlpts[1]+T.y*(f-v)):(n.ctrlpts[0]=N[0]+T.x*f,n.ctrlpts[1]=N[1]+T.y*f)}if(p||g||x){w=!0;var A={x:n.ctrlpts[0]-i.x,y:n.ctrlpts[1]-i.y},L=Math.sqrt(A.x*A.x+A.y*A.y),k={x:A.x/L,y:A.y/L},S=Math.max(a,o),I={x:n.ctrlpts[0]+2*k.x*S,y:n.ctrlpts[1]+2*k.y*S},M=c.intersectLine(i.x,i.y,s,l,I.x,I.y,0);x?(n.ctrlpts[0]=n.ctrlpts[0]+k.x*(f-m),n.ctrlpts[1]=n.ctrlpts[1]+k.y*(f-m)):(n.ctrlpts[0]=M[0]+k.x*f,n.ctrlpts[1]=M[1]+k.y*f)}w&&this.findEndpoints(e)}},Xl.storeAllpts=function(e){var t=e._private.rscratch;if("multibezier"===t.edgeType||"bezier"===t.edgeType||"self"===t.edgeType||"compound"===t.edgeType){t.allpts=[],t.allpts.push(t.startX,t.startY);for(var n=0;n+1<t.ctrlpts.length;n+=2)t.allpts.push(t.ctrlpts[n],t.ctrlpts[n+1]),n+3<t.ctrlpts.length&&t.allpts.push((t.ctrlpts[n]+t.ctrlpts[n+2])/2,(t.ctrlpts[n+1]+t.ctrlpts[n+3])/2);var r;t.allpts.push(t.endX,t.endY),t.ctrlpts.length/2%2==0?(r=t.allpts.length/2-1,t.midX=t.allpts[r],t.midY=t.allpts[r+1]):(r=t.allpts.length/2-3,.5,t.midX=St(t.allpts[r],t.allpts[r+2],t.allpts[r+4],.5),t.midY=St(t.allpts[r+1],t.allpts[r+3],t.allpts[r+5],.5))}else if("straight"===t.edgeType)t.allpts=[t.startX,t.startY,t.endX,t.endY],t.midX=(t.startX+t.endX+t.arrowStartX+t.arrowEndX)/4,t.midY=(t.startY+t.endY+t.arrowStartY+t.arrowEndY)/4;else if("segments"===t.edgeType)if(t.allpts=[],t.allpts.push(t.startX,t.startY),t.allpts.push.apply(t.allpts,t.segpts),t.allpts.push(t.endX,t.endY),t.segpts.length%4==0){var i=t.segpts.length/2,a=i-2;t.midX=(t.segpts[a]+t.segpts[i])/2,t.midY=(t.segpts[a+1]+t.segpts[i+1])/2}else{var o=t.segpts.length/2-1;t.midX=t.segpts[o],t.midY=t.segpts[o+1]}},Xl.checkForInvalidEdgeWarning=function(e){var t=e[0]._private.rscratch;t.nodesOverlap||b(t.startX)&&b(t.startY)&&b(t.endX)&&b(t.endY)?t.loggedErr=!1:t.loggedErr||(t.loggedErr=!0,je("Edge `"+e.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))},Xl.findEdgeControlPoints=function(e){var t=this;if(e&&0!==e.length){for(var n=this,r=n.cy.hasCompoundNodes(),i={map:new tt,get:function(e){var t=this.map.get(e[0]);return null!=t?t.get(e[1]):null},set:function(e,t){var n=this.map.get(e[0]);null==n&&(n=new tt,this.map.set(e[0],n)),n.set(e[1],t)}},a=[],o=[],s=0;s<e.length;s++){var l=e[s],u=l._private,c=l.pstyle("curve-style").value;if(!l.removed()&&l.takesUpSpace())if("haystack"!==c){var h="unbundled-bezier"===c||"segments"===c||"straight"===c||"straight-triangle"===c||"taxi"===c,d="unbundled-bezier"===c||"bezier"===c,p=u.source,g=u.target,f=[p.poolIndex(),g.poolIndex()].sort(),v=i.get(f);null==v&&(v={eles:[]},i.set(f,v),a.push(f)),v.eles.push(l),h&&(v.hasUnbundled=!0),d&&(v.hasBezier=!0)}else o.push(l)}for(var y=function(e){var o=a[e],s=i.get(o),l=void 0;if(!s.hasUnbundled){var u=s.eles[0].parallelEdges().filter((function(e){return e.isBundledBezier()}));Qe(s.eles),u.forEach((function(e){return s.eles.push(e)})),s.eles.sort((function(e,t){return e.poolIndex()-t.poolIndex()}))}var c=s.eles[0],h=c.source(),d=c.target();if(h.poolIndex()>d.poolIndex()){var p=h;h=d,d=p}var g=s.srcPos=h.position(),f=s.tgtPos=d.position(),v=s.srcW=h.outerWidth(),y=s.srcH=h.outerHeight(),m=s.tgtW=d.outerWidth(),x=s.tgtH=d.outerHeight(),w=s.srcShape=n.nodeShapes[t.getNodeShape(h)],E=s.tgtShape=n.nodeShapes[t.getNodeShape(d)];s.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var _=0;_<s.eles.length;_++){var T=s.eles[_],D=T[0]._private.rscratch,C=T.pstyle("curve-style").value,N="unbundled-bezier"===C||"segments"===C||"taxi"===C,A=!h.same(T.source());if(!s.calculatedIntersection&&h!==d&&(s.hasBezier||s.hasUnbundled)){s.calculatedIntersection=!0;var L=w.intersectLine(g.x,g.y,v,y,f.x,f.y,0),k=s.srcIntn=L,S=E.intersectLine(f.x,f.y,m,x,g.x,g.y,0),I=s.tgtIntn=S,M=s.intersectionPts={x1:L[0],x2:S[0],y1:L[1],y2:S[1]},O=s.posPts={x1:g.x,x2:f.x,y1:g.y,y2:f.y},P=S[1]-L[1],R=S[0]-L[0],B=Math.sqrt(R*R+P*P),F=s.vector={x:R,y:P},z=s.vectorNorm={x:F.x/B,y:F.y/B},G={x:-z.y,y:z.x};s.nodesOverlap=!b(B)||E.checkPoint(L[0],L[1],0,m,x,f.x,f.y)||w.checkPoint(S[0],S[1],0,v,y,g.x,g.y),s.vectorNormInverse=G,l={nodesOverlap:s.nodesOverlap,dirCounts:s.dirCounts,calculatedIntersection:!0,hasBezier:s.hasBezier,hasUnbundled:s.hasUnbundled,eles:s.eles,srcPos:f,tgtPos:g,srcW:m,srcH:x,tgtW:v,tgtH:y,srcIntn:I,tgtIntn:k,srcShape:E,tgtShape:w,posPts:{x1:O.x2,y1:O.y2,x2:O.x1,y2:O.y1},intersectionPts:{x1:M.x2,y1:M.y2,x2:M.x1,y2:M.y1},vector:{x:-F.x,y:-F.y},vectorNorm:{x:-z.x,y:-z.y},vectorNormInverse:{x:-G.x,y:-G.y}}}var Y=A?l:s;D.nodesOverlap=Y.nodesOverlap,D.srcIntn=Y.srcIntn,D.tgtIntn=Y.tgtIntn,r&&(h.isParent()||h.isChild()||d.isParent()||d.isChild())&&(h.parents().anySame(d)||d.parents().anySame(h)||h.same(d)&&h.isParent())?t.findCompoundLoopPoints(T,Y,_,N):h===d?t.findLoopPoints(T,Y,_,N):"segments"===C?t.findSegmentsPoints(T,Y):"taxi"===C?t.findTaxiPoints(T,Y):"straight"===C||!N&&s.eles.length%2==1&&_===Math.floor(s.eles.length/2)?t.findStraightEdgePoints(T):t.findBezierPoints(T,Y,_,N,A),t.findEndpoints(T),t.tryToCorrectInvalidPoints(T,Y),t.checkForInvalidEdgeWarning(T),t.storeAllpts(T),t.storeEdgeProjections(T),t.calculateArrowAngles(T),t.recalculateEdgeLabelProjections(T),t.calculateLabelAngles(T)}},m=0;m<a.length;m++)y(m);this.findHaystackPoints(o)}},Xl.getSegmentPoints=function(e){var t=e[0]._private.rscratch;if("segments"===t.edgeType)return this.recalculateRenderedStyle(e),Vl(t.segpts)},Xl.getControlPoints=function(e){var t=e[0]._private.rscratch,n=t.edgeType;if("bezier"===n||"multibezier"===n||"self"===n||"compound"===n)return this.recalculateRenderedStyle(e),Vl(t.ctrlpts)},Xl.getEdgeMidpoint=function(e){var t=e[0]._private.rscratch;return this.recalculateRenderedStyle(e),{x:t.midX,y:t.midY}};var Ul={manualEndptToPx:function(e,t){var n=e.position(),r=e.outerWidth(),i=e.outerHeight();if(2===t.value.length){var a=[t.pfValue[0],t.pfValue[1]];return"%"===t.units[0]&&(a[0]=a[0]*r),"%"===t.units[1]&&(a[1]=a[1]*i),a[0]+=n.x,a[1]+=n.y,a}var o=t.pfValue[0];o=-Math.PI/2+o;var s=2*Math.max(r,i),l=[n.x+Math.cos(o)*s,n.y+Math.sin(o)*s];return this.nodeShapes[this.getNodeShape(e)].intersectLine(n.x,n.y,r,i,l[0],l[1],0)},findEndpoints:function(e){var t,n,r,i,a,o=this,s=e.source()[0],l=e.target()[0],u=s.position(),c=l.position(),h=e.pstyle("target-arrow-shape").value,d=e.pstyle("source-arrow-shape").value,p=e.pstyle("target-distance-from-node").pfValue,g=e.pstyle("source-distance-from-node").pfValue,f=e.pstyle("curve-style").value,v=e._private.rscratch,y=v.edgeType,m="self"===y||"compound"===y,x="bezier"===y||"multibezier"===y||m,w="bezier"!==y,E="straight"===y||"segments"===y,_="segments"===y,T=x||w||E,D=m||"taxi"===f,C=e.pstyle("source-endpoint"),N=D?"outside-to-node":C.value,A=e.pstyle("target-endpoint"),L=D?"outside-to-node":A.value;if(v.srcManEndpt=C,v.tgtManEndpt=A,x){var k=[v.ctrlpts[0],v.ctrlpts[1]];n=w?[v.ctrlpts[v.ctrlpts.length-2],v.ctrlpts[v.ctrlpts.length-1]]:k,r=k}else if(E){var S=_?v.segpts.slice(0,2):[c.x,c.y];n=_?v.segpts.slice(v.segpts.length-2):[u.x,u.y],r=S}if("inside-to-node"===L)t=[c.x,c.y];else if(A.units)t=this.manualEndptToPx(l,A);else if("outside-to-line"===L)t=v.tgtIntn;else if("outside-to-node"===L||"outside-to-node-or-label"===L?i=n:"outside-to-line"!==L&&"outside-to-line-or-label"!==L||(i=[u.x,u.y]),t=o.nodeShapes[this.getNodeShape(l)].intersectLine(c.x,c.y,l.outerWidth(),l.outerHeight(),i[0],i[1],0),"outside-to-node-or-label"===L||"outside-to-line-or-label"===L){var I=l._private.rscratch,M=I.labelWidth,O=I.labelHeight,P=I.labelX,R=I.labelY,B=M/2,F=O/2,z=l.pstyle("text-valign").value;"top"===z?R-=F:"bottom"===z&&(R+=F);var G=l.pstyle("text-halign").value;"left"===G?P-=B:"right"===G&&(P+=B);var Y=tn(i[0],i[1],[P-B,R-F,P+B,R-F,P+B,R+F,P-B,R+F],c.x,c.y);if(Y.length>0){var X=u,V=Lt(X,Tt(t)),U=Lt(X,Tt(Y)),j=V;U<V&&(t=Y,j=U),Y.length>2&&Lt(X,{x:Y[2],y:Y[3]})<j&&(t=[Y[2],Y[3]])}}var q=nn(t,n,o.arrowShapes[h].spacing(e)+p),H=nn(t,n,o.arrowShapes[h].gap(e)+p);if(v.endX=H[0],v.endY=H[1],v.arrowEndX=q[0],v.arrowEndY=q[1],"inside-to-node"===N)t=[u.x,u.y];else if(C.units)t=this.manualEndptToPx(s,C);else if("outside-to-line"===N)t=v.srcIntn;else if("outside-to-node"===N||"outside-to-node-or-label"===N?a=r:"outside-to-line"!==N&&"outside-to-line-or-label"!==N||(a=[c.x,c.y]),t=o.nodeShapes[this.getNodeShape(s)].intersectLine(u.x,u.y,s.outerWidth(),s.outerHeight(),a[0],a[1],0),"outside-to-node-or-label"===N||"outside-to-line-or-label"===N){var W=s._private.rscratch,$=W.labelWidth,K=W.labelHeight,Z=W.labelX,Q=W.labelY,J=$/2,ee=K/2,te=s.pstyle("text-valign").value;"top"===te?Q-=ee:"bottom"===te&&(Q+=ee);var ne=s.pstyle("text-halign").value;"left"===ne?Z-=J:"right"===ne&&(Z+=J);var re=tn(a[0],a[1],[Z-J,Q-ee,Z+J,Q-ee,Z+J,Q+ee,Z-J,Q+ee],u.x,u.y);if(re.length>0){var ie=c,ae=Lt(ie,Tt(t)),oe=Lt(ie,Tt(re)),se=ae;oe<ae&&(t=[re[0],re[1]],se=oe),re.length>2&&Lt(ie,{x:re[2],y:re[3]})<se&&(t=[re[2],re[3]])}}var le=nn(t,r,o.arrowShapes[d].spacing(e)+g),ue=nn(t,r,o.arrowShapes[d].gap(e)+g);v.startX=ue[0],v.startY=ue[1],v.arrowStartX=le[0],v.arrowStartY=le[1],T&&(b(v.startX)&&b(v.startY)&&b(v.endX)&&b(v.endY)?v.badLine=!1:v.badLine=!0)},getSourceEndpoint:function(e){var t=e[0]._private.rscratch;return this.recalculateRenderedStyle(e),"haystack"===t.edgeType?{x:t.haystackPts[0],y:t.haystackPts[1]}:{x:t.arrowStartX,y:t.arrowStartY}},getTargetEndpoint:function(e){var t=e[0]._private.rscratch;return this.recalculateRenderedStyle(e),"haystack"===t.edgeType?{x:t.haystackPts[2],y:t.haystackPts[3]}:{x:t.arrowEndX,y:t.arrowEndY}}},jl={};function ql(e,t,n){for(var r=function(e,t,n,r){return St(e,t,n,r)},i=t._private.rstyle.bezierPts,a=0;a<e.bezierProjPcts.length;a++){var o=e.bezierProjPcts[a];i.push({x:r(n[0],n[2],n[4],o),y:r(n[1],n[3],n[5],o)})}}jl.storeEdgeProjections=function(e){var t=e._private,n=t.rscratch,r=n.edgeType;if(t.rstyle.bezierPts=null,t.rstyle.linePts=null,t.rstyle.haystackPts=null,"multibezier"===r||"bezier"===r||"self"===r||"compound"===r){t.rstyle.bezierPts=[];for(var i=0;i+5<n.allpts.length;i+=4)ql(this,e,n.allpts.slice(i,i+6))}else if("segments"===r){var a=t.rstyle.linePts=[];for(i=0;i+1<n.allpts.length;i+=2)a.push({x:n.allpts[i],y:n.allpts[i+1]})}else if("haystack"===r){var o=n.haystackPts;t.rstyle.haystackPts=[{x:o[0],y:o[1]},{x:o[2],y:o[3]}]}t.rstyle.arrowWidth=this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth},jl.recalculateEdgeProjections=function(e){this.findEdgeControlPoints(e)};var Hl={recalculateNodeLabelProjection:function(e){var t=e.pstyle("label").strValue;if(!C(t)){var n,r,i=e._private,a=e.width(),o=e.height(),s=e.padding(),l=e.position(),u=e.pstyle("text-halign").strValue,c=e.pstyle("text-valign").strValue,h=i.rscratch,d=i.rstyle;switch(u){case"left":n=l.x-a/2-s;break;case"right":n=l.x+a/2+s;break;default:n=l.x}switch(c){case"top":r=l.y-o/2-s;break;case"bottom":r=l.y+o/2+s;break;default:r=l.y}h.labelX=n,h.labelY=r,d.labelX=n,d.labelY=r,this.calculateLabelAngles(e),this.applyLabelDimensions(e)}}},Wl=function(e,t){var n=Math.atan(t/e);return 0===e&&n<0&&(n*=-1),n},$l=function(e,t){var n=t.x-e.x,r=t.y-e.y;return Wl(n,r)};Hl.recalculateEdgeLabelProjections=function(e){var t,n=e._private,r=n.rscratch,i=this,a={mid:e.pstyle("label").strValue,source:e.pstyle("source-label").strValue,target:e.pstyle("target-label").strValue};if(a.mid||a.source||a.target){t={x:r.midX,y:r.midY};var o=function(e,t,r){et(n.rscratch,e,t,r),et(n.rstyle,e,t,r)};o("labelX",null,t.x),o("labelY",null,t.y);var s=Wl(r.midDispX,r.midDispY);o("labelAutoAngle",null,s);var l=function e(){if(e.cache)return e.cache;for(var t=[],a=0;a+5<r.allpts.length;a+=4){var o={x:r.allpts[a],y:r.allpts[a+1]},s={x:r.allpts[a+2],y:r.allpts[a+3]},l={x:r.allpts[a+4],y:r.allpts[a+5]};t.push({p0:o,p1:s,p2:l,startDist:0,length:0,segments:[]})}var u=n.rstyle.bezierPts,c=i.bezierProjPcts.length;function h(e,t,n,r,i){var a=At(t,n),o=e.segments[e.segments.length-1],s={p0:t,p1:n,t0:r,t1:i,startDist:o?o.startDist+o.length:0,length:a};e.segments.push(s),e.length+=a}for(var d=0;d<t.length;d++){var p=t[d],g=t[d-1];g&&(p.startDist=g.startDist+g.length),h(p,p.p0,u[d*c],0,i.bezierProjPcts[0]);for(var f=0;f<c-1;f++)h(p,u[d*c+f],u[d*c+f+1],i.bezierProjPcts[f],i.bezierProjPcts[f+1]);h(p,u[d*c+c-1],p.p2,i.bezierProjPcts[c-1],1)}return e.cache=t},u=function(n){var i,s="source"===n;if(a[n]){var u=e.pstyle(n+"-text-offset").pfValue;switch(r.edgeType){case"self":case"compound":case"bezier":case"multibezier":for(var c,h=l(),d=0,p=0,g=0;g<h.length;g++){for(var f=h[s?g:h.length-1-g],v=0;v<f.segments.length;v++){var y=f.segments[s?v:f.segments.length-1-v],m=g===h.length-1&&v===f.segments.length-1;if(d=p,(p+=y.length)>=u||m){c={cp:f,segment:y};break}}if(c)break}var b=c.cp,x=c.segment,w=(u-d)/x.length,E=x.t1-x.t0,_=s?x.t0+E*w:x.t1-E*w;_=Mt(0,_,1),t=It(b.p0,b.p1,b.p2,_),i=function(e,t,n,r){var i=Mt(0,r-.001,1),a=Mt(0,r+.001,1),o=It(e,t,n,i),s=It(e,t,n,a);return $l(o,s)}(b.p0,b.p1,b.p2,_);break;case"straight":case"segments":case"haystack":for(var T,D,C,N,A=0,L=r.allpts.length,k=0;k+3<L&&(s?(C={x:r.allpts[k],y:r.allpts[k+1]},N={x:r.allpts[k+2],y:r.allpts[k+3]}):(C={x:r.allpts[L-2-k],y:r.allpts[L-1-k]},N={x:r.allpts[L-4-k],y:r.allpts[L-3-k]}),D=A,!((A+=T=At(C,N))>=u));k+=2);var S=(u-D)/T;S=Mt(0,S,1),t=function(e,t,n,r){var i=t.x-e.x,a=t.y-e.y,o=At(e,t),s=i/o,l=a/o;return n=null==n?0:n,r=null!=r?r:n*o,{x:e.x+s*r,y:e.y+l*r}}(C,N,S),i=$l(C,N)}o("labelX",n,t.x),o("labelY",n,t.y),o("labelAutoAngle",n,i)}};u("source"),u("target"),this.applyLabelDimensions(e)}},Hl.applyLabelDimensions=function(e){this.applyPrefixedLabelDimensions(e),e.isEdge()&&(this.applyPrefixedLabelDimensions(e,"source"),this.applyPrefixedLabelDimensions(e,"target"))},Hl.applyPrefixedLabelDimensions=function(e,t){var n=e._private,r=this.getLabelText(e,t),i=this.calculateLabelDimensions(e,r),a=e.pstyle("line-height").pfValue,o=e.pstyle("text-wrap").strValue,s=Je(n.rscratch,"labelWrapCachedLines",t)||[],l="wrap"!==o?1:Math.max(s.length,1),u=i.height/l,c=u*a,h=i.width,d=i.height+(l-1)*(a-1)*u;et(n.rstyle,"labelWidth",t,h),et(n.rscratch,"labelWidth",t,h),et(n.rstyle,"labelHeight",t,d),et(n.rscratch,"labelHeight",t,d),et(n.rscratch,"labelLineHeight",t,c)},Hl.getLabelText=function(e,t){var n=e._private,r=t?t+"-":"",i=e.pstyle(r+"label").strValue,a=e.pstyle("text-transform").value,o=function(e,r){return r?(et(n.rscratch,e,t,r),r):Je(n.rscratch,e,t)};if(!i)return"";"none"==a||("uppercase"==a?i=i.toUpperCase():"lowercase"==a&&(i=i.toLowerCase()));var s=e.pstyle("text-wrap").value;if("wrap"===s){var l=o("labelKey");if(null!=l&&o("labelWrapKey")===l)return o("labelWrapCachedText");for(var u=i.split("\n"),c=e.pstyle("text-max-width").pfValue,h="anywhere"===e.pstyle("text-overflow-wrap").value,d=[],p=/[\s\u200b]+/,g=h?"":" ",f=0;f<u.length;f++){var v=u[f],y=this.calculateLabelDimensions(e,v).width;if(h){var m=v.split("").join("​");v=m}if(y>c){for(var b=v.split(p),x="",w=0;w<b.length;w++){var E=b[w],_=0===x.length?E:x+g+E;this.calculateLabelDimensions(e,_).width<=c?x+=E+g:(x&&d.push(x),x=E+g)}x.match(/^[\s\u200b]+$/)||d.push(x)}else d.push(v)}o("labelWrapCachedLines",d),i=o("labelWrapCachedText",d.join("\n")),o("labelWrapKey",l)}else if("ellipsis"===s){var T=e.pstyle("text-max-width").pfValue,D="",C=!1;if(this.calculateLabelDimensions(e,i).width<T)return i;for(var N=0;N<i.length&&!(this.calculateLabelDimensions(e,D+i[N]+"…").width>T);N++)D+=i[N],N===i.length-1&&(C=!0);return C||(D+="…"),D}return i},Hl.getLabelJustification=function(e){var t=e.pstyle("text-justification").strValue,n=e.pstyle("text-halign").strValue;if("auto"!==t)return t;if(!e.isNode())return"center";switch(n){case"left":return"right";case"right":return"left";default:return"center"}},Hl.calculateLabelDimensions=function(e,t){var n=Ie(t,e._private.labelDimsKey),r=this.labelDimCache||(this.labelDimCache=[]),i=r[n];if(null!=i)return i;var a=e.pstyle("font-style").strValue,o=e.pstyle("font-size").pfValue,s=e.pstyle("font-family").strValue,l=e.pstyle("font-weight").strValue,u=this.labelCalcCanvas,c=this.labelCalcCanvasContext;if(!u){u=this.labelCalcCanvas=document.createElement("canvas"),c=this.labelCalcCanvasContext=u.getContext("2d");var h=u.style;h.position="absolute",h.left="-9999px",h.top="-9999px",h.zIndex="-1",h.visibility="hidden",h.pointerEvents="none"}c.font="".concat(a," ").concat(l," ").concat(o,"px ").concat(s);for(var d=0,p=0,g=t.split("\n"),f=0;f<g.length;f++){var v=g[f],y=c.measureText(v),m=Math.ceil(y.width),b=o;d=Math.max(m,d),p+=b}return d+=0,p+=0,r[n]={width:d,height:p}},Hl.calculateLabelAngle=function(e,t){var n=e._private.rscratch,r=e.isEdge(),i=t?t+"-":"",a=e.pstyle(i+"text-rotation"),o=a.strValue;return"none"===o?0:r&&"autorotate"===o?n.labelAutoAngle:"autorotate"===o?0:a.pfValue},Hl.calculateLabelAngles=function(e){var t=this,n=e.isEdge(),r=e._private.rscratch;r.labelAngle=t.calculateLabelAngle(e),n&&(r.sourceLabelAngle=t.calculateLabelAngle(e,"source"),r.targetLabelAngle=t.calculateLabelAngle(e,"target"))};var Kl={},Zl=!1;Kl.getNodeShape=function(e){var t=e.pstyle("shape").value;if("cutrectangle"===t&&(e.width()<28||e.height()<28))return Zl||(je("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),Zl=!0),"rectangle";if(e.isParent())return"rectangle"===t||"roundrectangle"===t||"round-rectangle"===t||"cutrectangle"===t||"cut-rectangle"===t||"barrel"===t?t:"rectangle";if("polygon"===t){var n=e.pstyle("shape-polygon-points").value;return this.nodeShapes.makePolygon(n).name}return t};var Ql={updateCachedGrabbedEles:function(){var e=this.cachedZSortedEles;if(e){e.drag=[],e.nondrag=[];for(var t=[],n=0;n<e.length;n++){var r=(i=e[n])._private.rscratch;i.grabbed()&&!i.isParent()?t.push(i):r.inDragLayer?e.drag.push(i):e.nondrag.push(i)}for(n=0;n<t.length;n++){var i=t[n];e.drag.push(i)}}},invalidateCachedZSortedEles:function(){this.cachedZSortedEles=null},getCachedZSortedEles:function(e){if(e||!this.cachedZSortedEles){var t=this.cy.mutableElements().toArray();t.sort(zo),t.interactive=t.filter((function(e){return e.interactive()})),this.cachedZSortedEles=t,this.updateCachedGrabbedEles()}else t=this.cachedZSortedEles;return t}},Jl={};[Gl,Yl,Xl,Ul,jl,Hl,Kl,{registerCalculationListeners:function(){var e=this.cy,t=e.collection(),n=this,r=function(e){var n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(t.merge(e),n)for(var r=0;r<e.length;r++){var i=e[r]._private.rstyle;i.clean=!1,i.cleanConnected=!1}};n.binder(e).on("bounds.* dirty.*",(function(e){var t=e.target;r(t)})).on("style.* background.*",(function(e){var t=e.target;r(t,!1)}));var i=function(i){if(i){var a=n.onUpdateEleCalcsFns;t.cleanStyle();for(var o=0;o<t.length;o++){var s=t[o],l=s._private.rstyle;s.isNode()&&!l.cleanConnected&&(r(s.connectedEdges()),l.cleanConnected=!0)}if(a)for(var u=0;u<a.length;u++)(0,a[u])(i,t);n.recalculateRenderedStyle(t),t=e.collection()}};n.flushRenderedStyleQueue=function(){i(!0)},n.beforeRender(i,n.beforeRenderPriorities.eleCalcs)},onUpdateEleCalcs:function(e){(this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[]).push(e)},recalculateRenderedStyle:function(e,t){var n=function(e){return e._private.rstyle.cleanConnected},r=[],i=[];if(!this.destroyed){void 0===t&&(t=!0);for(var a=0;a<e.length;a++){var o=e[a],s=o._private,l=s.rstyle;!o.isEdge()||n(o.source())&&n(o.target())||(l.clean=!1),t&&l.clean||o.removed()||"none"!==o.pstyle("display").value&&("nodes"===s.group?i.push(o):r.push(o),l.clean=!0)}for(var u=0;u<i.length;u++){var c=i[u],h=c._private.rstyle,d=c.position();this.recalculateNodeLabelProjection(c),h.nodeX=d.x,h.nodeY=d.y,h.nodeW=c.pstyle("width").pfValue,h.nodeH=c.pstyle("height").pfValue}this.recalculateEdgeProjections(r);for(var p=0;p<r.length;p++){var g=r[p]._private,f=g.rstyle,v=g.rscratch;f.srcX=v.arrowStartX,f.srcY=v.arrowStartY,f.tgtX=v.arrowEndX,f.tgtY=v.arrowEndY,f.midX=v.midX,f.midY=v.midY,f.labelAngle=v.labelAngle,f.sourceLabelAngle=v.sourceLabelAngle,f.targetLabelAngle=v.targetLabelAngle}}}},Ql].forEach((function(e){z(Jl,e)}));var eu={getCachedImage:function(e,t,n){var r=this.imageCache=this.imageCache||{},i=r[e];if(i)return i.image.complete||i.image.addEventListener("load",n),i.image;var a=(i=r[e]=r[e]||{}).image=new Image;a.addEventListener("load",n),a.addEventListener("error",(function(){a.error=!0}));var o="data:";return e.substring(0,5).toLowerCase()===o||(t="null"===t?null:t,a.crossOrigin=t),a.src=e,a}},tu={registerBinding:function(e,t,n,r){var i=Array.prototype.slice.apply(arguments,[1]),a=this.binder(e);return a.on.apply(a,i)},binder:function(e){var t,n=this,r=e===window||e===document||e===document.body||(t=e,"undefined"!=typeof HTMLElement&&t instanceof HTMLElement);if(null==n.supportsPassiveEvents){var i=!1;try{var a=Object.defineProperty({},"passive",{get:function(){return i=!0,!0}});window.addEventListener("test",null,a)}catch(e){}n.supportsPassiveEvents=i}var o=function(t,i,a){var o=Array.prototype.slice.call(arguments);return r&&n.supportsPassiveEvents&&(o[2]={capture:null!=a&&a,passive:!1,once:!1}),n.bindings.push({target:e,args:o}),(e.addEventListener||e.on).apply(e,o),this};return{on:o,addEventListener:o,addListener:o,bind:o}},nodeIsDraggable:function(e){return e&&e.isNode()&&!e.locked()&&e.grabbable()},nodeIsGrabbable:function(e){return this.nodeIsDraggable(e)&&e.interactive()},load:function(){var e=this,t=function(e){return e.selected()},n=function(t,n,r,i){null==t&&(t=e.cy);for(var a=0;a<n.length;a++){var o=n[a];t.emit({originalEvent:r,type:o,position:i})}},r=function(e){return e.shiftKey||e.metaKey||e.ctrlKey},i=function(t,n){var r=!0;if(e.cy.hasCompoundNodes()&&t&&t.pannable()){for(var i=0;n&&i<n.length;i++)if((t=n[i]).isNode()&&t.isParent()&&!t.pannable()){r=!1;break}}else r=!0;return r},a=function(e){e[0]._private.rscratch.inDragLayer=!0},o=function(e){e[0]._private.rscratch.isGrabTarget=!0},s=function(e,t){var n=t.addToList;n.has(e)||!e.grabbable()||e.locked()||(n.merge(e),function(e){e[0]._private.grabbed=!0}(e))},l=function(t,n){n=n||{};var r=t.cy().hasCompoundNodes();n.inDragLayer&&(t.forEach(a),t.neighborhood().stdFilter((function(e){return!r||e.isEdge()})).forEach(a)),n.addToList&&t.forEach((function(e){s(e,n)})),function(e,t){if(e.cy().hasCompoundNodes()&&(null!=t.inDragLayer||null!=t.addToList)){var n=e.descendants();t.inDragLayer&&(n.forEach(a),n.connectedEdges().forEach(a)),t.addToList&&s(n,t)}}(t,n),h(t,{inDragLayer:n.inDragLayer}),e.updateCachedGrabbedEles()},u=l,c=function(t){t&&(e.getCachedZSortedEles().forEach((function(e){(function(e){e[0]._private.grabbed=!1})(e),function(e){e[0]._private.rscratch.inDragLayer=!1}(e),function(e){e[0]._private.rscratch.isGrabTarget=!1}(e)})),e.updateCachedGrabbedEles())},h=function(e,t){if((null!=t.inDragLayer||null!=t.addToList)&&e.cy().hasCompoundNodes()){var n=e.ancestors().orphans();if(!n.same(e)){var r=n.descendants().spawnSelf().merge(n).unmerge(e).unmerge(e.descendants()),i=r.connectedEdges();t.inDragLayer&&(i.forEach(a),r.forEach(a)),t.addToList&&r.forEach((function(e){s(e,t)}))}}},d=function(){null!=document.activeElement&&null!=document.activeElement.blur&&document.activeElement.blur()},p="undefined"!=typeof MutationObserver,g="undefined"!=typeof ResizeObserver;p?(e.removeObserver=new MutationObserver((function(t){for(var n=0;n<t.length;n++){var r=t[n].removedNodes;if(r)for(var i=0;i<r.length;i++)if(r[i]===e.container){e.destroy();break}}})),e.container.parentNode&&e.removeObserver.observe(e.container.parentNode,{childList:!0})):e.registerBinding(e.container,"DOMNodeRemoved",(function(t){e.destroy()}));var f=be((function(){e.cy.resize()}),100);p&&(e.styleObserver=new MutationObserver(f),e.styleObserver.observe(e.container,{attributes:!0})),e.registerBinding(window,"resize",f),g&&(e.resizeObserver=new ResizeObserver(f),e.resizeObserver.observe(e.container));var v=function(){e.invalidateContainerClientCoordsCache()};(function(e,t){for(;null!=e;)t(e),e=e.parentNode})(e.container,(function(t){e.registerBinding(t,"transitionend",v),e.registerBinding(t,"animationend",v),e.registerBinding(t,"scroll",v)})),e.registerBinding(e.container,"contextmenu",(function(e){e.preventDefault()}));var y,m,x,w=function(t){for(var n=e.findContainerClientCoords(),r=n[0],i=n[1],a=n[2],o=n[3],s=t.touches?t.touches:[t],l=!1,u=0;u<s.length;u++){var c=s[u];if(r<=c.clientX&&c.clientX<=r+a&&i<=c.clientY&&c.clientY<=i+o){l=!0;break}}if(!l)return!1;for(var h=e.container,d=t.target.parentNode,p=!1;d;){if(d===h){p=!0;break}d=d.parentNode}return!!p};e.registerBinding(e.container,"mousedown",(function(t){if(w(t)){t.preventDefault(),d(),e.hoverData.capture=!0,e.hoverData.which=t.which;var r=e.cy,i=[t.clientX,t.clientY],a=e.projectIntoViewport(i[0],i[1]),s=e.selection,c=e.findNearestElements(a[0],a[1],!0,!1),h=c[0],p=e.dragData.possibleDragElements;e.hoverData.mdownPos=a,e.hoverData.mdownGPos=i;if(3==t.which){e.hoverData.cxtStarted=!0;var g={originalEvent:t,type:"cxttapstart",position:{x:a[0],y:a[1]}};h?(h.activate(),h.emit(g),e.hoverData.down=h):r.emit(g),e.hoverData.downTime=(new Date).getTime(),e.hoverData.cxtDragged=!1}else if(1==t.which){if(h&&h.activate(),null!=h&&e.nodeIsGrabbable(h)){var f=function(e){return{originalEvent:t,type:e,position:{x:a[0],y:a[1]}}};if(o(h),h.selected()){p=e.dragData.possibleDragElements=r.collection();var v=r.$((function(t){return t.isNode()&&t.selected()&&e.nodeIsGrabbable(t)}));l(v,{addToList:p}),h.emit(f("grabon")),v.forEach((function(e){e.emit(f("grab"))}))}else p=e.dragData.possibleDragElements=r.collection(),u(h,{addToList:p}),h.emit(f("grabon")).emit(f("grab"));e.redrawHint("eles",!0),e.redrawHint("drag",!0)}e.hoverData.down=h,e.hoverData.downs=c,e.hoverData.downTime=(new Date).getTime(),n(h,["mousedown","tapstart","vmousedown"],t,{x:a[0],y:a[1]}),null==h?(s[4]=1,e.data.bgActivePosistion={x:a[0],y:a[1]},e.redrawHint("select",!0),e.redraw()):h.pannable()&&(s[4]=1),e.hoverData.tapholdCancelled=!1,clearTimeout(e.hoverData.tapholdTimeout),e.hoverData.tapholdTimeout=setTimeout((function(){if(!e.hoverData.tapholdCancelled){var n=e.hoverData.down;n?n.emit({originalEvent:t,type:"taphold",position:{x:a[0],y:a[1]}}):r.emit({originalEvent:t,type:"taphold",position:{x:a[0],y:a[1]}})}}),e.tapholdDuration)}s[0]=s[2]=a[0],s[1]=s[3]=a[1]}}),!1),e.registerBinding(window,"mousemove",(function(t){if(e.hoverData.capture||w(t)){var a=!1,o=e.cy,s=o.zoom(),u=[t.clientX,t.clientY],h=e.projectIntoViewport(u[0],u[1]),d=e.hoverData.mdownPos,p=e.hoverData.mdownGPos,g=e.selection,f=null;e.hoverData.draggingEles||e.hoverData.dragging||e.hoverData.selecting||(f=e.findNearestElement(h[0],h[1],!0,!1));var v,y=e.hoverData.last,m=e.hoverData.down,x=[h[0]-g[2],h[1]-g[3]],E=e.dragData.possibleDragElements;if(p){var _=u[0]-p[0],T=_*_,D=u[1]-p[1],C=T+D*D;e.hoverData.isOverThresholdDrag=v=C>=e.desktopTapThreshold2}var N=r(t);v&&(e.hoverData.tapholdCancelled=!0);a=!0,n(f,["mousemove","vmousemove","tapdrag"],t,{x:h[0],y:h[1]});var A=function(){e.data.bgActivePosistion=void 0,e.hoverData.selecting||o.emit({originalEvent:t,type:"boxstart",position:{x:h[0],y:h[1]}}),g[4]=1,e.hoverData.selecting=!0,e.redrawHint("select",!0),e.redraw()};if(3===e.hoverData.which){if(v){var L={originalEvent:t,type:"cxtdrag",position:{x:h[0],y:h[1]}};m?m.emit(L):o.emit(L),e.hoverData.cxtDragged=!0,e.hoverData.cxtOver&&f===e.hoverData.cxtOver||(e.hoverData.cxtOver&&e.hoverData.cxtOver.emit({originalEvent:t,type:"cxtdragout",position:{x:h[0],y:h[1]}}),e.hoverData.cxtOver=f,f&&f.emit({originalEvent:t,type:"cxtdragover",position:{x:h[0],y:h[1]}}))}}else if(e.hoverData.dragging){if(a=!0,o.panningEnabled()&&o.userPanningEnabled()){var k;if(e.hoverData.justStartedPan){var S=e.hoverData.mdownPos;k={x:(h[0]-S[0])*s,y:(h[1]-S[1])*s},e.hoverData.justStartedPan=!1}else k={x:x[0]*s,y:x[1]*s};o.panBy(k),o.emit("dragpan"),e.hoverData.dragged=!0}h=e.projectIntoViewport(t.clientX,t.clientY)}else if(1!=g[4]||null!=m&&!m.pannable()){if(m&&m.pannable()&&m.active()&&m.unactivate(),m&&m.grabbed()||f==y||(y&&n(y,["mouseout","tapdragout"],t,{x:h[0],y:h[1]}),f&&n(f,["mouseover","tapdragover"],t,{x:h[0],y:h[1]}),e.hoverData.last=f),m)if(v){if(o.boxSelectionEnabled()&&N)m&&m.grabbed()&&(c(E),m.emit("freeon"),E.emit("free"),e.dragData.didDrag&&(m.emit("dragfreeon"),E.emit("dragfree"))),A();else if(m&&m.grabbed()&&e.nodeIsDraggable(m)){var I=!e.dragData.didDrag;I&&e.redrawHint("eles",!0),e.dragData.didDrag=!0,e.hoverData.draggingEles||l(E,{inDragLayer:!0});var M={x:0,y:0};if(b(x[0])&&b(x[1])&&(M.x+=x[0],M.y+=x[1],I)){var O=e.hoverData.dragDelta;O&&b(O[0])&&b(O[1])&&(M.x+=O[0],M.y+=O[1])}e.hoverData.draggingEles=!0,E.silentShift(M).emit("position drag"),e.redrawHint("drag",!0),e.redraw()}}else!function(){var t=e.hoverData.dragDelta=e.hoverData.dragDelta||[];0===t.length?(t.push(x[0]),t.push(x[1])):(t[0]+=x[0],t[1]+=x[1])}();a=!0}else v&&(e.hoverData.dragging||!o.boxSelectionEnabled()||!N&&o.panningEnabled()&&o.userPanningEnabled()?!e.hoverData.selecting&&o.panningEnabled()&&o.userPanningEnabled()&&i(m,e.hoverData.downs)&&(e.hoverData.dragging=!0,e.hoverData.justStartedPan=!0,g[4]=0,e.data.bgActivePosistion=Tt(d),e.redrawHint("select",!0),e.redraw()):A(),m&&m.pannable()&&m.active()&&m.unactivate());return g[2]=h[0],g[3]=h[1],a?(t.stopPropagation&&t.stopPropagation(),t.preventDefault&&t.preventDefault(),!1):void 0}}),!1),e.registerBinding(window,"mouseup",(function(i){if(e.hoverData.capture){e.hoverData.capture=!1;var a=e.cy,o=e.projectIntoViewport(i.clientX,i.clientY),s=e.selection,l=e.findNearestElement(o[0],o[1],!0,!1),u=e.dragData.possibleDragElements,h=e.hoverData.down,d=r(i);if(e.data.bgActivePosistion&&(e.redrawHint("select",!0),e.redraw()),e.hoverData.tapholdCancelled=!0,e.data.bgActivePosistion=void 0,h&&h.unactivate(),3===e.hoverData.which){var p={originalEvent:i,type:"cxttapend",position:{x:o[0],y:o[1]}};if(h?h.emit(p):a.emit(p),!e.hoverData.cxtDragged){var g={originalEvent:i,type:"cxttap",position:{x:o[0],y:o[1]}};h?h.emit(g):a.emit(g)}e.hoverData.cxtDragged=!1,e.hoverData.which=null}else if(1===e.hoverData.which){if(n(l,["mouseup","tapend","vmouseup"],i,{x:o[0],y:o[1]}),e.dragData.didDrag||e.hoverData.dragged||e.hoverData.selecting||e.hoverData.isOverThresholdDrag||(n(h,["click","tap","vclick"],i,{x:o[0],y:o[1]}),m=!1,i.timeStamp-x<=a.multiClickDebounceTime()?(y&&clearTimeout(y),m=!0,x=null,n(h,["dblclick","dbltap","vdblclick"],i,{x:o[0],y:o[1]})):(y=setTimeout((function(){m||n(h,["oneclick","onetap","voneclick"],i,{x:o[0],y:o[1]})}),a.multiClickDebounceTime()),x=i.timeStamp)),null!=h||e.dragData.didDrag||e.hoverData.selecting||e.hoverData.dragged||r(i)||(a.$(t).unselect(["tapunselect"]),u.length>0&&e.redrawHint("eles",!0),e.dragData.possibleDragElements=u=a.collection()),l!=h||e.dragData.didDrag||e.hoverData.selecting||null!=l&&l._private.selectable&&(e.hoverData.dragging||("additive"===a.selectionType()||d?l.selected()?l.unselect(["tapunselect"]):l.select(["tapselect"]):d||(a.$(t).unmerge(l).unselect(["tapunselect"]),l.select(["tapselect"]))),e.redrawHint("eles",!0)),e.hoverData.selecting){var f=a.collection(e.getAllInBox(s[0],s[1],s[2],s[3]));e.redrawHint("select",!0),f.length>0&&e.redrawHint("eles",!0),a.emit({type:"boxend",originalEvent:i,position:{x:o[0],y:o[1]}});"additive"===a.selectionType()||d||a.$(t).unmerge(f).unselect(),f.emit("box").stdFilter((function(e){return e.selectable()&&!e.selected()})).select().emit("boxselect"),e.redraw()}if(e.hoverData.dragging&&(e.hoverData.dragging=!1,e.redrawHint("select",!0),e.redrawHint("eles",!0),e.redraw()),!s[4]){e.redrawHint("drag",!0),e.redrawHint("eles",!0);var v=h&&h.grabbed();c(u),v&&(h.emit("freeon"),u.emit("free"),e.dragData.didDrag&&(h.emit("dragfreeon"),u.emit("dragfree")))}}s[4]=0,e.hoverData.down=null,e.hoverData.cxtStarted=!1,e.hoverData.draggingEles=!1,e.hoverData.selecting=!1,e.hoverData.isOverThresholdDrag=!1,e.dragData.didDrag=!1,e.hoverData.dragged=!1,e.hoverData.dragDelta=[],e.hoverData.mdownPos=null,e.hoverData.mdownGPos=null}}),!1);var E,_,T,D,C,N,A,L,k,S,I,M,O,P=function(t){if(!e.scrollingPage){var n=e.cy,r=n.zoom(),i=n.pan(),a=e.projectIntoViewport(t.clientX,t.clientY),o=[a[0]*r+i.x,a[1]*r+i.y];if(e.hoverData.draggingEles||e.hoverData.dragging||e.hoverData.cxtStarted||0!==e.selection[4])t.preventDefault();else if(n.panningEnabled()&&n.userPanningEnabled()&&n.zoomingEnabled()&&n.userZoomingEnabled()){var s;t.preventDefault(),e.data.wheelZooming=!0,clearTimeout(e.data.wheelTimeout),e.data.wheelTimeout=setTimeout((function(){e.data.wheelZooming=!1,e.redrawHint("eles",!0),e.redraw()}),150),s=null!=t.deltaY?t.deltaY/-250:null!=t.wheelDeltaY?t.wheelDeltaY/1e3:t.wheelDelta/1e3,s*=e.wheelSensitivity,1===t.deltaMode&&(s*=33);var l=n.zoom()*Math.pow(10,s);"gesturechange"===t.type&&(l=e.gestureStartZoom*t.scale),n.zoom({level:l,renderedPosition:{x:o[0],y:o[1]}}),n.emit("gesturechange"===t.type?"pinchzoom":"scrollzoom")}}};e.registerBinding(e.container,"wheel",P,!0),e.registerBinding(window,"scroll",(function(t){e.scrollingPage=!0,clearTimeout(e.scrollingPageTimeout),e.scrollingPageTimeout=setTimeout((function(){e.scrollingPage=!1}),250)}),!0),e.registerBinding(e.container,"gesturestart",(function(t){e.gestureStartZoom=e.cy.zoom(),e.hasTouchStarted||t.preventDefault()}),!0),e.registerBinding(e.container,"gesturechange",(function(t){e.hasTouchStarted||P(t)}),!0),e.registerBinding(e.container,"mouseout",(function(t){var n=e.projectIntoViewport(t.clientX,t.clientY);e.cy.emit({originalEvent:t,type:"mouseout",position:{x:n[0],y:n[1]}})}),!1),e.registerBinding(e.container,"mouseover",(function(t){var n=e.projectIntoViewport(t.clientX,t.clientY);e.cy.emit({originalEvent:t,type:"mouseover",position:{x:n[0],y:n[1]}})}),!1);var R,B,F,z,G,Y,X,V=function(e,t,n,r){return Math.sqrt((n-e)*(n-e)+(r-t)*(r-t))},U=function(e,t,n,r){return(n-e)*(n-e)+(r-t)*(r-t)};if(e.registerBinding(e.container,"touchstart",R=function(t){if(e.hasTouchStarted=!0,w(t)){d(),e.touchData.capture=!0,e.data.bgActivePosistion=void 0;var r=e.cy,i=e.touchData.now,a=e.touchData.earlier;if(t.touches[0]){var s=e.projectIntoViewport(t.touches[0].clientX,t.touches[0].clientY);i[0]=s[0],i[1]=s[1]}if(t.touches[1]&&(s=e.projectIntoViewport(t.touches[1].clientX,t.touches[1].clientY),i[2]=s[0],i[3]=s[1]),t.touches[2]&&(s=e.projectIntoViewport(t.touches[2].clientX,t.touches[2].clientY),i[4]=s[0],i[5]=s[1]),t.touches[1]){e.touchData.singleTouchMoved=!0,c(e.dragData.touchDragEles);var h=e.findContainerClientCoords();k=h[0],S=h[1],I=h[2],M=h[3],E=t.touches[0].clientX-k,_=t.touches[0].clientY-S,T=t.touches[1].clientX-k,D=t.touches[1].clientY-S,O=0<=E&&E<=I&&0<=T&&T<=I&&0<=_&&_<=M&&0<=D&&D<=M;var p=r.pan(),g=r.zoom();C=V(E,_,T,D),N=U(E,_,T,D),L=[((A=[(E+T)/2,(_+D)/2])[0]-p.x)/g,(A[1]-p.y)/g];if(N<4e4&&!t.touches[2]){var f=e.findNearestElement(i[0],i[1],!0,!0),v=e.findNearestElement(i[2],i[3],!0,!0);return f&&f.isNode()?(f.activate().emit({originalEvent:t,type:"cxttapstart",position:{x:i[0],y:i[1]}}),e.touchData.start=f):v&&v.isNode()?(v.activate().emit({originalEvent:t,type:"cxttapstart",position:{x:i[0],y:i[1]}}),e.touchData.start=v):r.emit({originalEvent:t,type:"cxttapstart",position:{x:i[0],y:i[1]}}),e.touchData.start&&(e.touchData.start._private.grabbed=!1),e.touchData.cxt=!0,e.touchData.cxtDragged=!1,e.data.bgActivePosistion=void 0,void e.redraw()}}if(t.touches[2])r.boxSelectionEnabled()&&t.preventDefault();else if(t.touches[1]);else if(t.touches[0]){var y=e.findNearestElements(i[0],i[1],!0,!0),m=y[0];if(null!=m&&(m.activate(),e.touchData.start=m,e.touchData.starts=y,e.nodeIsGrabbable(m))){var b=e.dragData.touchDragEles=r.collection(),x=null;e.redrawHint("eles",!0),e.redrawHint("drag",!0),m.selected()?(x=r.$((function(t){return t.selected()&&e.nodeIsGrabbable(t)})),l(x,{addToList:b})):u(m,{addToList:b}),o(m);var P=function(e){return{originalEvent:t,type:e,position:{x:i[0],y:i[1]}}};m.emit(P("grabon")),x?x.forEach((function(e){e.emit(P("grab"))})):m.emit(P("grab"))}n(m,["touchstart","tapstart","vmousedown"],t,{x:i[0],y:i[1]}),null==m&&(e.data.bgActivePosistion={x:s[0],y:s[1]},e.redrawHint("select",!0),e.redraw()),e.touchData.singleTouchMoved=!1,e.touchData.singleTouchStartTime=+new Date,clearTimeout(e.touchData.tapholdTimeout),e.touchData.tapholdTimeout=setTimeout((function(){!1!==e.touchData.singleTouchMoved||e.pinching||e.touchData.selecting||n(e.touchData.start,["taphold"],t,{x:i[0],y:i[1]})}),e.tapholdDuration)}if(t.touches.length>=1){for(var R=e.touchData.startPosition=[],B=0;B<i.length;B++)R[B]=a[B]=i[B];var F=t.touches[0];e.touchData.startGPosition=[F.clientX,F.clientY]}}},!1),e.registerBinding(window,"touchmove",B=function(t){var r=e.touchData.capture;if(r||w(t)){var a=e.selection,o=e.cy,s=e.touchData.now,u=e.touchData.earlier,h=o.zoom();if(t.touches[0]){var d=e.projectIntoViewport(t.touches[0].clientX,t.touches[0].clientY);s[0]=d[0],s[1]=d[1]}t.touches[1]&&(d=e.projectIntoViewport(t.touches[1].clientX,t.touches[1].clientY),s[2]=d[0],s[3]=d[1]),t.touches[2]&&(d=e.projectIntoViewport(t.touches[2].clientX,t.touches[2].clientY),s[4]=d[0],s[5]=d[1]);var p,g=e.touchData.startGPosition;if(r&&t.touches[0]&&g){for(var f=[],v=0;v<s.length;v++)f[v]=s[v]-u[v];var y=t.touches[0].clientX-g[0],m=y*y,x=t.touches[0].clientY-g[1];p=m+x*x>=e.touchTapThreshold2}if(r&&e.touchData.cxt){t.preventDefault();var A=t.touches[0].clientX-k,I=t.touches[0].clientY-S,M=t.touches[1].clientX-k,P=t.touches[1].clientY-S,R=U(A,I,M,P);if(R/N>=2.25||R>=22500){e.touchData.cxt=!1,e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);var B={originalEvent:t,type:"cxttapend",position:{x:s[0],y:s[1]}};e.touchData.start?(e.touchData.start.unactivate().emit(B),e.touchData.start=null):o.emit(B)}}if(r&&e.touchData.cxt){B={originalEvent:t,type:"cxtdrag",position:{x:s[0],y:s[1]}},e.data.bgActivePosistion=void 0,e.redrawHint("select",!0),e.touchData.start?e.touchData.start.emit(B):o.emit(B),e.touchData.start&&(e.touchData.start._private.grabbed=!1),e.touchData.cxtDragged=!0;var F=e.findNearestElement(s[0],s[1],!0,!0);e.touchData.cxtOver&&F===e.touchData.cxtOver||(e.touchData.cxtOver&&e.touchData.cxtOver.emit({originalEvent:t,type:"cxtdragout",position:{x:s[0],y:s[1]}}),e.touchData.cxtOver=F,F&&F.emit({originalEvent:t,type:"cxtdragover",position:{x:s[0],y:s[1]}}))}else if(r&&t.touches[2]&&o.boxSelectionEnabled())t.preventDefault(),e.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,e.touchData.selecting||o.emit({originalEvent:t,type:"boxstart",position:{x:s[0],y:s[1]}}),e.touchData.selecting=!0,e.touchData.didSelect=!0,a[4]=1,a&&0!==a.length&&void 0!==a[0]?(a[2]=(s[0]+s[2]+s[4])/3,a[3]=(s[1]+s[3]+s[5])/3):(a[0]=(s[0]+s[2]+s[4])/3,a[1]=(s[1]+s[3]+s[5])/3,a[2]=(s[0]+s[2]+s[4])/3+1,a[3]=(s[1]+s[3]+s[5])/3+1),e.redrawHint("select",!0),e.redraw();else if(r&&t.touches[1]&&!e.touchData.didSelect&&o.zoomingEnabled()&&o.panningEnabled()&&o.userZoomingEnabled()&&o.userPanningEnabled()){if(t.preventDefault(),e.data.bgActivePosistion=void 0,e.redrawHint("select",!0),ee=e.dragData.touchDragEles){e.redrawHint("drag",!0);for(var z=0;z<ee.length;z++){var G=ee[z]._private;G.grabbed=!1,G.rscratch.inDragLayer=!1}}var Y=e.touchData.start,X=(A=t.touches[0].clientX-k,I=t.touches[0].clientY-S,M=t.touches[1].clientX-k,P=t.touches[1].clientY-S,V(A,I,M,P)),j=X/C;if(O){var q=(A-E+(M-T))/2,H=(I-_+(P-D))/2,W=o.zoom(),$=W*j,K=o.pan(),Z=L[0]*W+K.x,Q=L[1]*W+K.y,J={x:-$/W*(Z-K.x-q)+Z,y:-$/W*(Q-K.y-H)+Q};if(Y&&Y.active()){var ee=e.dragData.touchDragEles;c(ee),e.redrawHint("drag",!0),e.redrawHint("eles",!0),Y.unactivate().emit("freeon"),ee.emit("free"),e.dragData.didDrag&&(Y.emit("dragfreeon"),ee.emit("dragfree"))}o.viewport({zoom:$,pan:J,cancelOnFailedZoom:!0}),o.emit("pinchzoom"),C=X,E=A,_=I,T=M,D=P,e.pinching=!0}t.touches[0]&&(d=e.projectIntoViewport(t.touches[0].clientX,t.touches[0].clientY),s[0]=d[0],s[1]=d[1]),t.touches[1]&&(d=e.projectIntoViewport(t.touches[1].clientX,t.touches[1].clientY),s[2]=d[0],s[3]=d[1]),t.touches[2]&&(d=e.projectIntoViewport(t.touches[2].clientX,t.touches[2].clientY),s[4]=d[0],s[5]=d[1])}else if(t.touches[0]&&!e.touchData.didSelect){var te=e.touchData.start,ne=e.touchData.last;if(e.hoverData.draggingEles||e.swipePanning||(F=e.findNearestElement(s[0],s[1],!0,!0)),r&&null!=te&&t.preventDefault(),r&&null!=te&&e.nodeIsDraggable(te))if(p){ee=e.dragData.touchDragEles;var re=!e.dragData.didDrag;re&&l(ee,{inDragLayer:!0}),e.dragData.didDrag=!0;var ie={x:0,y:0};b(f[0])&&b(f[1])&&(ie.x+=f[0],ie.y+=f[1],re&&(e.redrawHint("eles",!0),(ae=e.touchData.dragDelta)&&b(ae[0])&&b(ae[1])&&(ie.x+=ae[0],ie.y+=ae[1]))),e.hoverData.draggingEles=!0,ee.silentShift(ie).emit("position drag"),e.redrawHint("drag",!0),e.touchData.startPosition[0]==u[0]&&e.touchData.startPosition[1]==u[1]&&e.redrawHint("eles",!0),e.redraw()}else{var ae;0===(ae=e.touchData.dragDelta=e.touchData.dragDelta||[]).length?(ae.push(f[0]),ae.push(f[1])):(ae[0]+=f[0],ae[1]+=f[1])}if(n(te||F,["touchmove","tapdrag","vmousemove"],t,{x:s[0],y:s[1]}),te&&te.grabbed()||F==ne||(ne&&ne.emit({originalEvent:t,type:"tapdragout",position:{x:s[0],y:s[1]}}),F&&F.emit({originalEvent:t,type:"tapdragover",position:{x:s[0],y:s[1]}})),e.touchData.last=F,r)for(z=0;z<s.length;z++)s[z]&&e.touchData.startPosition[z]&&p&&(e.touchData.singleTouchMoved=!0);r&&(null==te||te.pannable())&&o.panningEnabled()&&o.userPanningEnabled()&&(i(te,e.touchData.starts)&&(t.preventDefault(),e.data.bgActivePosistion||(e.data.bgActivePosistion=Tt(e.touchData.startPosition)),e.swipePanning?(o.panBy({x:f[0]*h,y:f[1]*h}),o.emit("dragpan")):p&&(e.swipePanning=!0,o.panBy({x:y*h,y:x*h}),o.emit("dragpan"),te&&(te.unactivate(),e.redrawHint("select",!0),e.touchData.start=null))),d=e.projectIntoViewport(t.touches[0].clientX,t.touches[0].clientY),s[0]=d[0],s[1]=d[1])}for(v=0;v<s.length;v++)u[v]=s[v];r&&t.touches.length>0&&!e.hoverData.draggingEles&&!e.swipePanning&&null!=e.data.bgActivePosistion&&(e.data.bgActivePosistion=void 0,e.redrawHint("select",!0),e.redraw())}},!1),e.registerBinding(window,"touchcancel",F=function(t){var n=e.touchData.start;e.touchData.capture=!1,n&&n.unactivate()}),e.registerBinding(window,"touchend",z=function(r){var i=e.touchData.start;if(e.touchData.capture){0===r.touches.length&&(e.touchData.capture=!1),r.preventDefault();var a=e.selection;e.swipePanning=!1,e.hoverData.draggingEles=!1;var o,s=e.cy,l=s.zoom(),u=e.touchData.now,h=e.touchData.earlier;if(r.touches[0]){var d=e.projectIntoViewport(r.touches[0].clientX,r.touches[0].clientY);u[0]=d[0],u[1]=d[1]}if(r.touches[1]&&(d=e.projectIntoViewport(r.touches[1].clientX,r.touches[1].clientY),u[2]=d[0],u[3]=d[1]),r.touches[2]&&(d=e.projectIntoViewport(r.touches[2].clientX,r.touches[2].clientY),u[4]=d[0],u[5]=d[1]),i&&i.unactivate(),e.touchData.cxt){if(o={originalEvent:r,type:"cxttapend",position:{x:u[0],y:u[1]}},i?i.emit(o):s.emit(o),!e.touchData.cxtDragged){var p={originalEvent:r,type:"cxttap",position:{x:u[0],y:u[1]}};i?i.emit(p):s.emit(p)}return e.touchData.start&&(e.touchData.start._private.grabbed=!1),e.touchData.cxt=!1,e.touchData.start=null,void e.redraw()}if(!r.touches[2]&&s.boxSelectionEnabled()&&e.touchData.selecting){e.touchData.selecting=!1;var g=s.collection(e.getAllInBox(a[0],a[1],a[2],a[3]));a[0]=void 0,a[1]=void 0,a[2]=void 0,a[3]=void 0,a[4]=0,e.redrawHint("select",!0),s.emit({type:"boxend",originalEvent:r,position:{x:u[0],y:u[1]}});g.emit("box").stdFilter((function(e){return e.selectable()&&!e.selected()})).select().emit("boxselect"),g.nonempty()&&e.redrawHint("eles",!0),e.redraw()}if(null!=i&&i.unactivate(),r.touches[2])e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);else if(r.touches[1]);else if(r.touches[0]);else if(!r.touches[0]){e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);var f=e.dragData.touchDragEles;if(null!=i){var v=i._private.grabbed;c(f),e.redrawHint("drag",!0),e.redrawHint("eles",!0),v&&(i.emit("freeon"),f.emit("free"),e.dragData.didDrag&&(i.emit("dragfreeon"),f.emit("dragfree"))),n(i,["touchend","tapend","vmouseup","tapdragout"],r,{x:u[0],y:u[1]}),i.unactivate(),e.touchData.start=null}else{var y=e.findNearestElement(u[0],u[1],!0,!0);n(y,["touchend","tapend","vmouseup","tapdragout"],r,{x:u[0],y:u[1]})}var m=e.touchData.startPosition[0]-u[0],b=m*m,x=e.touchData.startPosition[1]-u[1],w=(b+x*x)*l*l;e.touchData.singleTouchMoved||(i||s.$(":selected").unselect(["tapunselect"]),n(i,["tap","vclick"],r,{x:u[0],y:u[1]}),G=!1,r.timeStamp-X<=s.multiClickDebounceTime()?(Y&&clearTimeout(Y),G=!0,X=null,n(i,["dbltap","vdblclick"],r,{x:u[0],y:u[1]})):(Y=setTimeout((function(){G||n(i,["onetap","voneclick"],r,{x:u[0],y:u[1]})}),s.multiClickDebounceTime()),X=r.timeStamp)),null!=i&&!e.dragData.didDrag&&i._private.selectable&&w<e.touchTapThreshold2&&!e.pinching&&("single"===s.selectionType()?(s.$(t).unmerge(i).unselect(["tapunselect"]),i.select(["tapselect"])):i.selected()?i.unselect(["tapunselect"]):i.select(["tapselect"]),e.redrawHint("eles",!0)),e.touchData.singleTouchMoved=!0}for(var E=0;E<u.length;E++)h[E]=u[E];e.dragData.didDrag=!1,0===r.touches.length&&(e.touchData.dragDelta=[],e.touchData.startPosition=null,e.touchData.startGPosition=null,e.touchData.didSelect=!1),r.touches.length<2&&(1===r.touches.length&&(e.touchData.startGPosition=[r.touches[0].clientX,r.touches[0].clientY]),e.pinching=!1,e.redrawHint("eles",!0),e.redraw())}},!1),"undefined"==typeof TouchEvent){var j=[],q=function(e){return{clientX:e.clientX,clientY:e.clientY,force:1,identifier:e.pointerId,pageX:e.pageX,pageY:e.pageY,radiusX:e.width/2,radiusY:e.height/2,screenX:e.screenX,screenY:e.screenY,target:e.target}},H=function(e){j.push(function(e){return{event:e,touch:q(e)}}(e))},W=function(e){for(var t=0;t<j.length;t++)if(j[t].event.pointerId===e.pointerId)return void j.splice(t,1)},$=function(e){e.touches=j.map((function(e){return e.touch}))},K=function(e){return"mouse"===e.pointerType||4===e.pointerType};e.registerBinding(e.container,"pointerdown",(function(e){K(e)||(e.preventDefault(),H(e),$(e),R(e))})),e.registerBinding(e.container,"pointerup",(function(e){K(e)||(W(e),$(e),z(e))})),e.registerBinding(e.container,"pointercancel",(function(e){K(e)||(W(e),$(e),F())})),e.registerBinding(e.container,"pointermove",(function(e){K(e)||(e.preventDefault(),function(e){var t=j.filter((function(t){return t.event.pointerId===e.pointerId}))[0];t.event=e,t.touch=q(e)}(e),$(e),B(e))}))}}},nu={generatePolygon:function(e,t){return this.nodeShapes[e]={renderer:this,name:e,points:t,draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl("polygon",e,t,n,r,i,this.points)},intersectLine:function(e,t,n,r,i,a,o){return tn(i,a,this.points,e,t,n/2,r/2,o)},checkPoint:function(e,t,n,r,i,a,o){return Wt(e,t,this.points,a,o,r,i,[0,-1],n)}}},generateEllipse:function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},intersectLine:function(e,t,n,r,i,a,o){return function(e,t,n,r,i,a){var o=n-e,s=r-t;o/=i,s/=a;var l=Math.sqrt(o*o+s*s),u=l-1;if(u<0)return[];var c=u/l;return[(n-e)*c+e,(r-t)*c+t]}(i,a,e,t,n/2+o,r/2+o)},checkPoint:function(e,t,n,r,i,a,o){return Zt(e,t,r,i,a,o,n)}}},generateRoundPolygon:function(e,t){for(var n=new Array(2*t.length),r=0;r<t.length/2;r++){var i,a=2*r;i=r<t.length/2-1?2*(r+1):0,n[4*r]=t[a],n[4*r+1]=t[a+1];var o=t[i]-t[a],s=t[i+1]-t[a+1],l=Math.sqrt(o*o+s*s);n[4*r+2]=o/l,n[4*r+3]=s/l}return this.nodeShapes[e]={renderer:this,name:e,points:n,draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl("round-polygon",e,t,n,r,i,this.points)},intersectLine:function(e,t,n,r,i,a,o){return function(e,t,n,r,i,a,o,s){for(var l,u=[],c=new Array(n.length),h=a/2,d=o/2,p=ln(a,o),g=0;g<n.length/4;g++){var f,v;f=0===g?n.length-2:4*g-2,v=4*g+2;var y=r+h*n[4*g],m=i+d*n[4*g+1],b=-n[f]*n[v]-n[f+1]*n[v+1],x=p/Math.tan(Math.acos(b)/2),w=y-x*n[f],E=m-x*n[f+1],_=y+x*n[v],T=m+x*n[v+1];0===g?(c[n.length-2]=w,c[n.length-1]=E):(c[4*g-2]=w,c[4*g-1]=E),c[4*g]=_,c[4*g+1]=T;var D=n[f+1],C=-n[f];D*n[v]+C*n[v+1]<0&&(D*=-1,C*=-1),0!==(l=Qt(e,t,r,i,w+D*p,E+C*p,p)).length&&u.push(l[0],l[1])}for(var N=0;N<c.length/4;N++)0!==(l=en(e,t,r,i,c[4*N],c[4*N+1],c[4*N+2],c[4*N+3],!1)).length&&u.push(l[0],l[1]);if(u.length>2){for(var A=[u[0],u[1]],L=Math.pow(A[0]-e,2)+Math.pow(A[1]-t,2),k=1;k<u.length/2;k++){var S=Math.pow(u[2*k]-e,2)+Math.pow(u[2*k+1]-t,2);S<=L&&(A[0]=u[2*k],A[1]=u[2*k+1],L=S)}return A}return u}(i,a,this.points,e,t,n,r)},checkPoint:function(e,t,n,r,i,a,o){return function(e,t,n,r,i,a,o){for(var s=new Array(n.length),l=a/2,u=o/2,c=ln(a,o),h=c*c,d=0;d<n.length/4;d++){var p,g;p=0===d?n.length-2:4*d-2,g=4*d+2;var f=r+l*n[4*d],v=i+u*n[4*d+1],y=-n[p]*n[g]-n[p+1]*n[g+1],m=c/Math.tan(Math.acos(y)/2),b=f-m*n[p],x=v-m*n[p+1],w=f+m*n[g],E=v+m*n[g+1];s[4*d]=b,s[4*d+1]=x,s[4*d+2]=w,s[4*d+3]=E;var _=n[p+1],T=-n[p];_*n[g]+T*n[g+1]<0&&(_*=-1,T*=-1);var D=b+_*c,C=x+T*c;if(Math.pow(D-e,2)+Math.pow(C-t,2)<=h)return!0}return Ht(e,t,s)}(e,t,this.points,a,o,r,i)}}},generateRoundRectangle:function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:rn(4,0),draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},intersectLine:function(e,t,n,r,i,a,o){return Xt(i,a,e,t,n,r,o)},checkPoint:function(e,t,n,r,i,a,o){var s=sn(r,i),l=2*s;return!!(Wt(e,t,this.points,a,o,r,i-l,[0,-1],n)||Wt(e,t,this.points,a,o,r-l,i,[0,-1],n)||Zt(e,t,l,l,a-r/2+s,o-i/2+s,n)||Zt(e,t,l,l,a+r/2-s,o-i/2+s,n)||Zt(e,t,l,l,a+r/2-s,o+i/2-s,n)||Zt(e,t,l,l,a-r/2+s,o+i/2-s,n))}}},generateCutRectangle:function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:8,points:rn(4,0),draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},generateCutTrianglePts:function(e,t,n,r){var i=this.cornerLength,a=t/2,o=e/2,s=n-o,l=n+o,u=r-a,c=r+a;return{topLeft:[s,u+i,s+i,u,s+i,u+i],topRight:[l-i,u,l,u+i,l-i,u+i],bottomRight:[l,c-i,l-i,c,l-i,c-i],bottomLeft:[s+i,c,s,c-i,s+i,c-i]}},intersectLine:function(e,t,n,r,i,a,o){var s=this.generateCutTrianglePts(n+2*o,r+2*o,e,t),l=[].concat.apply([],[s.topLeft.splice(0,4),s.topRight.splice(0,4),s.bottomRight.splice(0,4),s.bottomLeft.splice(0,4)]);return tn(i,a,l,e,t)},checkPoint:function(e,t,n,r,i,a,o){if(Wt(e,t,this.points,a,o,r,i-2*this.cornerLength,[0,-1],n))return!0;if(Wt(e,t,this.points,a,o,r-2*this.cornerLength,i,[0,-1],n))return!0;var s=this.generateCutTrianglePts(r,i,a,o);return Ht(e,t,s.topLeft)||Ht(e,t,s.topRight)||Ht(e,t,s.bottomRight)||Ht(e,t,s.bottomLeft)}}},generateBarrel:function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:rn(4,0),draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},intersectLine:function(e,t,n,r,i,a,o){var s=this.generateBarrelBezierPts(n+2*o,r+2*o,e,t),l=function(e){var t=It({x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},.15),n=It({x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},.5),r=It({x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},.85);return[e[0],e[1],t.x,t.y,n.x,n.y,r.x,r.y,e[4],e[5]]},u=[].concat(l(s.topLeft),l(s.topRight),l(s.bottomRight),l(s.bottomLeft));return tn(i,a,u,e,t)},generateBarrelBezierPts:function(e,t,n,r){var i=t/2,a=e/2,o=n-a,s=n+a,l=r-i,u=r+i,c=un(e,t),h=c.heightOffset,d=c.widthOffset,p=c.ctrlPtOffsetPct*e,g={topLeft:[o,l+h,o+p,l,o+d,l],topRight:[s-d,l,s-p,l,s,l+h],bottomRight:[s,u-h,s-p,u,s-d,u],bottomLeft:[o+d,u,o+p,u,o,u-h]};return g.topLeft.isTop=!0,g.topRight.isTop=!0,g.bottomLeft.isBottom=!0,g.bottomRight.isBottom=!0,g},checkPoint:function(e,t,n,r,i,a,o){var s=un(r,i),l=s.heightOffset,u=s.widthOffset;if(Wt(e,t,this.points,a,o,r,i-2*l,[0,-1],n))return!0;if(Wt(e,t,this.points,a,o,r-2*u,i,[0,-1],n))return!0;for(var c=this.generateBarrelBezierPts(r,i,a,o),h=function(e,t,n){var r,i,a=n[4],o=n[2],s=n[0],l=n[5],u=n[1],c=Math.min(a,s),h=Math.max(a,s),d=Math.min(l,u),p=Math.max(l,u);if(c<=e&&e<=h&&d<=t&&t<=p){var g=[(r=a)-2*(i=o)+s,2*(i-r),r],f=function(e,t,n,r){var i=t*t-4*e*(n-=r);if(i<0)return[];var a=Math.sqrt(i),o=2*e;return[(-t+a)/o,(-t-a)/o]}(g[0],g[1],g[2],e).filter((function(e){return 0<=e&&e<=1}));if(f.length>0)return f[0]}return null},d=Object.keys(c),p=0;p<d.length;p++){var g=c[d[p]],f=h(e,t,g);if(null!=f){var v=g[5],y=g[3],m=g[1],b=St(v,y,m,f);if(g.isTop&&b<=t)return!0;if(g.isBottom&&t<=b)return!0}}return!1}}},generateBottomRoundrectangle:function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:rn(4,0),draw:function(e,t,n,r,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},intersectLine:function(e,t,n,r,i,a,o){var s=t-(r/2+o),l=en(i,a,e,t,e-(n/2+o),s,e+(n/2+o),s,!1);return l.length>0?l:Xt(i,a,e,t,n,r,o)},checkPoint:function(e,t,n,r,i,a,o){var s=sn(r,i),l=2*s;if(Wt(e,t,this.points,a,o,r,i-l,[0,-1],n))return!0;if(Wt(e,t,this.points,a,o,r-l,i,[0,-1],n))return!0;var u=r/2+2*n,c=i/2+2*n;return!!Ht(e,t,[a-u,o-c,a-u,o,a+u,o,a+u,o-c])||!!Zt(e,t,l,l,a+r/2-s,o+i/2-s,n)||!!Zt(e,t,l,l,a-r/2+s,o+i/2-s,n)}}},registerNodeShapes:function(){var e=this.nodeShapes={},t=this;this.generateEllipse(),this.generatePolygon("triangle",rn(3,0)),this.generateRoundPolygon("round-triangle",rn(3,0)),this.generatePolygon("rectangle",rn(4,0)),e.square=e.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();var n=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",n),this.generateRoundPolygon("round-diamond",n),this.generatePolygon("pentagon",rn(5,0)),this.generateRoundPolygon("round-pentagon",rn(5,0)),this.generatePolygon("hexagon",rn(6,0)),this.generateRoundPolygon("round-hexagon",rn(6,0)),this.generatePolygon("heptagon",rn(7,0)),this.generateRoundPolygon("round-heptagon",rn(7,0)),this.generatePolygon("octagon",rn(8,0)),this.generateRoundPolygon("round-octagon",rn(8,0));var r=new Array(20),i=on(5,0),a=on(5,Math.PI/5),o=.5*(3-Math.sqrt(5));o*=1.57;for(var s=0;s<a.length/2;s++)a[2*s]*=o,a[2*s+1]*=o;for(s=0;s<5;s++)r[4*s]=i[2*s],r[4*s+1]=i[2*s+1],r[4*s+2]=a[2*s],r[4*s+3]=a[2*s+1];r=an(r),this.generatePolygon("star",r),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);var l=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",l),this.generateRoundPolygon("round-tag",l),e.makePolygon=function(e){var n,r="polygon-"+e.join("$");return(n=this[r])?n:t.generatePolygon(r,e)}}},ru={timeToRender:function(){return this.redrawTotalTime/this.redrawCount},redraw:function(e){e=e||$e();var t=this;void 0===t.averageRedrawTime&&(t.averageRedrawTime=0),void 0===t.lastRedrawTime&&(t.lastRedrawTime=0),void 0===t.lastDrawTime&&(t.lastDrawTime=0),t.requestedFrame=!0,t.renderOptions=e},beforeRender:function(e,t){if(!this.destroyed){null==t&&Ve("Priority is not optional for beforeRender");var n=this.beforeRenderCallbacks;n.push({fn:e,priority:t}),n.sort((function(e,t){return t.priority-e.priority}))}}},iu=function(e,t,n){for(var r=e.beforeRenderCallbacks,i=0;i<r.length;i++)r[i].fn(t,n)};ru.startRenderLoop=function(){var e=this,t=e.cy;if(!e.renderLoopStarted){e.renderLoopStarted=!0;_e((function n(r){if(!e.destroyed){if(t.batching());else if(e.requestedFrame&&!e.skipFrame){iu(e,!0,r);var i=Te();e.render(e.renderOptions);var a=e.lastDrawTime=Te();void 0===e.averageRedrawTime&&(e.averageRedrawTime=a-i),void 0===e.redrawCount&&(e.redrawCount=0),e.redrawCount++,void 0===e.redrawTotalTime&&(e.redrawTotalTime=0);var o=a-i;e.redrawTotalTime+=o,e.lastRedrawTime=o,e.averageRedrawTime=e.averageRedrawTime/2+o/2,e.requestedFrame=!1}else iu(e,!1,r);e.skipFrame=!1,_e(n)}}))}};var au=function(e){this.init(e)},ou=au.prototype;ou.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"],ou.init=function(e){var t=this;t.options=e,t.cy=e.cy;var n=t.container=e.cy.container();if(l){var r=l.document,i=r.head,a="__________cytoscape_stylesheet",o="__________cytoscape_container",s=null!=r.getElementById(a);if(n.className.indexOf(o)<0&&(n.className=(n.className||"")+" "+o),!s){var u=r.createElement("style");u.id=a,u.textContent="."+o+" { position: relative; }",i.insertBefore(u,i.children[0])}"static"===l.getComputedStyle(n).getPropertyValue("position")&&je("A Cytoscape container has style position:static and so can not use UI extensions properly")}t.selection=[void 0,void 0,void 0,void 0,0],t.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],t.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},t.dragData={possibleDragElements:[]},t.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},t.redraws=0,t.showFps=e.showFps,t.debug=e.debug,t.hideEdgesOnViewport=e.hideEdgesOnViewport,t.textureOnViewport=e.textureOnViewport,t.wheelSensitivity=e.wheelSensitivity,t.motionBlurEnabled=e.motionBlur,t.forcedPixelRatio=b(e.pixelRatio)?e.pixelRatio:null,t.motionBlur=e.motionBlur,t.motionBlurOpacity=e.motionBlurOpacity,t.motionBlurTransparency=1-t.motionBlurOpacity,t.motionBlurPxRatio=1,t.mbPxRBlurry=1,t.minMbLowQualFrames=4,t.fullQualityMb=!1,t.clearedForMotionBlur=[],t.desktopTapThreshold=e.desktopTapThreshold,t.desktopTapThreshold2=e.desktopTapThreshold*e.desktopTapThreshold,t.touchTapThreshold=e.touchTapThreshold,t.touchTapThreshold2=e.touchTapThreshold*e.touchTapThreshold,t.tapholdDuration=500,t.bindings=[],t.beforeRenderCallbacks=[],t.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},t.registerNodeShapes(),t.registerArrowShapes(),t.registerCalculationListeners()},ou.notify=function(e,t){var n=this,r=n.cy;this.destroyed||("init"!==e?"destroy"!==e?(("add"===e||"remove"===e||"move"===e&&r.hasCompoundNodes()||"load"===e||"zorder"===e||"mount"===e)&&n.invalidateCachedZSortedEles(),"viewport"===e&&n.redrawHint("select",!0),"load"!==e&&"resize"!==e&&"mount"!==e||(n.invalidateContainerClientCoordsCache(),n.matchCanvasSize(n.container)),n.redrawHint("eles",!0),n.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()):n.destroy():n.load())},ou.destroy=function(){var e=this;e.destroyed=!0,e.cy.stopAnimationLoop();for(var t=0;t<e.bindings.length;t++){var n=e.bindings[t],r=n.target;(r.off||r.removeEventListener).apply(r,n.args)}if(e.bindings=[],e.beforeRenderCallbacks=[],e.onUpdateEleCalcsFns=[],e.removeObserver&&e.removeObserver.disconnect(),e.styleObserver&&e.styleObserver.disconnect(),e.resizeObserver&&e.resizeObserver.disconnect(),e.labelCalcDiv)try{document.body.removeChild(e.labelCalcDiv)}catch(e){}},ou.isHeadless=function(){return!1},[zl,Jl,eu,tu,nu,ru].forEach((function(e){z(ou,e)}));var su=1e3/60,lu=function(e){return function(){var t=this,n=this.renderer;if(!t.dequeueingSetup){t.dequeueingSetup=!0;var r=be((function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()}),e.deqRedrawThreshold),i=e.priority||Xe;n.beforeRender((function(i,a){var o=Te(),s=n.averageRedrawTime,l=n.lastRedrawTime,u=[],c=n.cy.extent(),h=n.getPixelRatio();for(i||n.flushRenderedStyleQueue();;){var d=Te(),p=d-o,g=d-a;if(l<su){var f=su-(i?s:0);if(g>=e.deqFastCost*f)break}else if(i){if(p>=e.deqCost*l||p>=e.deqAvgCost*s)break}else if(g>=e.deqNoDrawCost*su)break;var v=e.deq(t,h,c);if(!(v.length>0))break;for(var y=0;y<v.length;y++)u.push(v[y])}u.length>0&&(e.onDeqd(t,u),!i&&e.shouldRedraw(t,u,h,c)&&r())}),i(t))}}},uu=function(){function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Ge;t(this,e),this.idsByKey=new tt,this.keyForId=new tt,this.cachesByLvl=new tt,this.lvls=[],this.getKey=n,this.doesEleInvalidateKey=r}return i(e,[{key:"getIdsFor",value:function(e){null==e&&Ve("Can not get id list for null key");var t=this.idsByKey,n=this.idsByKey.get(e);return n||(n=new rt,t.set(e,n)),n}},{key:"addIdForKey",value:function(e,t){null!=e&&this.getIdsFor(e).add(t)}},{key:"deleteIdForKey",value:function(e,t){null!=e&&this.getIdsFor(e).delete(t)}},{key:"getNumberOfIdsForKey",value:function(e){return null==e?0:this.getIdsFor(e).size}},{key:"updateKeyMappingFor",value:function(e){var t=e.id(),n=this.keyForId.get(t),r=this.getKey(e);this.deleteIdForKey(n,t),this.addIdForKey(r,t),this.keyForId.set(t,r)}},{key:"deleteKeyMappingFor",value:function(e){var t=e.id(),n=this.keyForId.get(t);this.deleteIdForKey(n,t),this.keyForId.delete(t)}},{key:"keyHasChangedFor",value:function(e){var t=e.id();return this.keyForId.get(t)!==this.getKey(e)}},{key:"isInvalid",value:function(e){return this.keyHasChangedFor(e)||this.doesEleInvalidateKey(e)}},{key:"getCachesAt",value:function(e){var t=this.cachesByLvl,n=this.lvls,r=t.get(e);return r||(r=new tt,t.set(e,r),n.push(e)),r}},{key:"getCache",value:function(e,t){return this.getCachesAt(t).get(e)}},{key:"get",value:function(e,t){var n=this.getKey(e),r=this.getCache(n,t);return null!=r&&this.updateKeyMappingFor(e),r}},{key:"getForCachedKey",value:function(e,t){var n=this.keyForId.get(e.id());return this.getCache(n,t)}},{key:"hasCache",value:function(e,t){return this.getCachesAt(t).has(e)}},{key:"has",value:function(e,t){var n=this.getKey(e);return this.hasCache(n,t)}},{key:"setCache",value:function(e,t,n){n.key=e,this.getCachesAt(t).set(e,n)}},{key:"set",value:function(e,t,n){var r=this.getKey(e);this.setCache(r,t,n),this.updateKeyMappingFor(e)}},{key:"deleteCache",value:function(e,t){this.getCachesAt(t).delete(e)}},{key:"delete",value:function(e,t){var n=this.getKey(e);this.deleteCache(n,t)}},{key:"invalidateKey",value:function(e){var t=this;this.lvls.forEach((function(n){return t.deleteCache(e,n)}))}},{key:"invalidate",value:function(e){var t=e.id(),n=this.keyForId.get(t);this.deleteKeyMappingFor(e);var r=this.doesEleInvalidateKey(e);return r&&this.invalidateKey(n),r||0===this.getNumberOfIdsForKey(n)}}]),e}(),cu={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},hu=Ke({getKey:null,doesEleInvalidateKey:Ge,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:ze,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),du=function(e,t){var n=this;n.renderer=e,n.onDequeues=[];var r=hu(t);z(n,r),n.lookup=new uu(r.getKey,r.doesEleInvalidateKey),n.setupDequeueing()},pu=du.prototype;pu.reasons=cu,pu.getTextureQueue=function(e){var t=this;return t.eleImgCaches=t.eleImgCaches||{},t.eleImgCaches[e]=t.eleImgCaches[e]||[]},pu.getRetiredTextureQueue=function(e){var t=this.eleImgCaches.retired=this.eleImgCaches.retired||{};return t[e]=t[e]||[]},pu.getElementQueue=function(){return this.eleCacheQueue=this.eleCacheQueue||new lt((function(e,t){return t.reqs-e.reqs}))},pu.getElementKeyToQueue=function(){return this.eleKeyToCacheQueue=this.eleKeyToCacheQueue||{}},pu.getElement=function(e,t,n,r,i){var a=this,o=this.renderer,s=o.cy.zoom(),l=this.lookup;if(!t||0===t.w||0===t.h||isNaN(t.w)||isNaN(t.h)||!e.visible()||e.removed())return null;if(!a.allowEdgeTxrCaching&&e.isEdge()||!a.allowParentTxrCaching&&e.isParent())return null;if(null==r&&(r=Math.ceil(Ct(s*n))),r<-4)r=-4;else if(s>=7.99||r>3)return null;var u=Math.pow(2,r),c=t.h*u,h=t.w*u,d=o.eleTextBiggerThanMin(e,u);if(!this.isVisible(e,d))return null;var p,g=l.get(e,r);if(g&&g.invalidated&&(g.invalidated=!1,g.texture.invalidatedWidth-=g.width),g)return g;if(p=c<=25?25:c<=50?50:50*Math.ceil(c/50),c>1024||h>1024)return null;var f=a.getTextureQueue(p),v=f[f.length-2],y=function(){return a.recycleTexture(p,h)||a.addTexture(p,h)};v||(v=f[f.length-1]),v||(v=y()),v.width-v.usedWidth<h&&(v=y());for(var m,b=function(e){return e&&e.scaledLabelShown===d},x=i&&i===cu.dequeue,w=i&&i===cu.highQuality,E=i&&i===cu.downscale,_=r+1;_<=3;_++){var T=l.get(e,_);if(T){m=T;break}}var D=m&&m.level===r+1?m:null,C=function(){v.context.drawImage(D.texture.canvas,D.x,0,D.width,D.height,v.usedWidth,0,h,c)};if(v.context.setTransform(1,0,0,1,0,0),v.context.clearRect(v.usedWidth,0,h,p),b(D))C();else if(b(m)){if(!w)return a.queueElement(e,m.level-1),m;for(var N=m.level;N>r;N--)D=a.getElement(e,t,n,N,cu.downscale);C()}else{var A;if(!x&&!w&&!E)for(var L=r-1;L>=-4;L--){var k=l.get(e,L);if(k){A=k;break}}if(b(A))return a.queueElement(e,r),A;v.context.translate(v.usedWidth,0),v.context.scale(u,u),this.drawElement(v.context,e,t,d,!1),v.context.scale(1/u,1/u),v.context.translate(-v.usedWidth,0)}return g={x:v.usedWidth,texture:v,level:r,scale:u,width:h,height:c,scaledLabelShown:d},v.usedWidth+=Math.ceil(h+8),v.eleCaches.push(g),l.set(e,r,g),a.checkTextureFullness(v),g},pu.invalidateElements=function(e){for(var t=0;t<e.length;t++)this.invalidateElement(e[t])},pu.invalidateElement=function(e){var t=this,n=t.lookup,r=[];if(n.isInvalid(e)){for(var i=-4;i<=3;i++){var a=n.getForCachedKey(e,i);a&&r.push(a)}if(n.invalidate(e))for(var o=0;o<r.length;o++){var s=r[o],l=s.texture;l.invalidatedWidth+=s.width,s.invalidated=!0,t.checkTextureUtility(l)}t.removeFromQueue(e)}},pu.checkTextureUtility=function(e){e.invalidatedWidth>=.2*e.width&&this.retireTexture(e)},pu.checkTextureFullness=function(e){var t=this.getTextureQueue(e.height);e.usedWidth/e.width>.8&&e.fullnessChecks>=10?Ze(t,e):e.fullnessChecks++},pu.retireTexture=function(e){var t=e.height,n=this.getTextureQueue(t),r=this.lookup;Ze(n,e),e.retired=!0;for(var i=e.eleCaches,a=0;a<i.length;a++){var o=i[a];r.deleteCache(o.key,o.level)}Qe(i),this.getRetiredTextureQueue(t).push(e)},pu.addTexture=function(e,t){var n={};return this.getTextureQueue(e).push(n),n.eleCaches=[],n.height=e,n.width=Math.max(1024,t),n.usedWidth=0,n.invalidatedWidth=0,n.fullnessChecks=0,n.canvas=this.renderer.makeOffscreenCanvas(n.width,n.height),n.context=n.canvas.getContext("2d"),n},pu.recycleTexture=function(e,t){for(var n=this.getTextureQueue(e),r=this.getRetiredTextureQueue(e),i=0;i<r.length;i++){var a=r[i];if(a.width>=t)return a.retired=!1,a.usedWidth=0,a.invalidatedWidth=0,a.fullnessChecks=0,Qe(a.eleCaches),a.context.setTransform(1,0,0,1,0,0),a.context.clearRect(0,0,a.width,a.height),Ze(r,a),n.push(a),a}},pu.queueElement=function(e,t){var n=this.getElementQueue(),r=this.getElementKeyToQueue(),i=this.getKey(e),a=r[i];if(a)a.level=Math.max(a.level,t),a.eles.merge(e),a.reqs++,n.updateItem(a);else{var o={eles:e.spawn().merge(e),level:t,reqs:1,key:i};n.push(o),r[i]=o}},pu.dequeue=function(e){for(var t=this,n=t.getElementQueue(),r=t.getElementKeyToQueue(),i=[],a=t.lookup,o=0;o<1&&n.size()>0;o++){var s=n.pop(),l=s.key,u=s.eles[0],c=a.hasCache(u,s.level);if(r[l]=null,!c){i.push(s);var h=t.getBoundingBox(u);t.getElement(u,h,e,s.level,cu.dequeue)}}return i},pu.removeFromQueue=function(e){var t=this.getElementQueue(),n=this.getElementKeyToQueue(),r=this.getKey(e),i=n[r];null!=i&&(1===i.eles.length?(i.reqs=Fe,t.updateItem(i),t.pop(),n[r]=null):i.eles.unmerge(e))},pu.onDequeue=function(e){this.onDequeues.push(e)},pu.offDequeue=function(e){Ze(this.onDequeues,e)},pu.setupDequeueing=lu({deqRedrawThreshold:100,deqCost:.15,deqAvgCost:.1,deqNoDrawCost:.9,deqFastCost:.9,deq:function(e,t,n){return e.dequeue(t,n)},onDeqd:function(e,t){for(var n=0;n<e.onDequeues.length;n++)(0,e.onDequeues[n])(t)},shouldRedraw:function(e,t,n,r){for(var i=0;i<t.length;i++)for(var a=t[i].eles,o=0;o<a.length;o++){var s=a[o].boundingBox();if(zt(s,r))return!0}return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}});var gu=function(e){var t=this,n=t.renderer=e,r=n.cy;t.layersByLevel={},t.firstGet=!0,t.lastInvalidationTime=Te()-500,t.skipping=!1,t.eleTxrDeqs=r.collection(),t.scheduleElementRefinement=be((function(){t.refineElementTextures(t.eleTxrDeqs),t.eleTxrDeqs.unmerge(t.eleTxrDeqs)}),50),n.beforeRender((function(e,n){n-t.lastInvalidationTime<=250?t.skipping=!0:t.skipping=!1}),n.beforeRenderPriorities.lyrTxrSkip);t.layersQueue=new lt((function(e,t){return t.reqs-e.reqs})),t.setupDequeueing()},fu=gu.prototype,vu=0,yu=Math.pow(2,53)-1;fu.makeLayer=function(e,t){var n=Math.pow(2,t),r=Math.ceil(e.w*n),i=Math.ceil(e.h*n),a=this.renderer.makeOffscreenCanvas(r,i),o={id:vu=++vu%yu,bb:e,level:t,width:r,height:i,canvas:a,context:a.getContext("2d"),eles:[],elesQueue:[],reqs:0},s=o.context,l=-o.bb.x1,u=-o.bb.y1;return s.scale(n,n),s.translate(l,u),o},fu.getLayers=function(e,t,n){var r=this,i=r.renderer.cy.zoom(),a=r.firstGet;if(r.firstGet=!1,null==n)if((n=Math.ceil(Ct(i*t)))<-4)n=-4;else if(i>=3.99||n>2)return null;r.validateLayersElesOrdering(n,e);var o,s,l=r.layersByLevel,u=Math.pow(2,n),c=l[n]=l[n]||[];if(r.levelIsComplete(n,e))return c;!function(){var t=function(t){if(r.validateLayersElesOrdering(t,e),r.levelIsComplete(t,e))return s=l[t],!0},i=function(e){if(!s)for(var r=n+e;-4<=r&&r<=2&&!t(r);r+=e);};i(1),i(-1);for(var a=c.length-1;a>=0;a--){var o=c[a];o.invalid&&Ze(c,o)}}();var h=function(t){var i=(t=t||{}).after;if(function(){if(!o){o=Ot();for(var t=0;t<e.length;t++)n=o,r=e[t].boundingBox(),n.x1=Math.min(n.x1,r.x1),n.x2=Math.max(n.x2,r.x2),n.w=n.x2-n.x1,n.y1=Math.min(n.y1,r.y1),n.y2=Math.max(n.y2,r.y2),n.h=n.y2-n.y1}var n,r}(),o.w*u*(o.h*u)>16e6)return null;var a=r.makeLayer(o,n);if(null!=i){var s=c.indexOf(i)+1;c.splice(s,0,a)}else(void 0===t.insert||t.insert)&&c.unshift(a);return a};if(r.skipping&&!a)return null;for(var d=null,p=e.length/1,g=!a,f=0;f<e.length;f++){var v=e[f],y=v._private.rscratch,m=y.imgLayerCaches=y.imgLayerCaches||{},b=m[n];if(b)d=b;else{if((!d||d.eles.length>=p||!Yt(d.bb,v.boundingBox()))&&!(d=h({insert:!0,after:d})))return null;s||g?r.queueLayer(d,v):r.drawEleInLayer(d,v,n,t),d.eles.push(v),m[n]=d}}return s||(g?null:c)},fu.getEleLevelForLayerLevel=function(e,t){return e},fu.drawEleInLayer=function(e,t,n,r){var i=this.renderer,a=e.context,o=t.boundingBox();0!==o.w&&0!==o.h&&t.visible()&&(n=this.getEleLevelForLayerLevel(n,r),i.setImgSmoothing(a,!1),i.drawCachedElement(a,t,null,null,n,true),i.setImgSmoothing(a,!0))},fu.levelIsComplete=function(e,t){var n=this.layersByLevel[e];if(!n||0===n.length)return!1;for(var r=0,i=0;i<n.length;i++){var a=n[i];if(a.reqs>0)return!1;if(a.invalid)return!1;r+=a.eles.length}return r===t.length},fu.validateLayersElesOrdering=function(e,t){var n=this.layersByLevel[e];if(n)for(var r=0;r<n.length;r++){for(var i=n[r],a=-1,o=0;o<t.length;o++)if(i.eles[0]===t[o]){a=o;break}if(a<0)this.invalidateLayer(i);else{var s=a;for(o=0;o<i.eles.length;o++)if(i.eles[o]!==t[s+o]){this.invalidateLayer(i);break}}}},fu.updateElementsInLayers=function(e,t){for(var n=E(e[0]),r=0;r<e.length;r++)for(var i=n?null:e[r],a=n?e[r]:e[r].ele,o=a._private.rscratch,s=o.imgLayerCaches=o.imgLayerCaches||{},l=-4;l<=2;l++){var u=s[l];u&&(i&&this.getEleLevelForLayerLevel(u.level)!==i.level||t(u,a,i))}},fu.haveLayers=function(){for(var e=!1,t=-4;t<=2;t++){var n=this.layersByLevel[t];if(n&&n.length>0){e=!0;break}}return e},fu.invalidateElements=function(e){var t=this;0!==e.length&&(t.lastInvalidationTime=Te(),0!==e.length&&t.haveLayers()&&t.updateElementsInLayers(e,(function(e,n,r){t.invalidateLayer(e)})))},fu.invalidateLayer=function(e){if(this.lastInvalidationTime=Te(),!e.invalid){var t=e.level,n=e.eles,r=this.layersByLevel[t];Ze(r,e),e.elesQueue=[],e.invalid=!0,e.replacement&&(e.replacement.invalid=!0);for(var i=0;i<n.length;i++){var a=n[i]._private.rscratch.imgLayerCaches;a&&(a[t]=null)}}},fu.refineElementTextures=function(e){var t=this;t.updateElementsInLayers(e,(function(e,n,r){var i=e.replacement;if(i||((i=e.replacement=t.makeLayer(e.bb,e.level)).replaces=e,i.eles=e.eles),!i.reqs)for(var a=0;a<i.eles.length;a++)t.queueLayer(i,i.eles[a])}))},fu.enqueueElementRefinement=function(e){this.eleTxrDeqs.merge(e),this.scheduleElementRefinement()},fu.queueLayer=function(e,t){var n=this.layersQueue,r=e.elesQueue,i=r.hasId=r.hasId||{};if(!e.replacement){if(t){if(i[t.id()])return;r.push(t),i[t.id()]=!0}e.reqs?(e.reqs++,n.updateItem(e)):(e.reqs=1,n.push(e))}},fu.dequeue=function(e){for(var t=this,n=t.layersQueue,r=[],i=0;i<1&&0!==n.size();){var a=n.peek();if(a.replacement)n.pop();else if(a.replaces&&a!==a.replaces.replacement)n.pop();else if(a.invalid)n.pop();else{var o=a.elesQueue.shift();o&&(t.drawEleInLayer(a,o,a.level,e),i++),0===r.length&&r.push(!0),0===a.elesQueue.length&&(n.pop(),a.reqs=0,a.replaces&&t.applyLayerReplacement(a),t.requestRedraw())}}return r},fu.applyLayerReplacement=function(e){var t=this.layersByLevel[e.level],n=e.replaces,r=t.indexOf(n);if(!(r<0||n.invalid)){t[r]=e;for(var i=0;i<e.eles.length;i++){var a=e.eles[i]._private,o=a.imgLayerCaches=a.imgLayerCaches||{};o&&(o[e.level]=e)}this.requestRedraw()}},fu.requestRedraw=be((function(){var e=this.renderer;e.redrawHint("eles",!0),e.redrawHint("drag",!0),e.redraw()}),100),fu.setupDequeueing=lu({deqRedrawThreshold:50,deqCost:.15,deqAvgCost:.1,deqNoDrawCost:.9,deqFastCost:.9,deq:function(e,t){return e.dequeue(t)},onDeqd:Xe,shouldRedraw:ze,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}});var mu,bu={};function xu(e,t){for(var n=0;n<t.length;n++){var r=t[n];e.lineTo(r.x,r.y)}}function wu(e,t,n){for(var r,i=0;i<t.length;i++){var a=t[i];0===i&&(r=a),e.lineTo(a.x,a.y)}e.quadraticCurveTo(n.x,n.y,r.x,r.y)}function Eu(e,t,n){e.beginPath&&e.beginPath();for(var r=t,i=0;i<r.length;i++){var a=r[i];e.lineTo(a.x,a.y)}var o=n,s=n[0];for(e.moveTo(s.x,s.y),i=1;i<o.length;i++)a=o[i],e.lineTo(a.x,a.y);e.closePath&&e.closePath()}function _u(e,t,n,r,i){e.beginPath&&e.beginPath(),e.arc(n,r,i,0,2*Math.PI,!1);var a=t,o=a[0];e.moveTo(o.x,o.y);for(var s=0;s<a.length;s++){var l=a[s];e.lineTo(l.x,l.y)}e.closePath&&e.closePath()}function Tu(e,t,n,r){e.arc(t,n,r,0,2*Math.PI,!1)}bu.arrowShapeImpl=function(e){return(mu||(mu={polygon:xu,"triangle-backcurve":wu,"triangle-tee":Eu,"circle-triangle":_u,"triangle-cross":Eu,circle:Tu}))[e]};var Du={drawElement:function(e,t,n,r,i,a){t.isNode()?this.drawNode(e,t,n,r,i,a):this.drawEdge(e,t,n,r,i,a)},drawElementOverlay:function(e,t){t.isNode()?this.drawNodeOverlay(e,t):this.drawEdgeOverlay(e,t)},drawElementUnderlay:function(e,t){t.isNode()?this.drawNodeUnderlay(e,t):this.drawEdgeUnderlay(e,t)},drawCachedElementPortion:function(e,t,n,r,i,a,o,s){var l=this,u=n.getBoundingBox(t);if(0!==u.w&&0!==u.h){var c=n.getElement(t,u,r,i,a);if(null!=c){var h=s(l,t);if(0===h)return;var d,p,g,f,v,y,m=o(l,t),b=u.x1,x=u.y1,w=u.w,E=u.h;if(0!==m){var _=n.getRotationPoint(t);g=_.x,f=_.y,e.translate(g,f),e.rotate(m),(v=l.getImgSmoothing(e))||l.setImgSmoothing(e,!0);var T=n.getRotationOffset(t);d=T.x,p=T.y}else d=b,p=x;1!==h&&(y=e.globalAlpha,e.globalAlpha=y*h),e.drawImage(c.texture.canvas,c.x,0,c.width,c.height,d,p,w,E),1!==h&&(e.globalAlpha=y),0!==m&&(e.rotate(-m),e.translate(-g,-f),v||l.setImgSmoothing(e,!1))}else n.drawElement(e,t)}}},Cu=function(){return 0},Nu=function(e,t){return e.getTextAngle(t,null)},Au=function(e,t){return e.getTextAngle(t,"source")},Lu=function(e,t){return e.getTextAngle(t,"target")},ku=function(e,t){return t.effectiveOpacity()},Su=function(e,t){return t.pstyle("text-opacity").pfValue*t.effectiveOpacity()};Du.drawCachedElement=function(e,t,n,r,i,a){var o=this,s=o.data,l=s.eleTxrCache,u=s.lblTxrCache,c=s.slbTxrCache,h=s.tlbTxrCache,d=t.boundingBox(),p=!0===a?l.reasons.highQuality:null;if(0!==d.w&&0!==d.h&&t.visible()&&(!r||zt(d,r))){var g=t.isEdge(),f=t.element()._private.rscratch.badLine;o.drawElementUnderlay(e,t),o.drawCachedElementPortion(e,t,l,n,i,p,Cu,ku),g&&f||o.drawCachedElementPortion(e,t,u,n,i,p,Nu,Su),g&&!f&&(o.drawCachedElementPortion(e,t,c,n,i,p,Au,Su),o.drawCachedElementPortion(e,t,h,n,i,p,Lu,Su)),o.drawElementOverlay(e,t)}},Du.drawElements=function(e,t){for(var n=0;n<t.length;n++){var r=t[n];this.drawElement(e,r)}},Du.drawCachedElements=function(e,t,n,r){for(var i=0;i<t.length;i++){var a=t[i];this.drawCachedElement(e,a,n,r)}},Du.drawCachedNodes=function(e,t,n,r){for(var i=0;i<t.length;i++){var a=t[i];a.isNode()&&this.drawCachedElement(e,a,n,r)}},Du.drawLayeredElements=function(e,t,n,r){var i=this.data.lyrTxrCache.getLayers(t,n);if(i)for(var a=0;a<i.length;a++){var o=i[a],s=o.bb;0!==s.w&&0!==s.h&&e.drawImage(o.canvas,s.x1,s.y1,s.w,s.h)}else this.drawCachedElements(e,t,n,r)};var Iu={drawEdge:function(e,t,n){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],i=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],a=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],o=this,s=t._private.rscratch;if((!a||t.visible())&&!s.badLine&&null!=s.allpts&&!isNaN(s.allpts[0])){var l;n&&(l=n,e.translate(-l.x1,-l.y1));var u=a?t.pstyle("opacity").value:1,c=a?t.pstyle("line-opacity").value:1,h=t.pstyle("curve-style").value,d=t.pstyle("line-style").value,p=t.pstyle("width").pfValue,g=t.pstyle("line-cap").value,f=u*c,v=u*c,y=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f;"straight-triangle"===h?(o.eleStrokeStyle(e,t,n),o.drawEdgeTrianglePath(t,e,s.allpts)):(e.lineWidth=p,e.lineCap=g,o.eleStrokeStyle(e,t,n),o.drawEdgePath(t,e,s.allpts,d),e.lineCap="butt")},m=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:v;o.drawArrowheads(e,t,n)};if(e.lineJoin="round","yes"===t.pstyle("ghost").value){var b=t.pstyle("ghost-offset-x").pfValue,x=t.pstyle("ghost-offset-y").pfValue,w=t.pstyle("ghost-opacity").value,E=f*w;e.translate(b,x),y(E),m(E),e.translate(-b,-x)}i&&o.drawEdgeUnderlay(e,t),y(),m(),i&&o.drawEdgeOverlay(e,t),o.drawElementText(e,t,null,r),n&&e.translate(l.x1,l.y1)}}},Mu=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,n){if(n.visible()){var r=n.pstyle("".concat(e,"-opacity")).value;if(0!==r){var i=this,a=i.usePaths(),o=n._private.rscratch,s=2*n.pstyle("".concat(e,"-padding")).pfValue,l=n.pstyle("".concat(e,"-color")).value;t.lineWidth=s,"self"!==o.edgeType||a?t.lineCap="round":t.lineCap="butt",i.colorStrokeStyle(t,l[0],l[1],l[2],r),i.drawEdgePath(n,t,o.allpts,"solid")}}}};Iu.drawEdgeOverlay=Mu("overlay"),Iu.drawEdgeUnderlay=Mu("underlay"),Iu.drawEdgePath=function(e,t,n,r){var i,a=e._private.rscratch,o=t,s=!1,l=this.usePaths(),u=e.pstyle("line-dash-pattern").pfValue,c=e.pstyle("line-dash-offset").pfValue;if(l){var h=n.join("$");a.pathCacheKey&&a.pathCacheKey===h?(i=t=a.pathCache,s=!0):(i=t=new Path2D,a.pathCacheKey=h,a.pathCache=i)}if(o.setLineDash)switch(r){case"dotted":o.setLineDash([1,1]);break;case"dashed":o.setLineDash(u),o.lineDashOffset=c;break;case"solid":o.setLineDash([])}if(!s&&!a.badLine)switch(t.beginPath&&t.beginPath(),t.moveTo(n[0],n[1]),a.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var d=2;d+3<n.length;d+=4)t.quadraticCurveTo(n[d],n[d+1],n[d+2],n[d+3]);break;case"straight":case"segments":case"haystack":for(var p=2;p+1<n.length;p+=2)t.lineTo(n[p],n[p+1])}t=o,l?t.stroke(i):t.stroke(),t.setLineDash&&t.setLineDash([])},Iu.drawEdgeTrianglePath=function(e,t,n){t.fillStyle=t.strokeStyle;for(var r=e.pstyle("width").pfValue,i=0;i+1<n.length;i+=2){var a=[n[i+2]-n[i],n[i+3]-n[i+1]],o=Math.sqrt(a[0]*a[0]+a[1]*a[1]),s=[a[1]/o,-a[0]/o],l=[s[0]*r/2,s[1]*r/2];t.beginPath(),t.moveTo(n[i]-l[0],n[i+1]-l[1]),t.lineTo(n[i]+l[0],n[i+1]+l[1]),t.lineTo(n[i+2],n[i+3]),t.closePath(),t.fill()}},Iu.drawArrowheads=function(e,t,n){var r=t._private.rscratch,i="haystack"===r.edgeType;i||this.drawArrowhead(e,t,"source",r.arrowStartX,r.arrowStartY,r.srcArrowAngle,n),this.drawArrowhead(e,t,"mid-target",r.midX,r.midY,r.midtgtArrowAngle,n),this.drawArrowhead(e,t,"mid-source",r.midX,r.midY,r.midsrcArrowAngle,n),i||this.drawArrowhead(e,t,"target",r.arrowEndX,r.arrowEndY,r.tgtArrowAngle,n)},Iu.drawArrowhead=function(e,t,n,r,i,a,o){if(!(isNaN(r)||null==r||isNaN(i)||null==i||isNaN(a)||null==a)){var s=this,l=t.pstyle(n+"-arrow-shape").value;if("none"!==l){var u="hollow"===t.pstyle(n+"-arrow-fill").value?"both":"filled",c=t.pstyle(n+"-arrow-fill").value,h=t.pstyle("width").pfValue,d=t.pstyle("opacity").value;void 0===o&&(o=d);var p=e.globalCompositeOperation;1===o&&"hollow"!==c||(e.globalCompositeOperation="destination-out",s.colorFillStyle(e,255,255,255,1),s.colorStrokeStyle(e,255,255,255,1),s.drawArrowShape(t,e,u,h,l,r,i,a),e.globalCompositeOperation=p);var g=t.pstyle(n+"-arrow-color").value;s.colorFillStyle(e,g[0],g[1],g[2],o),s.colorStrokeStyle(e,g[0],g[1],g[2],o),s.drawArrowShape(t,e,c,h,l,r,i,a)}}},Iu.drawArrowShape=function(e,t,n,r,i,a,o,s){var l,u=this,c=this.usePaths()&&"triangle-cross"!==i,h=!1,d=t,p={x:a,y:o},g=e.pstyle("arrow-scale").value,f=this.getArrowWidth(r,g),v=u.arrowShapes[i];if(c){var y=u.arrowPathCache=u.arrowPathCache||[],m=Ie(i),b=y[m];null!=b?(l=t=b,h=!0):(l=t=new Path2D,y[m]=l)}h||(t.beginPath&&t.beginPath(),c?v.draw(t,1,0,{x:0,y:0},1):v.draw(t,f,s,p,r),t.closePath&&t.closePath()),t=d,c&&(t.translate(a,o),t.rotate(s),t.scale(f,f)),"filled"!==n&&"both"!==n||(c?t.fill(l):t.fill()),"hollow"!==n&&"both"!==n||(t.lineWidth=(v.matchEdgeWidth?r:1)/(c?f:1),t.lineJoin="miter",c?t.stroke(l):t.stroke()),c&&(t.scale(1/f,1/f),t.rotate(-s),t.translate(-a,-o))};var Ou={safeDrawImage:function(e,t,n,r,i,a,o,s,l,u){if(!(i<=0||a<=0||l<=0||u<=0))try{e.drawImage(t,n,r,i,a,o,s,l,u)}catch(e){je(e)}},drawInscribedImage:function(e,t,n,r,i){var a=this,o=n.position(),s=o.x,l=o.y,u=n.cy().style(),c=u.getIndexedStyle.bind(u),h=c(n,"background-fit","value",r),d=c(n,"background-repeat","value",r),p=n.width(),g=n.height(),f=2*n.padding(),v=p+("inner"===c(n,"background-width-relative-to","value",r)?0:f),y=g+("inner"===c(n,"background-height-relative-to","value",r)?0:f),m=n._private.rscratch,b="node"===c(n,"background-clip","value",r),x=c(n,"background-image-opacity","value",r)*i,w=c(n,"background-image-smoothing","value",r),E=t.width||t.cachedW,_=t.height||t.cachedH;null!=E&&null!=_||(document.body.appendChild(t),E=t.cachedW=t.width||t.offsetWidth,_=t.cachedH=t.height||t.offsetHeight,document.body.removeChild(t));var T=E,D=_;if("auto"!==c(n,"background-width","value",r)&&(T="%"===c(n,"background-width","units",r)?c(n,"background-width","pfValue",r)*v:c(n,"background-width","pfValue",r)),"auto"!==c(n,"background-height","value",r)&&(D="%"===c(n,"background-height","units",r)?c(n,"background-height","pfValue",r)*y:c(n,"background-height","pfValue",r)),0!==T&&0!==D){if("contain"===h)T*=C=Math.min(v/T,y/D),D*=C;else if("cover"===h){var C;T*=C=Math.max(v/T,y/D),D*=C}var N=s-v/2,A=c(n,"background-position-x","units",r),L=c(n,"background-position-x","pfValue",r);N+="%"===A?(v-T)*L:L;var k=c(n,"background-offset-x","units",r),S=c(n,"background-offset-x","pfValue",r);N+="%"===k?(v-T)*S:S;var I=l-y/2,M=c(n,"background-position-y","units",r),O=c(n,"background-position-y","pfValue",r);I+="%"===M?(y-D)*O:O;var P=c(n,"background-offset-y","units",r),R=c(n,"background-offset-y","pfValue",r);I+="%"===P?(y-D)*R:R,m.pathCache&&(N-=s,I-=l,s=0,l=0);var B=e.globalAlpha;e.globalAlpha=x;var F=a.getImgSmoothing(e),z=!1;if("no"===w&&F?(a.setImgSmoothing(e,!1),z=!0):"yes"!==w||F||(a.setImgSmoothing(e,!0),z=!0),"no-repeat"===d)b&&(e.save(),m.pathCache?e.clip(m.pathCache):(a.nodeShapes[a.getNodeShape(n)].draw(e,s,l,v,y),e.clip())),a.safeDrawImage(e,t,0,0,E,_,N,I,T,D),b&&e.restore();else{var G=e.createPattern(t,d);e.fillStyle=G,a.nodeShapes[a.getNodeShape(n)].draw(e,s,l,v,y),e.translate(N,I),e.fill(),e.translate(-N,-I)}e.globalAlpha=B,z&&a.setImgSmoothing(e,F)}}},Pu={};Pu.eleTextBiggerThanMin=function(e,t){if(!t){var n=e.cy().zoom(),r=this.getPixelRatio(),i=Math.ceil(Ct(n*r));t=Math.pow(2,i)}return!(e.pstyle("font-size").pfValue*t<e.pstyle("min-zoomed-font-size").pfValue)},Pu.drawElementText=function(e,t,n,r,i){var a=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],o=this;if(null==r){if(a&&!o.eleTextBiggerThanMin(t))return}else if(!1===r)return;if(t.isNode()){var s=t.pstyle("label");if(!s||!s.value)return;var l=o.getLabelJustification(t);e.textAlign=l,e.textBaseline="bottom"}else{var u=t.element()._private.rscratch.badLine,c=t.pstyle("label"),h=t.pstyle("source-label"),d=t.pstyle("target-label");if(u||(!c||!c.value)&&(!h||!h.value)&&(!d||!d.value))return;e.textAlign="center",e.textBaseline="bottom"}var p,g=!n;n&&(p=n,e.translate(-p.x1,-p.y1)),null==i?(o.drawText(e,t,null,g,a),t.isEdge()&&(o.drawText(e,t,"source",g,a),o.drawText(e,t,"target",g,a))):o.drawText(e,t,i,g,a),n&&e.translate(p.x1,p.y1)},Pu.getFontCache=function(e){var t;this.fontCaches=this.fontCaches||[];for(var n=0;n<this.fontCaches.length;n++)if((t=this.fontCaches[n]).context===e)return t;return t={context:e},this.fontCaches.push(t),t},Pu.setupTextStyle=function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=t.pstyle("font-style").strValue,i=t.pstyle("font-size").pfValue+"px",a=t.pstyle("font-family").strValue,o=t.pstyle("font-weight").strValue,s=n?t.effectiveOpacity()*t.pstyle("text-opacity").value:1,l=t.pstyle("text-outline-opacity").value*s,u=t.pstyle("color").value,c=t.pstyle("text-outline-color").value;e.font=r+" "+o+" "+i+" "+a,e.lineJoin="round",this.colorFillStyle(e,u[0],u[1],u[2],s),this.colorStrokeStyle(e,c[0],c[1],c[2],l)},Pu.getTextAngle=function(e,t){var n=e._private.rscratch,r=t?t+"-":"",i=e.pstyle(r+"text-rotation"),a=Je(n,"labelAngle",t);return"autorotate"===i.strValue?e.isEdge()?a:0:"none"===i.strValue?0:i.pfValue},Pu.drawText=function(e,t,n){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],i=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],a=t._private.rscratch,o=i?t.effectiveOpacity():1;if(!i||0!==o&&0!==t.pstyle("text-opacity").value){"main"===n&&(n=null);var s,l,u=Je(a,"labelX",n),c=Je(a,"labelY",n),h=this.getLabelText(t,n);if(null!=h&&""!==h&&!isNaN(u)&&!isNaN(c)){this.setupTextStyle(e,t,i);var d,p=n?n+"-":"",g=Je(a,"labelWidth",n),f=Je(a,"labelHeight",n),v=t.pstyle(p+"text-margin-x").pfValue,y=t.pstyle(p+"text-margin-y").pfValue,m=t.isEdge(),b=t.pstyle("text-halign").value,x=t.pstyle("text-valign").value;switch(m&&(b="center",x="center"),u+=v,c+=y,0!==(d=r?this.getTextAngle(t,n):0)&&(s=u,l=c,e.translate(s,l),e.rotate(d),u=0,c=0),x){case"top":break;case"center":c+=f/2;break;case"bottom":c+=f}var w=t.pstyle("text-background-opacity").value,E=t.pstyle("text-border-opacity").value,_=t.pstyle("text-border-width").pfValue,T=t.pstyle("text-background-padding").pfValue;if(w>0||_>0&&E>0){var D=u-T;switch(b){case"left":D-=g;break;case"center":D-=g/2}var C=c-f-T,N=g+2*T,A=f+2*T;if(w>0){var L=e.fillStyle,k=t.pstyle("text-background-color").value;e.fillStyle="rgba("+k[0]+","+k[1]+","+k[2]+","+w*o+")",0===t.pstyle("text-background-shape").strValue.indexOf("round")?function(e,t,n,r,i){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:5;e.beginPath(),e.moveTo(t+a,n),e.lineTo(t+r-a,n),e.quadraticCurveTo(t+r,n,t+r,n+a),e.lineTo(t+r,n+i-a),e.quadraticCurveTo(t+r,n+i,t+r-a,n+i),e.lineTo(t+a,n+i),e.quadraticCurveTo(t,n+i,t,n+i-a),e.lineTo(t,n+a),e.quadraticCurveTo(t,n,t+a,n),e.closePath(),e.fill()}(e,D,C,N,A,2):e.fillRect(D,C,N,A),e.fillStyle=L}if(_>0&&E>0){var S=e.strokeStyle,I=e.lineWidth,M=t.pstyle("text-border-color").value,O=t.pstyle("text-border-style").value;if(e.strokeStyle="rgba("+M[0]+","+M[1]+","+M[2]+","+E*o+")",e.lineWidth=_,e.setLineDash)switch(O){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"double":e.lineWidth=_/4,e.setLineDash([]);break;case"solid":e.setLineDash([])}if(e.strokeRect(D,C,N,A),"double"===O){var P=_/2;e.strokeRect(D+P,C+P,N-2*P,A-2*P)}e.setLineDash&&e.setLineDash([]),e.lineWidth=I,e.strokeStyle=S}}var R=2*t.pstyle("text-outline-width").pfValue;if(R>0&&(e.lineWidth=R),"wrap"===t.pstyle("text-wrap").value){var B=Je(a,"labelWrapCachedLines",n),F=Je(a,"labelLineHeight",n),z=g/2,G=this.getLabelJustification(t);switch("auto"===G||("left"===b?"left"===G?u+=-g:"center"===G&&(u+=-z):"center"===b?"left"===G?u+=-z:"right"===G&&(u+=z):"right"===b&&("center"===G?u+=z:"right"===G&&(u+=g))),x){case"top":case"center":case"bottom":c-=(B.length-1)*F}for(var Y=0;Y<B.length;Y++)R>0&&e.strokeText(B[Y],u,c),e.fillText(B[Y],u,c),c+=F}else R>0&&e.strokeText(h,u,c),e.fillText(h,u,c);0!==d&&(e.rotate(-d),e.translate(-s,-l))}}};var Ru={drawNode:function(e,t,n){var r,i,a=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],s=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],l=this,u=t._private,c=u.rscratch,h=t.position();if(b(h.x)&&b(h.y)&&(!s||t.visible())){var d,p,g=s?t.effectiveOpacity():1,f=l.usePaths(),v=!1,y=t.padding();r=t.width()+2*y,i=t.height()+2*y,n&&(p=n,e.translate(-p.x1,-p.y1));for(var m=t.pstyle("background-image").value,x=new Array(m.length),w=new Array(m.length),E=0,_=0;_<m.length;_++){var T=m[_];if(x[_]=null!=T&&"none"!==T){var D=t.cy().style().getIndexedStyle(t,"background-image-crossorigin","value",_);E++,w[_]=l.getCachedImage(T,D,(function(){u.backgroundTimestamp=Date.now(),t.emitAndNotify("background")}))}}var C=t.pstyle("background-blacken").value,N=t.pstyle("border-width").pfValue,A=t.pstyle("background-opacity").value*g,L=t.pstyle("border-color").value,k=t.pstyle("border-style").value,S=t.pstyle("border-opacity").value*g;e.lineJoin="miter";var I=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:A;l.eleFillStyle(e,t,n)},M=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:S;l.colorStrokeStyle(e,L[0],L[1],L[2],t)},O=t.pstyle("shape").strValue,P=t.pstyle("shape-polygon-points").pfValue;if(f){e.translate(h.x,h.y);var R=l.nodePathCache=l.nodePathCache||[],B=Me("polygon"===O?O+","+P.join(","):O,""+i,""+r),F=R[B];null!=F?(d=F,v=!0,c.pathCache=d):(d=new Path2D,R[B]=c.pathCache=d)}var z=function(){if(!v){var n=h;f&&(n={x:0,y:0}),l.nodeShapes[l.getNodeShape(t)].draw(d||e,n.x,n.y,r,i)}f?e.fill(d):e.fill()},G=function(){for(var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g,r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=u.backgrounding,a=0,o=0;o<w.length;o++){var s=t.cy().style().getIndexedStyle(t,"background-image-containment","value",o);r&&"over"===s||!r&&"inside"===s?a++:x[o]&&w[o].complete&&!w[o].error&&(a++,l.drawInscribedImage(e,w[o],t,o,n))}u.backgrounding=!(a===E),i!==u.backgrounding&&t.updateStyle(!1)},Y=function(){var n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:g;l.hasPie(t)&&(l.drawPie(e,t,a),n&&(f||l.nodeShapes[l.getNodeShape(t)].draw(e,h.x,h.y,r,i)))},X=function(){var t=(C>0?C:-C)*(arguments.length>0&&void 0!==arguments[0]?arguments[0]:g),n=C>0?0:255;0!==C&&(l.colorFillStyle(e,n,n,n,t),f?e.fill(d):e.fill())},V=function(){if(N>0){if(e.lineWidth=N,e.lineCap="butt",e.setLineDash)switch(k){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"solid":case"double":e.setLineDash([])}if(f?e.stroke(d):e.stroke(),"double"===k){e.lineWidth=N/3;var t=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",f?e.stroke(d):e.stroke(),e.globalCompositeOperation=t}e.setLineDash&&e.setLineDash([])}};if("yes"===t.pstyle("ghost").value){var U=t.pstyle("ghost-offset-x").pfValue,j=t.pstyle("ghost-offset-y").pfValue,q=t.pstyle("ghost-opacity").value,H=q*g;e.translate(U,j),I(q*A),z(),G(H,!0),M(q*S),V(),Y(0!==C||0!==N),G(H,!1),X(H),e.translate(-U,-j)}f&&e.translate(-h.x,-h.y),o&&l.drawNodeUnderlay(e,t,h,r,i),f&&e.translate(h.x,h.y),I(),z(),G(g,!0),M(),V(),Y(0!==C||0!==N),G(g,!1),X(),f&&e.translate(-h.x,-h.y),l.drawElementText(e,t,null,a),o&&l.drawNodeOverlay(e,t,h,r,i),n&&e.translate(p.x1,p.y1)}}},Bu=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,n,r,i,a){if(n.visible()){var o=n.pstyle("".concat(e,"-padding")).pfValue,s=n.pstyle("".concat(e,"-opacity")).value,l=n.pstyle("".concat(e,"-color")).value,u=n.pstyle("".concat(e,"-shape")).value;if(s>0){if(r=r||n.position(),null==i||null==a){var c=n.padding();i=n.width()+2*c,a=n.height()+2*c}this.colorFillStyle(t,l[0],l[1],l[2],s),this.nodeShapes[u].draw(t,r.x,r.y,i+2*o,a+2*o),t.fill()}}}};Ru.drawNodeOverlay=Bu("overlay"),Ru.drawNodeUnderlay=Bu("underlay"),Ru.hasPie=function(e){return(e=e[0])._private.hasPie},Ru.drawPie=function(e,t,n,r){t=t[0],r=r||t.position();var i=t.cy().style(),a=t.pstyle("pie-size"),o=r.x,s=r.y,l=t.width(),u=t.height(),c=Math.min(l,u)/2,h=0;this.usePaths()&&(o=0,s=0),"%"===a.units?c*=a.pfValue:void 0!==a.pfValue&&(c=a.pfValue/2);for(var d=1;d<=i.pieBackgroundN;d++){var p=t.pstyle("pie-"+d+"-background-size").value,g=t.pstyle("pie-"+d+"-background-color").value,f=t.pstyle("pie-"+d+"-background-opacity").value*n,v=p/100;v+h>1&&(v=1-h);var y=1.5*Math.PI+2*Math.PI*h,m=y+2*Math.PI*v;0===p||h>=1||h+v>1||(e.beginPath(),e.moveTo(o,s),e.arc(o,s,c,y,m),e.closePath(),this.colorFillStyle(e,g[0],g[1],g[2],f),e.fill(),h+=v)}};var Fu={};Fu.getPixelRatio=function(){var e=this.data.contexts[0];if(null!=this.forcedPixelRatio)return this.forcedPixelRatio;var t=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/t},Fu.paintCache=function(e){for(var t,n=this.paintCaches=this.paintCaches||[],r=!0,i=0;i<n.length;i++)if((t=n[i]).context===e){r=!1;break}return r&&(t={context:e},n.push(t)),t},Fu.createGradientStyleFor=function(e,t,n,r,i){var a,o=this.usePaths(),s=n.pstyle(t+"-gradient-stop-colors").value,l=n.pstyle(t+"-gradient-stop-positions").pfValue;if("radial-gradient"===r)if(n.isEdge()){var u=n.sourceEndpoint(),c=n.targetEndpoint(),h=n.midpoint(),d=At(u,h),p=At(c,h);a=e.createRadialGradient(h.x,h.y,0,h.x,h.y,Math.max(d,p))}else{var g=o?{x:0,y:0}:n.position(),f=n.paddedWidth(),v=n.paddedHeight();a=e.createRadialGradient(g.x,g.y,0,g.x,g.y,Math.max(f,v))}else if(n.isEdge()){var y=n.sourceEndpoint(),m=n.targetEndpoint();a=e.createLinearGradient(y.x,y.y,m.x,m.y)}else{var b=o?{x:0,y:0}:n.position(),x=n.paddedWidth()/2,w=n.paddedHeight()/2;switch(n.pstyle("background-gradient-direction").value){case"to-bottom":a=e.createLinearGradient(b.x,b.y-w,b.x,b.y+w);break;case"to-top":a=e.createLinearGradient(b.x,b.y+w,b.x,b.y-w);break;case"to-left":a=e.createLinearGradient(b.x+x,b.y,b.x-x,b.y);break;case"to-right":a=e.createLinearGradient(b.x-x,b.y,b.x+x,b.y);break;case"to-bottom-right":case"to-right-bottom":a=e.createLinearGradient(b.x-x,b.y-w,b.x+x,b.y+w);break;case"to-top-right":case"to-right-top":a=e.createLinearGradient(b.x-x,b.y+w,b.x+x,b.y-w);break;case"to-bottom-left":case"to-left-bottom":a=e.createLinearGradient(b.x+x,b.y-w,b.x-x,b.y+w);break;case"to-top-left":case"to-left-top":a=e.createLinearGradient(b.x+x,b.y+w,b.x-x,b.y-w)}}if(!a)return null;for(var E=l.length===s.length,_=s.length,T=0;T<_;T++)a.addColorStop(E?l[T]:T/(_-1),"rgba("+s[T][0]+","+s[T][1]+","+s[T][2]+","+i+")");return a},Fu.gradientFillStyle=function(e,t,n,r){var i=this.createGradientStyleFor(e,"background",t,n,r);if(!i)return null;e.fillStyle=i},Fu.colorFillStyle=function(e,t,n,r,i){e.fillStyle="rgba("+t+","+n+","+r+","+i+")"},Fu.eleFillStyle=function(e,t,n){var r=t.pstyle("background-fill").value;if("linear-gradient"===r||"radial-gradient"===r)this.gradientFillStyle(e,t,r,n);else{var i=t.pstyle("background-color").value;this.colorFillStyle(e,i[0],i[1],i[2],n)}},Fu.gradientStrokeStyle=function(e,t,n,r){var i=this.createGradientStyleFor(e,"line",t,n,r);if(!i)return null;e.strokeStyle=i},Fu.colorStrokeStyle=function(e,t,n,r,i){e.strokeStyle="rgba("+t+","+n+","+r+","+i+")"},Fu.eleStrokeStyle=function(e,t,n){var r=t.pstyle("line-fill").value;if("linear-gradient"===r||"radial-gradient"===r)this.gradientStrokeStyle(e,t,r,n);else{var i=t.pstyle("line-color").value;this.colorStrokeStyle(e,i[0],i[1],i[2],n)}},Fu.matchCanvasSize=function(e){var t=this,n=t.data,r=t.findContainerClientCoords(),i=r[2],a=r[3],o=t.getPixelRatio(),s=t.motionBlurPxRatio;e!==t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_NODE]&&e!==t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_DRAG]||(o=s);var l,u=i*o,c=a*o;if(u!==t.canvasWidth||c!==t.canvasHeight){t.fontCaches=null;var h=n.canvasContainer;h.style.width=i+"px",h.style.height=a+"px";for(var d=0;d<t.CANVAS_LAYERS;d++)(l=n.canvases[d]).width=u,l.height=c,l.style.width=i+"px",l.style.height=a+"px";for(d=0;d<t.BUFFER_COUNT;d++)(l=n.bufferCanvases[d]).width=u,l.height=c,l.style.width=i+"px",l.style.height=a+"px";t.textureMult=1,o<=1&&(l=n.bufferCanvases[t.TEXTURE_BUFFER],t.textureMult=2,l.width=u*t.textureMult,l.height=c*t.textureMult),t.canvasWidth=u,t.canvasHeight=c}},Fu.renderTo=function(e,t,n,r){this.render({forcedContext:e,forcedZoom:t,forcedPan:n,drawAllLayers:!0,forcedPxRatio:r})},Fu.render=function(e){var t=(e=e||$e()).forcedContext,n=e.drawAllLayers,r=e.drawOnlyNodeLayer,i=e.forcedZoom,a=e.forcedPan,o=this,s=void 0===e.forcedPxRatio?this.getPixelRatio():e.forcedPxRatio,l=o.cy,u=o.data,c=u.canvasNeedsRedraw,h=o.textureOnViewport&&!t&&(o.pinching||o.hoverData.dragging||o.swipePanning||o.data.wheelZooming),d=void 0!==e.motionBlur?e.motionBlur:o.motionBlur,p=o.motionBlurPxRatio,g=l.hasCompoundNodes(),f=o.hoverData.draggingEles,v=!(!o.hoverData.selecting&&!o.touchData.selecting),y=d=d&&!t&&o.motionBlurEnabled&&!v;t||(o.prevPxRatio!==s&&(o.invalidateContainerClientCoordsCache(),o.matchCanvasSize(o.container),o.redrawHint("eles",!0),o.redrawHint("drag",!0)),o.prevPxRatio=s),!t&&o.motionBlurTimeout&&clearTimeout(o.motionBlurTimeout),d&&(null==o.mbFrames&&(o.mbFrames=0),o.mbFrames++,o.mbFrames<3&&(y=!1),o.mbFrames>o.minMbLowQualFrames&&(o.motionBlurPxRatio=o.mbPxRBlurry)),o.clearingMotionBlur&&(o.motionBlurPxRatio=1),o.textureDrawLastFrame&&!h&&(c[o.NODE]=!0,c[o.SELECT_BOX]=!0);var m=l.style(),b=l.zoom(),x=void 0!==i?i:b,w=l.pan(),E={x:w.x,y:w.y},_={zoom:b,pan:{x:w.x,y:w.y}},T=o.prevViewport;void 0===T||_.zoom!==T.zoom||_.pan.x!==T.pan.x||_.pan.y!==T.pan.y||f&&!g||(o.motionBlurPxRatio=1),a&&(E=a),x*=s,E.x*=s,E.y*=s;var D=o.getCachedZSortedEles();function C(e,t,n,r,i){var a=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",o.colorFillStyle(e,255,255,255,o.motionBlurTransparency),e.fillRect(t,n,r,i),e.globalCompositeOperation=a}function N(e,r){var s,l,c,h;o.clearingMotionBlur||e!==u.bufferContexts[o.MOTIONBLUR_BUFFER_NODE]&&e!==u.bufferContexts[o.MOTIONBLUR_BUFFER_DRAG]?(s=E,l=x,c=o.canvasWidth,h=o.canvasHeight):(s={x:w.x*p,y:w.y*p},l=b*p,c=o.canvasWidth*p,h=o.canvasHeight*p),e.setTransform(1,0,0,1,0,0),"motionBlur"===r?C(e,0,0,c,h):t||void 0!==r&&!r||e.clearRect(0,0,c,h),n||(e.translate(s.x,s.y),e.scale(l,l)),a&&e.translate(a.x,a.y),i&&e.scale(i,i)}if(h||(o.textureDrawLastFrame=!1),h){if(o.textureDrawLastFrame=!0,!o.textureCache){o.textureCache={},o.textureCache.bb=l.mutableElements().boundingBox(),o.textureCache.texture=o.data.bufferCanvases[o.TEXTURE_BUFFER];var A=o.data.bufferContexts[o.TEXTURE_BUFFER];A.setTransform(1,0,0,1,0,0),A.clearRect(0,0,o.canvasWidth*o.textureMult,o.canvasHeight*o.textureMult),o.render({forcedContext:A,drawOnlyNodeLayer:!0,forcedPxRatio:s*o.textureMult}),(_=o.textureCache.viewport={zoom:l.zoom(),pan:l.pan(),width:o.canvasWidth,height:o.canvasHeight}).mpan={x:(0-_.pan.x)/_.zoom,y:(0-_.pan.y)/_.zoom}}c[o.DRAG]=!1,c[o.NODE]=!1;var L=u.contexts[o.NODE],k=o.textureCache.texture;_=o.textureCache.viewport,L.setTransform(1,0,0,1,0,0),d?C(L,0,0,_.width,_.height):L.clearRect(0,0,_.width,_.height);var S=m.core("outside-texture-bg-color").value,I=m.core("outside-texture-bg-opacity").value;o.colorFillStyle(L,S[0],S[1],S[2],I),L.fillRect(0,0,_.width,_.height),b=l.zoom(),N(L,!1),L.clearRect(_.mpan.x,_.mpan.y,_.width/_.zoom/s,_.height/_.zoom/s),L.drawImage(k,_.mpan.x,_.mpan.y,_.width/_.zoom/s,_.height/_.zoom/s)}else o.textureOnViewport&&!t&&(o.textureCache=null);var M=l.extent(),O=o.pinching||o.hoverData.dragging||o.swipePanning||o.data.wheelZooming||o.hoverData.draggingEles||o.cy.animated(),P=o.hideEdgesOnViewport&&O,R=[];if(R[o.NODE]=!c[o.NODE]&&d&&!o.clearedForMotionBlur[o.NODE]||o.clearingMotionBlur,R[o.NODE]&&(o.clearedForMotionBlur[o.NODE]=!0),R[o.DRAG]=!c[o.DRAG]&&d&&!o.clearedForMotionBlur[o.DRAG]||o.clearingMotionBlur,R[o.DRAG]&&(o.clearedForMotionBlur[o.DRAG]=!0),c[o.NODE]||n||r||R[o.NODE]){var B=d&&!R[o.NODE]&&1!==p;N(L=t||(B?o.data.bufferContexts[o.MOTIONBLUR_BUFFER_NODE]:u.contexts[o.NODE]),d&&!B?"motionBlur":void 0),P?o.drawCachedNodes(L,D.nondrag,s,M):o.drawLayeredElements(L,D.nondrag,s,M),o.debug&&o.drawDebugPoints(L,D.nondrag),n||d||(c[o.NODE]=!1)}if(!r&&(c[o.DRAG]||n||R[o.DRAG])&&(B=d&&!R[o.DRAG]&&1!==p,N(L=t||(B?o.data.bufferContexts[o.MOTIONBLUR_BUFFER_DRAG]:u.contexts[o.DRAG]),d&&!B?"motionBlur":void 0),P?o.drawCachedNodes(L,D.drag,s,M):o.drawCachedElements(L,D.drag,s,M),o.debug&&o.drawDebugPoints(L,D.drag),n||d||(c[o.DRAG]=!1)),o.showFps||!r&&c[o.SELECT_BOX]&&!n){if(N(L=t||u.contexts[o.SELECT_BOX]),1==o.selection[4]&&(o.hoverData.selecting||o.touchData.selecting)){b=o.cy.zoom();var F=m.core("selection-box-border-width").value/b;L.lineWidth=F,L.fillStyle="rgba("+m.core("selection-box-color").value[0]+","+m.core("selection-box-color").value[1]+","+m.core("selection-box-color").value[2]+","+m.core("selection-box-opacity").value+")",L.fillRect(o.selection[0],o.selection[1],o.selection[2]-o.selection[0],o.selection[3]-o.selection[1]),F>0&&(L.strokeStyle="rgba("+m.core("selection-box-border-color").value[0]+","+m.core("selection-box-border-color").value[1]+","+m.core("selection-box-border-color").value[2]+","+m.core("selection-box-opacity").value+")",L.strokeRect(o.selection[0],o.selection[1],o.selection[2]-o.selection[0],o.selection[3]-o.selection[1]))}if(u.bgActivePosistion&&!o.hoverData.selecting){b=o.cy.zoom();var z=u.bgActivePosistion;L.fillStyle="rgba("+m.core("active-bg-color").value[0]+","+m.core("active-bg-color").value[1]+","+m.core("active-bg-color").value[2]+","+m.core("active-bg-opacity").value+")",L.beginPath(),L.arc(z.x,z.y,m.core("active-bg-size").pfValue/b,0,2*Math.PI),L.fill()}var G=o.lastRedrawTime;if(o.showFps&&G){G=Math.round(G);var Y=Math.round(1e3/G);L.setTransform(1,0,0,1,0,0),L.fillStyle="rgba(255, 0, 0, 0.75)",L.strokeStyle="rgba(255, 0, 0, 0.75)",L.lineWidth=1,L.fillText("1 frame = "+G+" ms = "+Y+" fps",0,20);L.strokeRect(0,30,250,20),L.fillRect(0,30,250*Math.min(Y/60,1),20)}n||(c[o.SELECT_BOX]=!1)}if(d&&1!==p){var X=u.contexts[o.NODE],V=o.data.bufferCanvases[o.MOTIONBLUR_BUFFER_NODE],U=u.contexts[o.DRAG],j=o.data.bufferCanvases[o.MOTIONBLUR_BUFFER_DRAG],q=function(e,t,n){e.setTransform(1,0,0,1,0,0),n||!y?e.clearRect(0,0,o.canvasWidth,o.canvasHeight):C(e,0,0,o.canvasWidth,o.canvasHeight);var r=p;e.drawImage(t,0,0,o.canvasWidth*r,o.canvasHeight*r,0,0,o.canvasWidth,o.canvasHeight)};(c[o.NODE]||R[o.NODE])&&(q(X,V,R[o.NODE]),c[o.NODE]=!1),(c[o.DRAG]||R[o.DRAG])&&(q(U,j,R[o.DRAG]),c[o.DRAG]=!1)}o.prevViewport=_,o.clearingMotionBlur&&(o.clearingMotionBlur=!1,o.motionBlurCleared=!0,o.motionBlur=!0),d&&(o.motionBlurTimeout=setTimeout((function(){o.motionBlurTimeout=null,o.clearedForMotionBlur[o.NODE]=!1,o.clearedForMotionBlur[o.DRAG]=!1,o.motionBlur=!1,o.clearingMotionBlur=!h,o.mbFrames=0,c[o.NODE]=!0,c[o.DRAG]=!0,o.redraw()}),100)),t||l.emit("render")};for(var zu={drawPolygonPath:function(e,t,n,r,i,a){var o=r/2,s=i/2;e.beginPath&&e.beginPath(),e.moveTo(t+o*a[0],n+s*a[1]);for(var l=1;l<a.length/2;l++)e.lineTo(t+o*a[2*l],n+s*a[2*l+1]);e.closePath()},drawRoundPolygonPath:function(e,t,n,r,i,a){var o=r/2,s=i/2,l=ln(r,i);e.beginPath&&e.beginPath();for(var u=0;u<a.length/4;u++){var c,h;c=0===u?a.length-2:4*u-2,h=4*u+2;var d=t+o*a[4*u],p=n+s*a[4*u+1],g=-a[c]*a[h]-a[c+1]*a[h+1],f=l/Math.tan(Math.acos(g)/2),v=d-f*a[c],y=p-f*a[c+1],m=d+f*a[h],b=p+f*a[h+1];0===u?e.moveTo(v,y):e.lineTo(v,y),e.arcTo(d,p,m,b,l)}e.closePath()},drawRoundRectanglePath:function(e,t,n,r,i){var a=r/2,o=i/2,s=sn(r,i);e.beginPath&&e.beginPath(),e.moveTo(t,n-o),e.arcTo(t+a,n-o,t+a,n,s),e.arcTo(t+a,n+o,t,n+o,s),e.arcTo(t-a,n+o,t-a,n,s),e.arcTo(t-a,n-o,t,n-o,s),e.lineTo(t,n-o),e.closePath()},drawBottomRoundRectanglePath:function(e,t,n,r,i){var a=r/2,o=i/2,s=sn(r,i);e.beginPath&&e.beginPath(),e.moveTo(t,n-o),e.lineTo(t+a,n-o),e.lineTo(t+a,n),e.arcTo(t+a,n+o,t,n+o,s),e.arcTo(t-a,n+o,t-a,n,s),e.lineTo(t-a,n-o),e.lineTo(t,n-o),e.closePath()},drawCutRectanglePath:function(e,t,n,r,i){var a=r/2,o=i/2;e.beginPath&&e.beginPath(),e.moveTo(t-a+8,n-o),e.lineTo(t+a-8,n-o),e.lineTo(t+a,n-o+8),e.lineTo(t+a,n+o-8),e.lineTo(t+a-8,n+o),e.lineTo(t-a+8,n+o),e.lineTo(t-a,n+o-8),e.lineTo(t-a,n-o+8),e.closePath()},drawBarrelPath:function(e,t,n,r,i){var a=r/2,o=i/2,s=t-a,l=t+a,u=n-o,c=n+o,h=un(r,i),d=h.widthOffset,p=h.heightOffset,g=h.ctrlPtOffsetPct*d;e.beginPath&&e.beginPath(),e.moveTo(s,u+p),e.lineTo(s,c-p),e.quadraticCurveTo(s+g,c,s+d,c),e.lineTo(l-d,c),e.quadraticCurveTo(l-g,c,l,c-p),e.lineTo(l,u+p),e.quadraticCurveTo(l-g,u,l-d,u),e.lineTo(s+d,u),e.quadraticCurveTo(s+g,u,s,u+p),e.closePath()}},Gu=Math.sin(0),Yu=Math.cos(0),Xu={},Vu={},Uu=Math.PI/40,ju=0*Math.PI;ju<2*Math.PI;ju+=Uu)Xu[ju]=Math.sin(ju),Vu[ju]=Math.cos(ju);zu.drawEllipsePath=function(e,t,n,r,i){if(e.beginPath&&e.beginPath(),e.ellipse)e.ellipse(t,n,r/2,i/2,0,0,2*Math.PI);else for(var a,o,s=r/2,l=i/2,u=0*Math.PI;u<2*Math.PI;u+=Uu)a=t-s*Xu[u]*Gu+s*Vu[u]*Yu,o=n+l*Vu[u]*Gu+l*Xu[u]*Yu,0===u?e.moveTo(a,o):e.lineTo(a,o);e.closePath()};var qu={};function Hu(e){var t=e.indexOf(",");return e.substr(t+1)}function Wu(e,t,n){var r=function(){return t.toDataURL(n,e.quality)};switch(e.output){case"blob-promise":return new _r((function(r,i){try{t.toBlob((function(e){null!=e?r(e):i(new Error("`canvas.toBlob()` sent a null value in its callback"))}),n,e.quality)}catch(e){i(e)}}));case"blob":return function(e,t){for(var n=atob(e),r=new ArrayBuffer(n.length),i=new Uint8Array(r),a=0;a<n.length;a++)i[a]=n.charCodeAt(a);return new Blob([r],{type:t})}(Hu(r()),n);case"base64":return Hu(r());default:return r()}}qu.createBuffer=function(e,t){var n=document.createElement("canvas");return n.width=e,n.height=t,[n,n.getContext("2d")]},qu.bufferCanvasImage=function(e){var t=this.cy,n=t.mutableElements().boundingBox(),r=this.findContainerClientCoords(),i=e.full?Math.ceil(n.w):r[2],a=e.full?Math.ceil(n.h):r[3],o=b(e.maxWidth)||b(e.maxHeight),s=this.getPixelRatio(),l=1;if(void 0!==e.scale)i*=e.scale,a*=e.scale,l=e.scale;else if(o){var u=1/0,c=1/0;b(e.maxWidth)&&(u=l*e.maxWidth/i),b(e.maxHeight)&&(c=l*e.maxHeight/a),i*=l=Math.min(u,c),a*=l}o||(i*=s,a*=s,l*=s);var h=document.createElement("canvas");h.width=i,h.height=a,h.style.width=i+"px",h.style.height=a+"px";var d=h.getContext("2d");if(i>0&&a>0){d.clearRect(0,0,i,a),d.globalCompositeOperation="source-over";var p=this.getCachedZSortedEles();if(e.full)d.translate(-n.x1*l,-n.y1*l),d.scale(l,l),this.drawElements(d,p),d.scale(1/l,1/l),d.translate(n.x1*l,n.y1*l);else{var g=t.pan(),f={x:g.x*l,y:g.y*l};l*=t.zoom(),d.translate(f.x,f.y),d.scale(l,l),this.drawElements(d,p),d.scale(1/l,1/l),d.translate(-f.x,-f.y)}e.bg&&(d.globalCompositeOperation="destination-over",d.fillStyle=e.bg,d.rect(0,0,i,a),d.fill())}return h},qu.png=function(e){return Wu(e,this.bufferCanvasImage(e),"image/png")},qu.jpg=function(e){return Wu(e,this.bufferCanvasImage(e),"image/jpeg")};var $u=Zu,Ku=Zu.prototype;function Zu(e){var t=this;t.data={canvases:new Array(Ku.CANVAS_LAYERS),contexts:new Array(Ku.CANVAS_LAYERS),canvasNeedsRedraw:new Array(Ku.CANVAS_LAYERS),bufferCanvases:new Array(Ku.BUFFER_COUNT),bufferContexts:new Array(Ku.CANVAS_LAYERS)};var n="-webkit-tap-highlight-color",r="rgba(0,0,0,0)";t.data.canvasContainer=document.createElement("div");var i=t.data.canvasContainer.style;t.data.canvasContainer.style[n]=r,i.position="relative",i.zIndex="0",i.overflow="hidden";var a=e.cy.container();a.appendChild(t.data.canvasContainer),a.style[n]=r;var o={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};u&&u.userAgent.match(/msie|trident|edge/i)&&(o["-ms-touch-action"]="none",o["touch-action"]="none");for(var s=0;s<Ku.CANVAS_LAYERS;s++){var l=t.data.canvases[s]=document.createElement("canvas");t.data.contexts[s]=l.getContext("2d"),Object.keys(o).forEach((function(e){l.style[e]=o[e]})),l.style.position="absolute",l.setAttribute("data-id","layer"+s),l.style.zIndex=String(Ku.CANVAS_LAYERS-s),t.data.canvasContainer.appendChild(l),t.data.canvasNeedsRedraw[s]=!1}for(t.data.topCanvas=t.data.canvases[0],t.data.canvases[Ku.NODE].setAttribute("data-id","layer"+Ku.NODE+"-node"),t.data.canvases[Ku.SELECT_BOX].setAttribute("data-id","layer"+Ku.SELECT_BOX+"-selectbox"),t.data.canvases[Ku.DRAG].setAttribute("data-id","layer"+Ku.DRAG+"-drag"),s=0;s<Ku.BUFFER_COUNT;s++)t.data.bufferCanvases[s]=document.createElement("canvas"),t.data.bufferContexts[s]=t.data.bufferCanvases[s].getContext("2d"),t.data.bufferCanvases[s].style.position="absolute",t.data.bufferCanvases[s].setAttribute("data-id","buffer"+s),t.data.bufferCanvases[s].style.zIndex=String(-s-1),t.data.bufferCanvases[s].style.visibility="hidden";t.pathsEnabled=!0;var c=Ot(),h=function(e){return{x:-e.w/2,y:-e.h/2}},d=function(e){return e.boundingBox(),e[0]._private.bodyBounds},p=function(e){return e.boundingBox(),e[0]._private.labelBounds.main||c},g=function(e){return e.boundingBox(),e[0]._private.labelBounds.source||c},f=function(e){return e.boundingBox(),e[0]._private.labelBounds.target||c},v=function(e,t){return t},y=function(e,t,n){var r=e?e+"-":"";return{x:t.x+n.pstyle(r+"text-margin-x").pfValue,y:t.y+n.pstyle(r+"text-margin-y").pfValue}},m=function(e,t,n){var r=e[0]._private.rscratch;return{x:r[t],y:r[n]}},b=t.data.eleTxrCache=new du(t,{getKey:function(e){return e[0]._private.nodeKey},doesEleInvalidateKey:function(e){var t=e[0]._private;return!(t.oldBackgroundTimestamp===t.backgroundTimestamp)},drawElement:function(e,n,r,i,a){return t.drawElement(e,n,r,!1,!1,a)},getBoundingBox:d,getRotationPoint:function(e){return{x:((t=d(e)).x1+t.x2)/2,y:(t.y1+t.y2)/2};var t},getRotationOffset:function(e){return h(d(e))},allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),x=t.data.lblTxrCache=new du(t,{getKey:function(e){return e[0]._private.labelStyleKey},drawElement:function(e,n,r,i,a){return t.drawElementText(e,n,r,i,"main",a)},getBoundingBox:p,getRotationPoint:function(e){return y("",m(e,"labelX","labelY"),e)},getRotationOffset:function(e){var t=p(e),n=h(p(e));if(e.isNode()){switch(e.pstyle("text-halign").value){case"left":n.x=-t.w;break;case"right":n.x=0}switch(e.pstyle("text-valign").value){case"top":n.y=-t.h;break;case"bottom":n.y=0}}return n},isVisible:v}),w=t.data.slbTxrCache=new du(t,{getKey:function(e){return e[0]._private.sourceLabelStyleKey},drawElement:function(e,n,r,i,a){return t.drawElementText(e,n,r,i,"source",a)},getBoundingBox:g,getRotationPoint:function(e){return y("source",m(e,"sourceLabelX","sourceLabelY"),e)},getRotationOffset:function(e){return h(g(e))},isVisible:v}),E=t.data.tlbTxrCache=new du(t,{getKey:function(e){return e[0]._private.targetLabelStyleKey},drawElement:function(e,n,r,i,a){return t.drawElementText(e,n,r,i,"target",a)},getBoundingBox:f,getRotationPoint:function(e){return y("target",m(e,"targetLabelX","targetLabelY"),e)},getRotationOffset:function(e){return h(f(e))},isVisible:v}),_=t.data.lyrTxrCache=new gu(t);t.onUpdateEleCalcs((function(e,t){b.invalidateElements(t),x.invalidateElements(t),w.invalidateElements(t),E.invalidateElements(t),_.invalidateElements(t);for(var n=0;n<t.length;n++){var r=t[n]._private;r.oldBackgroundTimestamp=r.backgroundTimestamp}}));var T=function(e){for(var t=0;t<e.length;t++)_.enqueueElementRefinement(e[t].ele)};b.onDequeue(T),x.onDequeue(T),w.onDequeue(T),E.onDequeue(T)}Ku.CANVAS_LAYERS=3,Ku.SELECT_BOX=0,Ku.DRAG=1,Ku.NODE=2,Ku.BUFFER_COUNT=3,Ku.TEXTURE_BUFFER=0,Ku.MOTIONBLUR_BUFFER_NODE=1,Ku.MOTIONBLUR_BUFFER_DRAG=2,Ku.redrawHint=function(e,t){var n=this;switch(e){case"eles":n.data.canvasNeedsRedraw[Ku.NODE]=t;break;case"drag":n.data.canvasNeedsRedraw[Ku.DRAG]=t;break;case"select":n.data.canvasNeedsRedraw[Ku.SELECT_BOX]=t}};var Qu="undefined"!=typeof Path2D;Ku.path2dEnabled=function(e){if(void 0===e)return this.pathsEnabled;this.pathsEnabled=!!e},Ku.usePaths=function(){return Qu&&this.pathsEnabled},Ku.setImgSmoothing=function(e,t){null!=e.imageSmoothingEnabled?e.imageSmoothingEnabled=t:(e.webkitImageSmoothingEnabled=t,e.mozImageSmoothingEnabled=t,e.msImageSmoothingEnabled=t)},Ku.getImgSmoothing=function(e){return null!=e.imageSmoothingEnabled?e.imageSmoothingEnabled:e.webkitImageSmoothingEnabled||e.mozImageSmoothingEnabled||e.msImageSmoothingEnabled},Ku.makeOffscreenCanvas=function(t,n){var r;return"undefined"!==("undefined"==typeof OffscreenCanvas?"undefined":e(OffscreenCanvas))?r=new OffscreenCanvas(t,n):((r=document.createElement("canvas")).width=t,r.height=n),r},[bu,Du,Iu,Ou,Pu,Ru,Fu,zu,qu,{nodeShapeImpl:function(e,t,n,r,i,a,o){switch(e){case"ellipse":return this.drawEllipsePath(t,n,r,i,a);case"polygon":return this.drawPolygonPath(t,n,r,i,a,o);case"round-polygon":return this.drawRoundPolygonPath(t,n,r,i,a,o);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(t,n,r,i,a);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(t,n,r,i,a);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(t,n,r,i,a);case"barrel":return this.drawBarrelPath(t,n,r,i,a)}}}].forEach((function(e){z(Ku,e)}));var Ju=[{type:"layout",extensions:Pl},{type:"renderer",extensions:[{name:"null",impl:Rl},{name:"base",impl:au},{name:"canvas",impl:$u}]}],ec={},tc={};function nc(e,t,n){var r=n,i=function(n){je("Can not register `"+t+"` for `"+e+"` since `"+n+"` already exists in the prototype and can not be overridden")};if("core"===e){if(Ws.prototype[t])return i(t);Ws.prototype[t]=n}else if("collection"===e){if(cs.prototype[t])return i(t);cs.prototype[t]=n}else if("layout"===e){for(var a=function(e){this.options=e,n.call(this,e),m(this._private)||(this._private={}),this._private.cy=e.cy,this._private.listeners=[],this.createEmitter()},o=a.prototype=Object.create(n.prototype),s=[],l=0;l<s.length;l++){var u=s[l];o[u]=o[u]||function(){return this}}o.start&&!o.run?o.run=function(){return this.start(),this}:!o.start&&o.run&&(o.start=function(){return this.run(),this});var c=n.prototype.stop;o.stop=function(){var e=this.options;if(e&&e.animate){var t=this.animations;if(t)for(var n=0;n<t.length;n++)t[n].stop()}return c?c.call(this):this.emit("layoutstop"),this},o.destroy||(o.destroy=function(){return this}),o.cy=function(){return this._private.cy};var h=function(e){return e._private.cy},d={addEventFields:function(e,t){t.layout=e,t.cy=h(e),t.target=e},bubble:function(){return!0},parent:function(e){return h(e)}};z(o,{createEmitter:function(){return this._private.emitter=new Ao(d,this),this},emitter:function(){return this._private.emitter},on:function(e,t){return this.emitter().on(e,t),this},one:function(e,t){return this.emitter().one(e,t),this},once:function(e,t){return this.emitter().one(e,t),this},removeListener:function(e,t){return this.emitter().removeListener(e,t),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},emit:function(e,t){return this.emitter().emit(e,t),this}}),Wi.eventAliasesOn(o),r=a}else if("renderer"===e&&"null"!==t&&"base"!==t){var p=rc("renderer","base"),g=p.prototype,f=n,v=n.prototype,y=function(){p.apply(this,arguments),f.apply(this,arguments)},b=y.prototype;for(var x in g){var w=g[x];if(null!=v[x])return i(x);b[x]=w}for(var E in v)b[E]=v[E];g.clientFunctions.forEach((function(e){b[e]=b[e]||function(){Ve("Renderer does not implement `renderer."+e+"()` on its prototype")}})),r=y}else if("__proto__"===e||"constructor"===e||"prototype"===e)return Ve(e+" is an illegal type to be registered, possibly lead to prototype pollutions");return X({map:ec,keys:[e,t],value:r})}function rc(e,t){return V({map:ec,keys:[e,t]})}function ic(e,t,n,r,i){return X({map:tc,keys:[e,t,n,r],value:i})}function ac(e,t,n,r){return V({map:tc,keys:[e,t,n,r]})}var oc=function(){return 2===arguments.length?rc.apply(null,arguments):3===arguments.length?nc.apply(null,arguments):4===arguments.length?ac.apply(null,arguments):5===arguments.length?ic.apply(null,arguments):void Ve("Invalid extension access syntax")};Ws.prototype.extension=oc,Ju.forEach((function(e){e.extensions.forEach((function(t){nc(e.type,t.name,t.impl)}))}));var sc=function e(){if(!(this instanceof e))return new e;this.length=0},lc=sc.prototype;lc.instanceString=function(){return"stylesheet"},lc.selector=function(e){return this[this.length++]={selector:e,properties:[]},this},lc.css=function(e,t){var n=this.length-1;if(f(e))this[n].properties.push({name:e,value:t});else if(m(e))for(var r=e,i=Object.keys(r),a=0;a<i.length;a++){var o=i[a],s=r[o];if(null!=s){var l=Vs.properties[o]||Vs.properties[k(o)];if(null!=l){var u=l.name,c=s;this[n].properties.push({name:u,value:c})}}}return this},lc.style=lc.css,lc.generateStyle=function(e){var t=new Vs(e);return this.appendToStyle(t)},lc.appendToStyle=function(e){for(var t=0;t<this.length;t++){var n=this[t],r=n.selector,i=n.properties;e.selector(r);for(var a=0;a<i.length;a++){var o=i[a];e.css(o.name,o.value)}}return e};var uc=function(e){return void 0===e&&(e={}),m(e)?new Ws(e):f(e)?oc.apply(oc,arguments):void 0};return uc.use=function(e){var t=Array.prototype.slice.call(arguments,1);return t.unshift(uc),e.apply(null,t),this},uc.warnings=function(e){return Ue(e)},uc.version="3.25.0",uc.stylesheet=uc.Stylesheet=sc,uc}()},2241:function(e){var t;t=function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.i=function(e){return e},n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=26)}([function(e,t,n){"use strict";function r(){}r.QUALITY=1,r.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,r.DEFAULT_INCREMENTAL=!1,r.DEFAULT_ANIMATION_ON_LAYOUT=!0,r.DEFAULT_ANIMATION_DURING_LAYOUT=!1,r.DEFAULT_ANIMATION_PERIOD=50,r.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,r.DEFAULT_GRAPH_MARGIN=15,r.NODE_DIMENSIONS_INCLUDE_LABELS=!1,r.SIMPLE_NODE_SIZE=40,r.SIMPLE_NODE_HALF_SIZE=r.SIMPLE_NODE_SIZE/2,r.EMPTY_COMPOUND_NODE_SIZE=40,r.MIN_EDGE_LENGTH=1,r.WORLD_BOUNDARY=1e6,r.INITIAL_WORLD_BOUNDARY=r.WORLD_BOUNDARY/1e3,r.WORLD_CENTER_X=1200,r.WORLD_CENTER_Y=900,e.exports=r},function(e,t,n){"use strict";var r=n(2),i=n(8),a=n(9);function o(e,t,n){r.call(this,n),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=n,this.bendpoints=[],this.source=e,this.target=t}for(var s in o.prototype=Object.create(r.prototype),r)o[s]=r[s];o.prototype.getSource=function(){return this.source},o.prototype.getTarget=function(){return this.target},o.prototype.isInterGraph=function(){return this.isInterGraph},o.prototype.getLength=function(){return this.length},o.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},o.prototype.getBendpoints=function(){return this.bendpoints},o.prototype.getLca=function(){return this.lca},o.prototype.getSourceInLca=function(){return this.sourceInLca},o.prototype.getTargetInLca=function(){return this.targetInLca},o.prototype.getOtherEnd=function(e){if(this.source===e)return this.target;if(this.target===e)return this.source;throw"Node is not incident with this edge"},o.prototype.getOtherEndInGraph=function(e,t){for(var n=this.getOtherEnd(e),r=t.getGraphManager().getRoot();;){if(n.getOwner()==t)return n;if(n.getOwner()==r)break;n=n.getOwner().getParent()}return null},o.prototype.updateLength=function(){var e=new Array(4);this.isOverlapingSourceAndTarget=i.getIntersection(this.target.getRect(),this.source.getRect(),e),this.isOverlapingSourceAndTarget||(this.lengthX=e[0]-e[2],this.lengthY=e[1]-e[3],Math.abs(this.lengthX)<1&&(this.lengthX=a.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=a.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},o.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=a.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=a.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},e.exports=o},function(e,t,n){"use strict";e.exports=function(e){this.vGraphObject=e}},function(e,t,n){"use strict";var r=n(2),i=n(10),a=n(13),o=n(0),s=n(16),l=n(4);function u(e,t,n,o){null==n&&null==o&&(o=t),r.call(this,o),null!=e.graphManager&&(e=e.graphManager),this.estimatedSize=i.MIN_VALUE,this.inclusionTreeDepth=i.MAX_VALUE,this.vGraphObject=o,this.edges=[],this.graphManager=e,this.rect=null!=n&&null!=t?new a(t.x,t.y,n.width,n.height):new a}for(var c in u.prototype=Object.create(r.prototype),r)u[c]=r[c];u.prototype.getEdges=function(){return this.edges},u.prototype.getChild=function(){return this.child},u.prototype.getOwner=function(){return this.owner},u.prototype.getWidth=function(){return this.rect.width},u.prototype.setWidth=function(e){this.rect.width=e},u.prototype.getHeight=function(){return this.rect.height},u.prototype.setHeight=function(e){this.rect.height=e},u.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},u.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},u.prototype.getCenter=function(){return new l(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},u.prototype.getLocation=function(){return new l(this.rect.x,this.rect.y)},u.prototype.getRect=function(){return this.rect},u.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},u.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},u.prototype.setRect=function(e,t){this.rect.x=e.x,this.rect.y=e.y,this.rect.width=t.width,this.rect.height=t.height},u.prototype.setCenter=function(e,t){this.rect.x=e-this.rect.width/2,this.rect.y=t-this.rect.height/2},u.prototype.setLocation=function(e,t){this.rect.x=e,this.rect.y=t},u.prototype.moveBy=function(e,t){this.rect.x+=e,this.rect.y+=t},u.prototype.getEdgeListToNode=function(e){var t=[],n=this;return n.edges.forEach((function(r){if(r.target==e){if(r.source!=n)throw"Incorrect edge source!";t.push(r)}})),t},u.prototype.getEdgesBetween=function(e){var t=[],n=this;return n.edges.forEach((function(r){if(r.source!=n&&r.target!=n)throw"Incorrect edge source and/or target";r.target!=e&&r.source!=e||t.push(r)})),t},u.prototype.getNeighborsList=function(){var e=new Set,t=this;return t.edges.forEach((function(n){if(n.source==t)e.add(n.target);else{if(n.target!=t)throw"Incorrect incidency!";e.add(n.source)}})),e},u.prototype.withChildren=function(){var e=new Set;if(e.add(this),null!=this.child)for(var t=this.child.getNodes(),n=0;n<t.length;n++)t[n].withChildren().forEach((function(t){e.add(t)}));return e},u.prototype.getNoOfChildren=function(){var e=0;if(null==this.child)e=1;else for(var t=this.child.getNodes(),n=0;n<t.length;n++)e+=t[n].getNoOfChildren();return 0==e&&(e=1),e},u.prototype.getEstimatedSize=function(){if(this.estimatedSize==i.MIN_VALUE)throw"assert failed";return this.estimatedSize},u.prototype.calcEstimatedSize=function(){return null==this.child?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},u.prototype.scatter=function(){var e,t,n=-o.INITIAL_WORLD_BOUNDARY,r=o.INITIAL_WORLD_BOUNDARY;e=o.WORLD_CENTER_X+s.nextDouble()*(r-n)+n;var i=-o.INITIAL_WORLD_BOUNDARY,a=o.INITIAL_WORLD_BOUNDARY;t=o.WORLD_CENTER_Y+s.nextDouble()*(a-i)+i,this.rect.x=e,this.rect.y=t},u.prototype.updateBounds=function(){if(null==this.getChild())throw"assert failed";if(0!=this.getChild().getNodes().length){var e=this.getChild();if(e.updateBounds(!0),this.rect.x=e.getLeft(),this.rect.y=e.getTop(),this.setWidth(e.getRight()-e.getLeft()),this.setHeight(e.getBottom()-e.getTop()),o.NODE_DIMENSIONS_INCLUDE_LABELS){var t=e.getRight()-e.getLeft(),n=e.getBottom()-e.getTop();this.labelWidth>t&&(this.rect.x-=(this.labelWidth-t)/2,this.setWidth(this.labelWidth)),this.labelHeight>n&&("center"==this.labelPos?this.rect.y-=(this.labelHeight-n)/2:"top"==this.labelPos&&(this.rect.y-=this.labelHeight-n),this.setHeight(this.labelHeight))}}},u.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==i.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},u.prototype.transform=function(e){var t=this.rect.x;t>o.WORLD_BOUNDARY?t=o.WORLD_BOUNDARY:t<-o.WORLD_BOUNDARY&&(t=-o.WORLD_BOUNDARY);var n=this.rect.y;n>o.WORLD_BOUNDARY?n=o.WORLD_BOUNDARY:n<-o.WORLD_BOUNDARY&&(n=-o.WORLD_BOUNDARY);var r=new l(t,n),i=e.inverseTransformPoint(r);this.setLocation(i.x,i.y)},u.prototype.getLeft=function(){return this.rect.x},u.prototype.getRight=function(){return this.rect.x+this.rect.width},u.prototype.getTop=function(){return this.rect.y},u.prototype.getBottom=function(){return this.rect.y+this.rect.height},u.prototype.getParent=function(){return null==this.owner?null:this.owner.getParent()},e.exports=u},function(e,t,n){"use strict";function r(e,t){null==e&&null==t?(this.x=0,this.y=0):(this.x=e,this.y=t)}r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r.prototype.setX=function(e){this.x=e},r.prototype.setY=function(e){this.y=e},r.prototype.getDifference=function(e){return new DimensionD(this.x-e.x,this.y-e.y)},r.prototype.getCopy=function(){return new r(this.x,this.y)},r.prototype.translate=function(e){return this.x+=e.width,this.y+=e.height,this},e.exports=r},function(e,t,n){"use strict";var r=n(2),i=n(10),a=n(0),o=n(6),s=n(3),l=n(1),u=n(13),c=n(12),h=n(11);function d(e,t,n){r.call(this,n),this.estimatedSize=i.MIN_VALUE,this.margin=a.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=e,null!=t&&t instanceof o?this.graphManager=t:null!=t&&t instanceof Layout&&(this.graphManager=t.graphManager)}for(var p in d.prototype=Object.create(r.prototype),r)d[p]=r[p];d.prototype.getNodes=function(){return this.nodes},d.prototype.getEdges=function(){return this.edges},d.prototype.getGraphManager=function(){return this.graphManager},d.prototype.getParent=function(){return this.parent},d.prototype.getLeft=function(){return this.left},d.prototype.getRight=function(){return this.right},d.prototype.getTop=function(){return this.top},d.prototype.getBottom=function(){return this.bottom},d.prototype.isConnected=function(){return this.isConnected},d.prototype.add=function(e,t,n){if(null==t&&null==n){var r=e;if(null==this.graphManager)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(r)>-1)throw"Node already in graph!";return r.owner=this,this.getNodes().push(r),r}var i=e;if(!(this.getNodes().indexOf(t)>-1&&this.getNodes().indexOf(n)>-1))throw"Source or target not in graph!";if(t.owner!=n.owner||t.owner!=this)throw"Both owners must be this graph!";return t.owner!=n.owner?null:(i.source=t,i.target=n,i.isInterGraph=!1,this.getEdges().push(i),t.edges.push(i),n!=t&&n.edges.push(i),i)},d.prototype.remove=function(e){var t=e;if(e instanceof s){if(null==t)throw"Node is null!";if(null==t.owner||t.owner!=this)throw"Owner graph is invalid!";if(null==this.graphManager)throw"Owner graph manager is invalid!";for(var n=t.edges.slice(),r=n.length,i=0;i<r;i++)(a=n[i]).isInterGraph?this.graphManager.remove(a):a.source.owner.remove(a);if(-1==(o=this.nodes.indexOf(t)))throw"Node not in owner node list!";this.nodes.splice(o,1)}else if(e instanceof l){var a;if(null==(a=e))throw"Edge is null!";if(null==a.source||null==a.target)throw"Source and/or target is null!";if(null==a.source.owner||null==a.target.owner||a.source.owner!=this||a.target.owner!=this)throw"Source and/or target owner is invalid!";var o,u=a.source.edges.indexOf(a),c=a.target.edges.indexOf(a);if(!(u>-1&&c>-1))throw"Source and/or target doesn't know this edge!";if(a.source.edges.splice(u,1),a.target!=a.source&&a.target.edges.splice(c,1),-1==(o=a.source.owner.getEdges().indexOf(a)))throw"Not in owner's edge list!";a.source.owner.getEdges().splice(o,1)}},d.prototype.updateLeftTop=function(){for(var e,t,n,r=i.MAX_VALUE,a=i.MAX_VALUE,o=this.getNodes(),s=o.length,l=0;l<s;l++){var u=o[l];r>(e=u.getTop())&&(r=e),a>(t=u.getLeft())&&(a=t)}return r==i.MAX_VALUE?null:(n=null!=o[0].getParent().paddingLeft?o[0].getParent().paddingLeft:this.margin,this.left=a-n,this.top=r-n,new c(this.left,this.top))},d.prototype.updateBounds=function(e){for(var t,n,r,a,o,s=i.MAX_VALUE,l=-i.MAX_VALUE,c=i.MAX_VALUE,h=-i.MAX_VALUE,d=this.nodes,p=d.length,g=0;g<p;g++){var f=d[g];e&&null!=f.child&&f.updateBounds(),s>(t=f.getLeft())&&(s=t),l<(n=f.getRight())&&(l=n),c>(r=f.getTop())&&(c=r),h<(a=f.getBottom())&&(h=a)}var v=new u(s,c,l-s,h-c);s==i.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),o=null!=d[0].getParent().paddingLeft?d[0].getParent().paddingLeft:this.margin,this.left=v.x-o,this.right=v.x+v.width+o,this.top=v.y-o,this.bottom=v.y+v.height+o},d.calculateBounds=function(e){for(var t,n,r,a,o=i.MAX_VALUE,s=-i.MAX_VALUE,l=i.MAX_VALUE,c=-i.MAX_VALUE,h=e.length,d=0;d<h;d++){var p=e[d];o>(t=p.getLeft())&&(o=t),s<(n=p.getRight())&&(s=n),l>(r=p.getTop())&&(l=r),c<(a=p.getBottom())&&(c=a)}return new u(o,l,s-o,c-l)},d.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},d.prototype.getEstimatedSize=function(){if(this.estimatedSize==i.MIN_VALUE)throw"assert failed";return this.estimatedSize},d.prototype.calcEstimatedSize=function(){for(var e=0,t=this.nodes,n=t.length,r=0;r<n;r++)e+=t[r].calcEstimatedSize();return this.estimatedSize=0==e?a.EMPTY_COMPOUND_NODE_SIZE:e/Math.sqrt(this.nodes.length),this.estimatedSize},d.prototype.updateConnected=function(){var e=this;if(0!=this.nodes.length){var t,n,r=new h,i=new Set,a=this.nodes[0];for(a.withChildren().forEach((function(e){r.push(e),i.add(e)}));0!==r.length;)for(var o=(t=(a=r.shift()).getEdges()).length,s=0;s<o;s++)null==(n=t[s].getOtherEndInGraph(a,this))||i.has(n)||n.withChildren().forEach((function(e){r.push(e),i.add(e)}));if(this.isConnected=!1,i.size>=this.nodes.length){var l=0;i.forEach((function(t){t.owner==e&&l++})),l==this.nodes.length&&(this.isConnected=!0)}}else this.isConnected=!0},e.exports=d},function(e,t,n){"use strict";var r,i=n(1);function a(e){r=n(5),this.layout=e,this.graphs=[],this.edges=[]}a.prototype.addRoot=function(){var e=this.layout.newGraph(),t=this.layout.newNode(null),n=this.add(e,t);return this.setRootGraph(n),this.rootGraph},a.prototype.add=function(e,t,n,r,i){if(null==n&&null==r&&null==i){if(null==e)throw"Graph is null!";if(null==t)throw"Parent node is null!";if(this.graphs.indexOf(e)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(e),null!=e.parent)throw"Already has a parent!";if(null!=t.child)throw"Already has a child!";return e.parent=t,t.child=e,e}i=n,n=e;var a=(r=t).getOwner(),o=i.getOwner();if(null==a||a.getGraphManager()!=this)throw"Source not in this graph mgr!";if(null==o||o.getGraphManager()!=this)throw"Target not in this graph mgr!";if(a==o)return n.isInterGraph=!1,a.add(n,r,i);if(n.isInterGraph=!0,n.source=r,n.target=i,this.edges.indexOf(n)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(n),null==n.source||null==n.target)throw"Edge source and/or target is null!";if(-1!=n.source.edges.indexOf(n)||-1!=n.target.edges.indexOf(n))throw"Edge already in source and/or target incidency list!";return n.source.edges.push(n),n.target.edges.push(n),n},a.prototype.remove=function(e){if(e instanceof r){var t=e;if(t.getGraphManager()!=this)throw"Graph not in this graph mgr";if(t!=this.rootGraph&&(null==t.parent||t.parent.graphManager!=this))throw"Invalid parent node!";for(var n,a=[],o=(a=a.concat(t.getEdges())).length,s=0;s<o;s++)n=a[s],t.remove(n);var l,u=[];for(o=(u=u.concat(t.getNodes())).length,s=0;s<o;s++)l=u[s],t.remove(l);t==this.rootGraph&&this.setRootGraph(null);var c=this.graphs.indexOf(t);this.graphs.splice(c,1),t.parent=null}else if(e instanceof i){if(null==(n=e))throw"Edge is null!";if(!n.isInterGraph)throw"Not an inter-graph edge!";if(null==n.source||null==n.target)throw"Source and/or target is null!";if(-1==n.source.edges.indexOf(n)||-1==n.target.edges.indexOf(n))throw"Source and/or target doesn't know this edge!";if(c=n.source.edges.indexOf(n),n.source.edges.splice(c,1),c=n.target.edges.indexOf(n),n.target.edges.splice(c,1),null==n.source.owner||null==n.source.owner.getGraphManager())throw"Edge owner graph or owner graph manager is null!";if(-1==n.source.owner.getGraphManager().edges.indexOf(n))throw"Not in owner graph manager's edge list!";c=n.source.owner.getGraphManager().edges.indexOf(n),n.source.owner.getGraphManager().edges.splice(c,1)}},a.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},a.prototype.getGraphs=function(){return this.graphs},a.prototype.getAllNodes=function(){if(null==this.allNodes){for(var e=[],t=this.getGraphs(),n=t.length,r=0;r<n;r++)e=e.concat(t[r].getNodes());this.allNodes=e}return this.allNodes},a.prototype.resetAllNodes=function(){this.allNodes=null},a.prototype.resetAllEdges=function(){this.allEdges=null},a.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},a.prototype.getAllEdges=function(){if(null==this.allEdges){for(var e=[],t=this.getGraphs(),n=(t.length,0);n<t.length;n++)e=e.concat(t[n].getEdges());e=e.concat(this.edges),this.allEdges=e}return this.allEdges},a.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},a.prototype.setAllNodesToApplyGravitation=function(e){if(null!=this.allNodesToApplyGravitation)throw"assert failed";this.allNodesToApplyGravitation=e},a.prototype.getRoot=function(){return this.rootGraph},a.prototype.setRootGraph=function(e){if(e.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=e,null==e.parent&&(e.parent=this.layout.newNode("Root node"))},a.prototype.getLayout=function(){return this.layout},a.prototype.isOneAncestorOfOther=function(e,t){if(null==e||null==t)throw"assert failed";if(e==t)return!0;for(var n,r=e.getOwner();null!=(n=r.getParent());){if(n==t)return!0;if(null==(r=n.getOwner()))break}for(r=t.getOwner();null!=(n=r.getParent());){if(n==e)return!0;if(null==(r=n.getOwner()))break}return!1},a.prototype.calcLowestCommonAncestors=function(){for(var e,t,n,r,i,a=this.getAllEdges(),o=a.length,s=0;s<o;s++)if(t=(e=a[s]).source,n=e.target,e.lca=null,e.sourceInLca=t,e.targetInLca=n,t!=n){for(r=t.getOwner();null==e.lca;){for(e.targetInLca=n,i=n.getOwner();null==e.lca;){if(i==r){e.lca=i;break}if(i==this.rootGraph)break;if(null!=e.lca)throw"assert failed";e.targetInLca=i.getParent(),i=e.targetInLca.getOwner()}if(r==this.rootGraph)break;null==e.lca&&(e.sourceInLca=r.getParent(),r=e.sourceInLca.getOwner())}if(null==e.lca)throw"assert failed"}else e.lca=t.getOwner()},a.prototype.calcLowestCommonAncestor=function(e,t){if(e==t)return e.getOwner();for(var n=e.getOwner();null!=n;){for(var r=t.getOwner();null!=r;){if(r==n)return r;r=r.getParent().getOwner()}n=n.getParent().getOwner()}return n},a.prototype.calcInclusionTreeDepths=function(e,t){var n;null==e&&null==t&&(e=this.rootGraph,t=1);for(var r=e.getNodes(),i=r.length,a=0;a<i;a++)(n=r[a]).inclusionTreeDepth=t,null!=n.child&&this.calcInclusionTreeDepths(n.child,t+1)},a.prototype.includesInvalidEdge=function(){for(var e,t=this.edges.length,n=0;n<t;n++)if(e=this.edges[n],this.isOneAncestorOfOther(e.source,e.target))return!0;return!1},e.exports=a},function(e,t,n){"use strict";var r=n(0);function i(){}for(var a in r)i[a]=r[a];i.MAX_ITERATIONS=2500,i.DEFAULT_EDGE_LENGTH=50,i.DEFAULT_SPRING_STRENGTH=.45,i.DEFAULT_REPULSION_STRENGTH=4500,i.DEFAULT_GRAVITY_STRENGTH=.4,i.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,i.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,i.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,i.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,i.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,i.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,i.COOLING_ADAPTATION_FACTOR=.33,i.ADAPTATION_LOWER_NODE_LIMIT=1e3,i.ADAPTATION_UPPER_NODE_LIMIT=5e3,i.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,i.MAX_NODE_DISPLACEMENT=3*i.MAX_NODE_DISPLACEMENT_INCREMENTAL,i.MIN_REPULSION_DIST=i.DEFAULT_EDGE_LENGTH/10,i.CONVERGENCE_CHECK_PERIOD=100,i.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,i.MIN_EDGE_LENGTH=1,i.GRID_CALCULATION_CHECK_PERIOD=10,e.exports=i},function(e,t,n){"use strict";var r=n(12);function i(){}i.calcSeparationAmount=function(e,t,n,r){if(!e.intersects(t))throw"assert failed";var i=new Array(2);this.decideDirectionsForOverlappingNodes(e,t,i),n[0]=Math.min(e.getRight(),t.getRight())-Math.max(e.x,t.x),n[1]=Math.min(e.getBottom(),t.getBottom())-Math.max(e.y,t.y),e.getX()<=t.getX()&&e.getRight()>=t.getRight()?n[0]+=Math.min(t.getX()-e.getX(),e.getRight()-t.getRight()):t.getX()<=e.getX()&&t.getRight()>=e.getRight()&&(n[0]+=Math.min(e.getX()-t.getX(),t.getRight()-e.getRight())),e.getY()<=t.getY()&&e.getBottom()>=t.getBottom()?n[1]+=Math.min(t.getY()-e.getY(),e.getBottom()-t.getBottom()):t.getY()<=e.getY()&&t.getBottom()>=e.getBottom()&&(n[1]+=Math.min(e.getY()-t.getY(),t.getBottom()-e.getBottom()));var a=Math.abs((t.getCenterY()-e.getCenterY())/(t.getCenterX()-e.getCenterX()));t.getCenterY()===e.getCenterY()&&t.getCenterX()===e.getCenterX()&&(a=1);var o=a*n[0],s=n[1]/a;n[0]<s?s=n[0]:o=n[1],n[0]=-1*i[0]*(s/2+r),n[1]=-1*i[1]*(o/2+r)},i.decideDirectionsForOverlappingNodes=function(e,t,n){e.getCenterX()<t.getCenterX()?n[0]=-1:n[0]=1,e.getCenterY()<t.getCenterY()?n[1]=-1:n[1]=1},i.getIntersection2=function(e,t,n){var r=e.getCenterX(),i=e.getCenterY(),a=t.getCenterX(),o=t.getCenterY();if(e.intersects(t))return n[0]=r,n[1]=i,n[2]=a,n[3]=o,!0;var s=e.getX(),l=e.getY(),u=e.getRight(),c=e.getX(),h=e.getBottom(),d=e.getRight(),p=e.getWidthHalf(),g=e.getHeightHalf(),f=t.getX(),v=t.getY(),y=t.getRight(),m=t.getX(),b=t.getBottom(),x=t.getRight(),w=t.getWidthHalf(),E=t.getHeightHalf(),_=!1,T=!1;if(r===a){if(i>o)return n[0]=r,n[1]=l,n[2]=a,n[3]=b,!1;if(i<o)return n[0]=r,n[1]=h,n[2]=a,n[3]=v,!1}else if(i===o){if(r>a)return n[0]=s,n[1]=i,n[2]=y,n[3]=o,!1;if(r<a)return n[0]=u,n[1]=i,n[2]=f,n[3]=o,!1}else{var D=e.height/e.width,C=t.height/t.width,N=(o-i)/(a-r),A=void 0,L=void 0,k=void 0,S=void 0,I=void 0,M=void 0;if(-D===N?r>a?(n[0]=c,n[1]=h,_=!0):(n[0]=u,n[1]=l,_=!0):D===N&&(r>a?(n[0]=s,n[1]=l,_=!0):(n[0]=d,n[1]=h,_=!0)),-C===N?a>r?(n[2]=m,n[3]=b,T=!0):(n[2]=y,n[3]=v,T=!0):C===N&&(a>r?(n[2]=f,n[3]=v,T=!0):(n[2]=x,n[3]=b,T=!0)),_&&T)return!1;if(r>a?i>o?(A=this.getCardinalDirection(D,N,4),L=this.getCardinalDirection(C,N,2)):(A=this.getCardinalDirection(-D,N,3),L=this.getCardinalDirection(-C,N,1)):i>o?(A=this.getCardinalDirection(-D,N,1),L=this.getCardinalDirection(-C,N,3)):(A=this.getCardinalDirection(D,N,2),L=this.getCardinalDirection(C,N,4)),!_)switch(A){case 1:S=l,k=r+-g/N,n[0]=k,n[1]=S;break;case 2:k=d,S=i+p*N,n[0]=k,n[1]=S;break;case 3:S=h,k=r+g/N,n[0]=k,n[1]=S;break;case 4:k=c,S=i+-p*N,n[0]=k,n[1]=S}if(!T)switch(L){case 1:M=v,I=a+-E/N,n[2]=I,n[3]=M;break;case 2:I=x,M=o+w*N,n[2]=I,n[3]=M;break;case 3:M=b,I=a+E/N,n[2]=I,n[3]=M;break;case 4:I=m,M=o+-w*N,n[2]=I,n[3]=M}}return!1},i.getCardinalDirection=function(e,t,n){return e>t?n:1+n%4},i.getIntersection=function(e,t,n,i){if(null==i)return this.getIntersection2(e,t,n);var a,o,s,l,u,c,h,d=e.x,p=e.y,g=t.x,f=t.y,v=n.x,y=n.y,m=i.x,b=i.y;return 0==(h=(a=f-p)*(l=v-m)-(o=b-y)*(s=d-g))?null:new r((s*(c=m*y-v*b)-l*(u=g*p-d*f))/h,(o*u-a*c)/h)},i.angleOfVector=function(e,t,n,r){var i=void 0;return e!==n?(i=Math.atan((r-t)/(n-e)),n<e?i+=Math.PI:r<t&&(i+=this.TWO_PI)):i=r<t?this.ONE_AND_HALF_PI:this.HALF_PI,i},i.doIntersect=function(e,t,n,r){var i=e.x,a=e.y,o=t.x,s=t.y,l=n.x,u=n.y,c=r.x,h=r.y,d=(o-i)*(h-u)-(c-l)*(s-a);if(0===d)return!1;var p=((h-u)*(c-i)+(l-c)*(h-a))/d,g=((a-s)*(c-i)+(o-i)*(h-a))/d;return 0<p&&p<1&&0<g&&g<1},i.HALF_PI=.5*Math.PI,i.ONE_AND_HALF_PI=1.5*Math.PI,i.TWO_PI=2*Math.PI,i.THREE_PI=3*Math.PI,e.exports=i},function(e,t,n){"use strict";function r(){}r.sign=function(e){return e>0?1:e<0?-1:0},r.floor=function(e){return e<0?Math.ceil(e):Math.floor(e)},r.ceil=function(e){return e<0?Math.floor(e):Math.ceil(e)},e.exports=r},function(e,t,n){"use strict";function r(){}r.MAX_VALUE=2147483647,r.MIN_VALUE=-2147483648,e.exports=r},function(e,t,n){"use strict";var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=function(e){return{value:e,next:null,prev:null}},a=function(e,t,n,r){return null!==e?e.next=t:r.head=t,null!==n?n.prev=t:r.tail=t,t.prev=e,t.next=n,r.length++,t},o=function(e,t){var n=e.prev,r=e.next;return null!==n?n.next=r:t.head=r,null!==r?r.prev=n:t.tail=n,e.prev=e.next=null,t.length--,e},s=function(){function e(t){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.length=0,this.head=null,this.tail=null,null!=t&&t.forEach((function(e){return n.push(e)}))}return r(e,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(e,t){return a(t.prev,i(e),t,this)}},{key:"insertAfter",value:function(e,t){return a(t,i(e),t.next,this)}},{key:"insertNodeBefore",value:function(e,t){return a(t.prev,e,t,this)}},{key:"insertNodeAfter",value:function(e,t){return a(t,e,t.next,this)}},{key:"push",value:function(e){return a(this.tail,i(e),null,this)}},{key:"unshift",value:function(e){return a(null,i(e),this.head,this)}},{key:"remove",value:function(e){return o(e,this)}},{key:"pop",value:function(){return o(this.tail,this).value}},{key:"popNode",value:function(){return o(this.tail,this)}},{key:"shift",value:function(){return o(this.head,this).value}},{key:"shiftNode",value:function(){return o(this.head,this)}},{key:"get_object_at",value:function(e){if(e<=this.length()){for(var t=1,n=this.head;t<e;)n=n.next,t++;return n.value}}},{key:"set_object_at",value:function(e,t){if(e<=this.length()){for(var n=1,r=this.head;n<e;)r=r.next,n++;r.value=t}}}]),e}();e.exports=s},function(e,t,n){"use strict";function r(e,t,n){this.x=null,this.y=null,null==e&&null==t&&null==n?(this.x=0,this.y=0):"number"==typeof e&&"number"==typeof t&&null==n?(this.x=e,this.y=t):"Point"==e.constructor.name&&null==t&&null==n&&(n=e,this.x=n.x,this.y=n.y)}r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r.prototype.getLocation=function(){return new r(this.x,this.y)},r.prototype.setLocation=function(e,t,n){"Point"==e.constructor.name&&null==t&&null==n?(n=e,this.setLocation(n.x,n.y)):"number"==typeof e&&"number"==typeof t&&null==n&&(parseInt(e)==e&&parseInt(t)==t?this.move(e,t):(this.x=Math.floor(e+.5),this.y=Math.floor(t+.5)))},r.prototype.move=function(e,t){this.x=e,this.y=t},r.prototype.translate=function(e,t){this.x+=e,this.y+=t},r.prototype.equals=function(e){if("Point"==e.constructor.name){var t=e;return this.x==t.x&&this.y==t.y}return this==e},r.prototype.toString=function(){return(new r).constructor.name+"[x="+this.x+",y="+this.y+"]"},e.exports=r},function(e,t,n){"use strict";function r(e,t,n,r){this.x=0,this.y=0,this.width=0,this.height=0,null!=e&&null!=t&&null!=n&&null!=r&&(this.x=e,this.y=t,this.width=n,this.height=r)}r.prototype.getX=function(){return this.x},r.prototype.setX=function(e){this.x=e},r.prototype.getY=function(){return this.y},r.prototype.setY=function(e){this.y=e},r.prototype.getWidth=function(){return this.width},r.prototype.setWidth=function(e){this.width=e},r.prototype.getHeight=function(){return this.height},r.prototype.setHeight=function(e){this.height=e},r.prototype.getRight=function(){return this.x+this.width},r.prototype.getBottom=function(){return this.y+this.height},r.prototype.intersects=function(e){return!(this.getRight()<e.x||this.getBottom()<e.y||e.getRight()<this.x||e.getBottom()<this.y)},r.prototype.getCenterX=function(){return this.x+this.width/2},r.prototype.getMinX=function(){return this.getX()},r.prototype.getMaxX=function(){return this.getX()+this.width},r.prototype.getCenterY=function(){return this.y+this.height/2},r.prototype.getMinY=function(){return this.getY()},r.prototype.getMaxY=function(){return this.getY()+this.height},r.prototype.getWidthHalf=function(){return this.width/2},r.prototype.getHeightHalf=function(){return this.height/2},e.exports=r},function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function i(){}i.lastID=0,i.createID=function(e){return i.isPrimitive(e)?e:(null!=e.uniqueID||(e.uniqueID=i.getString(),i.lastID++),e.uniqueID)},i.getString=function(e){return null==e&&(e=i.lastID),"Object#"+e},i.isPrimitive=function(e){var t=void 0===e?"undefined":r(e);return null==e||"object"!=t&&"function"!=t},e.exports=i},function(e,t,n){"use strict";function r(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}var i=n(0),a=n(6),o=n(3),s=n(1),l=n(5),u=n(4),c=n(17),h=n(27);function d(e){h.call(this),this.layoutQuality=i.QUALITY,this.createBendsAsNeeded=i.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=i.DEFAULT_INCREMENTAL,this.animationOnLayout=i.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=i.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=i.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=i.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new a(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,null!=e&&(this.isRemoteUse=e)}d.RANDOM_SEED=1,d.prototype=Object.create(h.prototype),d.prototype.getGraphManager=function(){return this.graphManager},d.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},d.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},d.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},d.prototype.newGraphManager=function(){var e=new a(this);return this.graphManager=e,e},d.prototype.newGraph=function(e){return new l(null,this.graphManager,e)},d.prototype.newNode=function(e){return new o(this.graphManager,e)},d.prototype.newEdge=function(e){return new s(null,null,e)},d.prototype.checkLayoutSuccess=function(){return null==this.graphManager.getRoot()||0==this.graphManager.getRoot().getNodes().length||this.graphManager.includesInvalidEdge()},d.prototype.runLayout=function(){var e;return this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters(),e=!this.checkLayoutSuccess()&&this.layout(),"during"!==i.ANIMATE&&(e&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,e)},d.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},d.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var e=this.graphManager.getAllEdges(),t=0;t<e.length;t++)e[t];var n=this.graphManager.getRoot().getNodes();for(t=0;t<n.length;t++)n[t];this.update(this.graphManager.getRoot())}},d.prototype.update=function(e){if(null==e)this.update2();else if(e instanceof o){var t=e;if(null!=t.getChild())for(var n=t.getChild().getNodes(),r=0;r<n.length;r++)update(n[r]);null!=t.vGraphObject&&t.vGraphObject.update(t)}else if(e instanceof s){var i=e;null!=i.vGraphObject&&i.vGraphObject.update(i)}else if(e instanceof l){var a=e;null!=a.vGraphObject&&a.vGraphObject.update(a)}},d.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=i.QUALITY,this.animationDuringLayout=i.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=i.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=i.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=i.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=i.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=i.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},d.prototype.transform=function(e){if(null==e)this.transform(new u(0,0));else{var t=new c,n=this.graphManager.getRoot().updateLeftTop();if(null!=n){t.setWorldOrgX(e.x),t.setWorldOrgY(e.y),t.setDeviceOrgX(n.x),t.setDeviceOrgY(n.y);for(var r=this.getAllNodes(),i=0;i<r.length;i++)r[i].transform(t)}}},d.prototype.positionNodesRandomly=function(e){if(null==e)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var t,n,r=e.getNodes(),i=0;i<r.length;i++)null==(n=(t=r[i]).getChild())||0==n.getNodes().length?t.scatter():(this.positionNodesRandomly(n),t.updateBounds())},d.prototype.getFlatForest=function(){for(var e=[],t=!0,n=this.graphManager.getRoot().getNodes(),i=!0,a=0;a<n.length;a++)null!=n[a].getChild()&&(i=!1);if(!i)return e;var o=new Set,s=[],l=new Map,u=[];for(u=u.concat(n);u.length>0&&t;){for(s.push(u[0]);s.length>0&&t;){var c=s[0];s.splice(0,1),o.add(c);var h=c.getEdges();for(a=0;a<h.length;a++){var d=h[a].getOtherEnd(c);if(l.get(c)!=d){if(o.has(d)){t=!1;break}s.push(d),l.set(d,c)}}}if(t){var p=[].concat(r(o));for(e.push(p),a=0;a<p.length;a++){var g=p[a],f=u.indexOf(g);f>-1&&u.splice(f,1)}o=new Set,l=new Map}else e=[]}return e},d.prototype.createDummyNodesForBendpoints=function(e){for(var t=[],n=e.source,r=this.graphManager.calcLowestCommonAncestor(e.source,e.target),i=0;i<e.bendpoints.length;i++){var a=this.newNode(null);a.setRect(new Point(0,0),new Dimension(1,1)),r.add(a);var o=this.newEdge(null);this.graphManager.add(o,n,a),t.add(a),n=a}return o=this.newEdge(null),this.graphManager.add(o,n,e.target),this.edgeToDummyNodes.set(e,t),e.isInterGraph()?this.graphManager.remove(e):r.remove(e),t},d.prototype.createBendpointsFromDummyNodes=function(){var e=[];e=e.concat(this.graphManager.getAllEdges()),e=[].concat(r(this.edgeToDummyNodes.keys())).concat(e);for(var t=0;t<e.length;t++){var n=e[t];if(n.bendpoints.length>0){for(var i=this.edgeToDummyNodes.get(n),a=0;a<i.length;a++){var o=i[a],s=new u(o.getCenterX(),o.getCenterY()),l=n.bendpoints.get(a);l.x=s.x,l.y=s.y,o.getOwner().remove(o)}this.graphManager.add(n,n.source,n.target)}}},d.transform=function(e,t,n,r){if(null!=n&&null!=r){var i=t;return e<=50?i-=(t-t/n)/50*(50-e):i+=(t*r-t)/50*(e-50),i}var a,o;return e<=50?(a=9*t/500,o=t/10):(a=9*t/50,o=-8*t),a*e+o},d.findCenterOfTree=function(e){var t=[];t=t.concat(e);var n=[],r=new Map,i=!1,a=null;1!=t.length&&2!=t.length||(i=!0,a=t[0]);for(var o=0;o<t.length;o++){var s=(c=t[o]).getNeighborsList().size;r.set(c,c.getNeighborsList().size),1==s&&n.push(c)}var l=[];for(l=l.concat(n);!i;){var u=[];for(u=u.concat(l),l=[],o=0;o<t.length;o++){var c=t[o],h=t.indexOf(c);h>=0&&t.splice(h,1),c.getNeighborsList().forEach((function(e){if(n.indexOf(e)<0){var t=r.get(e)-1;1==t&&l.push(e),r.set(e,t)}}))}n=n.concat(l),1!=t.length&&2!=t.length||(i=!0,a=t[0])}return a},d.prototype.setGraphManager=function(e){this.graphManager=e},e.exports=d},function(e,t,n){"use strict";function r(){}r.seed=1,r.x=0,r.nextDouble=function(){return r.x=1e4*Math.sin(r.seed++),r.x-Math.floor(r.x)},e.exports=r},function(e,t,n){"use strict";var r=n(4);function i(e,t){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}i.prototype.getWorldOrgX=function(){return this.lworldOrgX},i.prototype.setWorldOrgX=function(e){this.lworldOrgX=e},i.prototype.getWorldOrgY=function(){return this.lworldOrgY},i.prototype.setWorldOrgY=function(e){this.lworldOrgY=e},i.prototype.getWorldExtX=function(){return this.lworldExtX},i.prototype.setWorldExtX=function(e){this.lworldExtX=e},i.prototype.getWorldExtY=function(){return this.lworldExtY},i.prototype.setWorldExtY=function(e){this.lworldExtY=e},i.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},i.prototype.setDeviceOrgX=function(e){this.ldeviceOrgX=e},i.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},i.prototype.setDeviceOrgY=function(e){this.ldeviceOrgY=e},i.prototype.getDeviceExtX=function(){return this.ldeviceExtX},i.prototype.setDeviceExtX=function(e){this.ldeviceExtX=e},i.prototype.getDeviceExtY=function(){return this.ldeviceExtY},i.prototype.setDeviceExtY=function(e){this.ldeviceExtY=e},i.prototype.transformX=function(e){var t=0,n=this.lworldExtX;return 0!=n&&(t=this.ldeviceOrgX+(e-this.lworldOrgX)*this.ldeviceExtX/n),t},i.prototype.transformY=function(e){var t=0,n=this.lworldExtY;return 0!=n&&(t=this.ldeviceOrgY+(e-this.lworldOrgY)*this.ldeviceExtY/n),t},i.prototype.inverseTransformX=function(e){var t=0,n=this.ldeviceExtX;return 0!=n&&(t=this.lworldOrgX+(e-this.ldeviceOrgX)*this.lworldExtX/n),t},i.prototype.inverseTransformY=function(e){var t=0,n=this.ldeviceExtY;return 0!=n&&(t=this.lworldOrgY+(e-this.ldeviceOrgY)*this.lworldExtY/n),t},i.prototype.inverseTransformPoint=function(e){return new r(this.inverseTransformX(e.x),this.inverseTransformY(e.y))},e.exports=i},function(e,t,n){"use strict";var r=n(15),i=n(7),a=n(0),o=n(8),s=n(9);function l(){r.call(this),this.useSmartIdealEdgeLengthCalculation=i.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.idealEdgeLength=i.DEFAULT_EDGE_LENGTH,this.springConstant=i.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=i.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=i.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=i.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=i.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=i.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*i.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=i.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=i.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=i.MAX_ITERATIONS}for(var u in l.prototype=Object.create(r.prototype),r)l[u]=r[u];l.prototype.initParameters=function(){r.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=i.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},l.prototype.calcIdealEdgeLengths=function(){for(var e,t,n,r,o,s,l=this.getGraphManager().getAllEdges(),u=0;u<l.length;u++)(e=l[u]).idealLength=this.idealEdgeLength,e.isInterGraph&&(n=e.getSource(),r=e.getTarget(),o=e.getSourceInLca().getEstimatedSize(),s=e.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(e.idealLength+=o+s-2*a.SIMPLE_NODE_SIZE),t=e.getLca().getInclusionTreeDepth(),e.idealLength+=i.DEFAULT_EDGE_LENGTH*i.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(n.getInclusionTreeDepth()+r.getInclusionTreeDepth()-2*t))},l.prototype.initSpringEmbedder=function(){var e=this.getAllNodes().length;this.incremental?(e>i.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*i.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(e-i.ADAPTATION_LOWER_NODE_LIMIT)/(i.ADAPTATION_UPPER_NODE_LIMIT-i.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-i.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=i.MAX_NODE_DISPLACEMENT_INCREMENTAL):(e>i.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(i.COOLING_ADAPTATION_FACTOR,1-(e-i.ADAPTATION_LOWER_NODE_LIMIT)/(i.ADAPTATION_UPPER_NODE_LIMIT-i.ADAPTATION_LOWER_NODE_LIMIT)*(1-i.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=i.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(5*this.getAllNodes().length,this.maxIterations),this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},l.prototype.calcSpringForces=function(){for(var e,t=this.getAllEdges(),n=0;n<t.length;n++)e=t[n],this.calcSpringForce(e,e.idealLength)},l.prototype.calcRepulsionForces=function(){var e,t,n,r,a,o=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],s=arguments.length>1&&void 0!==arguments[1]&&arguments[1],l=this.getAllNodes();if(this.useFRGridVariant)for(this.totalIterations%i.GRID_CALCULATION_CHECK_PERIOD==1&&o&&this.updateGrid(),a=new Set,e=0;e<l.length;e++)n=l[e],this.calculateRepulsionForceOfANode(n,a,o,s),a.add(n);else for(e=0;e<l.length;e++)for(n=l[e],t=e+1;t<l.length;t++)r=l[t],n.getOwner()==r.getOwner()&&this.calcRepulsionForce(n,r)},l.prototype.calcGravitationalForces=function(){for(var e,t=this.getAllNodesToApplyGravitation(),n=0;n<t.length;n++)e=t[n],this.calcGravitationalForce(e)},l.prototype.moveNodes=function(){for(var e=this.getAllNodes(),t=0;t<e.length;t++)e[t].move()},l.prototype.calcSpringForce=function(e,t){var n,r,i,a,o=e.getSource(),s=e.getTarget();if(this.uniformLeafNodeSizes&&null==o.getChild()&&null==s.getChild())e.updateLengthSimple();else if(e.updateLength(),e.isOverlapingSourceAndTarget)return;0!=(n=e.getLength())&&(i=(r=this.springConstant*(n-t))*(e.lengthX/n),a=r*(e.lengthY/n),o.springForceX+=i,o.springForceY+=a,s.springForceX-=i,s.springForceY-=a)},l.prototype.calcRepulsionForce=function(e,t){var n,r,a,l,u,c,h,d=e.getRect(),p=t.getRect(),g=new Array(2),f=new Array(4);if(d.intersects(p)){o.calcSeparationAmount(d,p,g,i.DEFAULT_EDGE_LENGTH/2),c=2*g[0],h=2*g[1];var v=e.noOfChildren*t.noOfChildren/(e.noOfChildren+t.noOfChildren);e.repulsionForceX-=v*c,e.repulsionForceY-=v*h,t.repulsionForceX+=v*c,t.repulsionForceY+=v*h}else this.uniformLeafNodeSizes&&null==e.getChild()&&null==t.getChild()?(n=p.getCenterX()-d.getCenterX(),r=p.getCenterY()-d.getCenterY()):(o.getIntersection(d,p,f),n=f[2]-f[0],r=f[3]-f[1]),Math.abs(n)<i.MIN_REPULSION_DIST&&(n=s.sign(n)*i.MIN_REPULSION_DIST),Math.abs(r)<i.MIN_REPULSION_DIST&&(r=s.sign(r)*i.MIN_REPULSION_DIST),a=n*n+r*r,l=Math.sqrt(a),c=(u=this.repulsionConstant*e.noOfChildren*t.noOfChildren/a)*n/l,h=u*r/l,e.repulsionForceX-=c,e.repulsionForceY-=h,t.repulsionForceX+=c,t.repulsionForceY+=h},l.prototype.calcGravitationalForce=function(e){var t,n,r,i,a,o,s,l;n=((t=e.getOwner()).getRight()+t.getLeft())/2,r=(t.getTop()+t.getBottom())/2,i=e.getCenterX()-n,a=e.getCenterY()-r,o=Math.abs(i)+e.getWidth()/2,s=Math.abs(a)+e.getHeight()/2,e.getOwner()==this.graphManager.getRoot()?(o>(l=t.getEstimatedSize()*this.gravityRangeFactor)||s>l)&&(e.gravitationForceX=-this.gravityConstant*i,e.gravitationForceY=-this.gravityConstant*a):(o>(l=t.getEstimatedSize()*this.compoundGravityRangeFactor)||s>l)&&(e.gravitationForceX=-this.gravityConstant*i*this.compoundGravityConstant,e.gravitationForceY=-this.gravityConstant*a*this.compoundGravityConstant)},l.prototype.isConverged=function(){var e,t=!1;return this.totalIterations>this.maxIterations/3&&(t=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),e=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,e||t},l.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},l.prototype.calcNoOfChildrenForAllNodes=function(){for(var e,t=this.graphManager.getAllNodes(),n=0;n<t.length;n++)(e=t[n]).noOfChildren=e.getNoOfChildren()},l.prototype.calcGrid=function(e){var t,n;t=parseInt(Math.ceil((e.getRight()-e.getLeft())/this.repulsionRange)),n=parseInt(Math.ceil((e.getBottom()-e.getTop())/this.repulsionRange));for(var r=new Array(t),i=0;i<t;i++)r[i]=new Array(n);for(i=0;i<t;i++)for(var a=0;a<n;a++)r[i][a]=new Array;return r},l.prototype.addNodeToGrid=function(e,t,n){var r,i,a,o;r=parseInt(Math.floor((e.getRect().x-t)/this.repulsionRange)),i=parseInt(Math.floor((e.getRect().width+e.getRect().x-t)/this.repulsionRange)),a=parseInt(Math.floor((e.getRect().y-n)/this.repulsionRange)),o=parseInt(Math.floor((e.getRect().height+e.getRect().y-n)/this.repulsionRange));for(var s=r;s<=i;s++)for(var l=a;l<=o;l++)this.grid[s][l].push(e),e.setGridCoordinates(r,i,a,o)},l.prototype.updateGrid=function(){var e,t,n=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),e=0;e<n.length;e++)t=n[e],this.addNodeToGrid(t,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},l.prototype.calculateRepulsionForceOfANode=function(e,t,n,r){if(this.totalIterations%i.GRID_CALCULATION_CHECK_PERIOD==1&&n||r){var a,o=new Set;e.surrounding=new Array;for(var s=this.grid,l=e.startX-1;l<e.finishX+2;l++)for(var u=e.startY-1;u<e.finishY+2;u++)if(!(l<0||u<0||l>=s.length||u>=s[0].length))for(var c=0;c<s[l][u].length;c++)if(a=s[l][u][c],e.getOwner()==a.getOwner()&&e!=a&&!t.has(a)&&!o.has(a)){var h=Math.abs(e.getCenterX()-a.getCenterX())-(e.getWidth()/2+a.getWidth()/2),d=Math.abs(e.getCenterY()-a.getCenterY())-(e.getHeight()/2+a.getHeight()/2);h<=this.repulsionRange&&d<=this.repulsionRange&&o.add(a)}e.surrounding=[].concat(function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}(o))}for(l=0;l<e.surrounding.length;l++)this.calcRepulsionForce(e,e.surrounding[l])},l.prototype.calcRepulsionRange=function(){return 0},e.exports=l},function(e,t,n){"use strict";var r=n(1),i=n(7);function a(e,t,n){r.call(this,e,t,n),this.idealLength=i.DEFAULT_EDGE_LENGTH}for(var o in a.prototype=Object.create(r.prototype),r)a[o]=r[o];e.exports=a},function(e,t,n){"use strict";var r=n(3);function i(e,t,n,i){r.call(this,e,t,n,i),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}for(var a in i.prototype=Object.create(r.prototype),r)i[a]=r[a];i.prototype.setGridCoordinates=function(e,t,n,r){this.startX=e,this.finishX=t,this.startY=n,this.finishY=r},e.exports=i},function(e,t,n){"use strict";function r(e,t){this.width=0,this.height=0,null!==e&&null!==t&&(this.height=t,this.width=e)}r.prototype.getWidth=function(){return this.width},r.prototype.setWidth=function(e){this.width=e},r.prototype.getHeight=function(){return this.height},r.prototype.setHeight=function(e){this.height=e},e.exports=r},function(e,t,n){"use strict";var r=n(14);function i(){this.map={},this.keys=[]}i.prototype.put=function(e,t){var n=r.createID(e);this.contains(n)||(this.map[n]=t,this.keys.push(e))},i.prototype.contains=function(e){return r.createID(e),null!=this.map[e]},i.prototype.get=function(e){var t=r.createID(e);return this.map[t]},i.prototype.keySet=function(){return this.keys},e.exports=i},function(e,t,n){"use strict";var r=n(14);function i(){this.set={}}i.prototype.add=function(e){var t=r.createID(e);this.contains(t)||(this.set[t]=e)},i.prototype.remove=function(e){delete this.set[r.createID(e)]},i.prototype.clear=function(){this.set={}},i.prototype.contains=function(e){return this.set[r.createID(e)]==e},i.prototype.isEmpty=function(){return 0===this.size()},i.prototype.size=function(){return Object.keys(this.set).length},i.prototype.addAllTo=function(e){for(var t=Object.keys(this.set),n=t.length,r=0;r<n;r++)e.push(this.set[t[r]])},i.prototype.size=function(){return Object.keys(this.set).length},i.prototype.addAll=function(e){for(var t=e.length,n=0;n<t;n++){var r=e[n];this.add(r)}},e.exports=i},function(e,t,n){"use strict";var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(11),a=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),null===n&&void 0===n||(this.compareFunction=this._defaultCompareFunction);var r;r=t instanceof i?t.size():t.length,this._quicksort(t,0,r-1)}return r(e,[{key:"_quicksort",value:function(e,t,n){if(t<n){var r=this._partition(e,t,n);this._quicksort(e,t,r),this._quicksort(e,r+1,n)}}},{key:"_partition",value:function(e,t,n){for(var r=this._get(e,t),i=t,a=n;;){for(;this.compareFunction(r,this._get(e,a));)a--;for(;this.compareFunction(this._get(e,i),r);)i++;if(!(i<a))return a;this._swap(e,i,a),i++,a--}}},{key:"_get",value:function(e,t){return e instanceof i?e.get_object_at(t):e[t]}},{key:"_set",value:function(e,t,n){e instanceof i?e.set_object_at(t,n):e[t]=n}},{key:"_swap",value:function(e,t,n){var r=this._get(e,t);this._set(e,t,this._get(e,n)),this._set(e,n,r)}},{key:"_defaultCompareFunction",value:function(e,t){return t>e}}]),e}();e.exports=a},function(e,t,n){"use strict";var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=function(){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:-1,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.sequence1=t,this.sequence2=n,this.match_score=r,this.mismatch_penalty=i,this.gap_penalty=a,this.iMax=t.length+1,this.jMax=n.length+1,this.grid=new Array(this.iMax);for(var o=0;o<this.iMax;o++){this.grid[o]=new Array(this.jMax);for(var s=0;s<this.jMax;s++)this.grid[o][s]=0}this.tracebackGrid=new Array(this.iMax);for(var l=0;l<this.iMax;l++){this.tracebackGrid[l]=new Array(this.jMax);for(var u=0;u<this.jMax;u++)this.tracebackGrid[l][u]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return r(e,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var e=1;e<this.jMax;e++)this.grid[0][e]=this.grid[0][e-1]+this.gap_penalty,this.tracebackGrid[0][e]=[!1,!1,!0];for(var t=1;t<this.iMax;t++)this.grid[t][0]=this.grid[t-1][0]+this.gap_penalty,this.tracebackGrid[t][0]=[!1,!0,!1];for(var n=1;n<this.iMax;n++)for(var r=1;r<this.jMax;r++){var i=[this.sequence1[n-1]===this.sequence2[r-1]?this.grid[n-1][r-1]+this.match_score:this.grid[n-1][r-1]+this.mismatch_penalty,this.grid[n-1][r]+this.gap_penalty,this.grid[n][r-1]+this.gap_penalty],a=this.arrayAllMaxIndexes(i);this.grid[n][r]=i[a[0]],this.tracebackGrid[n][r]=[a.includes(0),a.includes(1),a.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var e=[];for(e.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});e[0];){var t=e[0],n=this.tracebackGrid[t.pos[0]][t.pos[1]];n[0]&&e.push({pos:[t.pos[0]-1,t.pos[1]-1],seq1:this.sequence1[t.pos[0]-1]+t.seq1,seq2:this.sequence2[t.pos[1]-1]+t.seq2}),n[1]&&e.push({pos:[t.pos[0]-1,t.pos[1]],seq1:this.sequence1[t.pos[0]-1]+t.seq1,seq2:"-"+t.seq2}),n[2]&&e.push({pos:[t.pos[0],t.pos[1]-1],seq1:"-"+t.seq1,seq2:this.sequence2[t.pos[1]-1]+t.seq2}),0===t.pos[0]&&0===t.pos[1]&&this.alignments.push({sequence1:t.seq1,sequence2:t.seq2}),e.shift()}return this.alignments}},{key:"getAllIndexes",value:function(e,t){for(var n=[],r=-1;-1!==(r=e.indexOf(t,r+1));)n.push(r);return n}},{key:"arrayAllMaxIndexes",value:function(e){return this.getAllIndexes(e,Math.max.apply(null,e))}}]),e}();e.exports=i},function(e,t,n){"use strict";var r=function(){};r.FDLayout=n(18),r.FDLayoutConstants=n(7),r.FDLayoutEdge=n(19),r.FDLayoutNode=n(20),r.DimensionD=n(21),r.HashMap=n(22),r.HashSet=n(23),r.IGeometry=n(8),r.IMath=n(9),r.Integer=n(10),r.Point=n(12),r.PointD=n(4),r.RandomSeed=n(16),r.RectangleD=n(13),r.Transform=n(17),r.UniqueIDGeneretor=n(14),r.Quicksort=n(24),r.LinkedList=n(11),r.LGraphObject=n(2),r.LGraph=n(5),r.LEdge=n(1),r.LGraphManager=n(6),r.LNode=n(3),r.Layout=n(15),r.LayoutConstants=n(0),r.NeedlemanWunsch=n(25),e.exports=r},function(e,t,n){"use strict";function r(){this.listeners=[]}var i=r.prototype;i.addListener=function(e,t){this.listeners.push({event:e,callback:t})},i.removeListener=function(e,t){for(var n=this.listeners.length;n>=0;n--){var r=this.listeners[n];r.event===e&&r.callback===t&&this.listeners.splice(n,1)}},i.emit=function(e,t){for(var n=0;n<this.listeners.length;n++){var r=this.listeners[n];e===r.event&&r.callback(t)}},e.exports=r}])},e.exports=t()},9968:function(e,t,n){"use strict";n.d(t,{diagram:function(){return C}});var r=n(5103),i=n(5740),a=n(6105),o=n(1377),s=n(4607),l=n(6500),u=n(2281),c=n(7201),h=(n(7484),n(7967),n(7856),function(){var e=function(e,t,n,r){for(n=n||{},r=e.length;r--;n[e[r]]=t);return n},t=[1,4],n=[1,13],r=[1,12],i=[1,15],a=[1,16],o=[1,20],s=[1,19],l=[6,7,8],u=[1,26],c=[1,24],h=[1,25],d=[6,7,11],p=[1,6,13,15,16,19,22],g=[1,33],f=[1,34],v=[1,6,7,11,13,15,16,19,22],y={trace:function(){},yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,MINDMAP:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,ICON:15,CLASS:16,nodeWithId:17,nodeWithoutId:18,NODE_DSTART:19,NODE_DESCR:20,NODE_DEND:21,NODE_ID:22,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"MINDMAP",11:"EOF",13:"SPACELIST",15:"ICON",16:"CLASS",19:"NODE_DSTART",20:"NODE_DESCR",21:"NODE_DEND",22:"NODE_ID"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],performAction:function(e,t,n,r,i,a,o){var s=a.length-1;switch(i){case 6:case 7:return r;case 8:r.getLogger().trace("Stop NL ");break;case 9:r.getLogger().trace("Stop EOF ");break;case 11:r.getLogger().trace("Stop NL2 ");break;case 12:r.getLogger().trace("Stop EOF2 ");break;case 15:r.getLogger().info("Node: ",a[s].id),r.addNode(a[s-1].length,a[s].id,a[s].descr,a[s].type);break;case 16:r.getLogger().trace("Icon: ",a[s]),r.decorateNode({icon:a[s]});break;case 17:case 21:r.decorateNode({class:a[s]});break;case 18:r.getLogger().trace("SPACELIST");break;case 19:r.getLogger().trace("Node: ",a[s].id),r.addNode(0,a[s].id,a[s].descr,a[s].type);break;case 20:r.decorateNode({icon:a[s]});break;case 25:r.getLogger().trace("node found ..",a[s-2]),this.$={id:a[s-1],descr:a[s-1],type:r.getType(a[s-2],a[s])};break;case 26:this.$={id:a[s],descr:a[s],type:r.nodeType.DEFAULT};break;case 27:r.getLogger().trace("node found ..",a[s-3]),this.$={id:a[s-3],descr:a[s-1],type:r.getType(a[s-2],a[s])}}},table:[{3:1,4:2,5:3,6:[1,5],8:t},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:t},{6:n,7:[1,10],9:9,12:11,13:r,14:14,15:i,16:a,17:17,18:18,19:o,22:s},e(l,[2,3]),{1:[2,2]},e(l,[2,4]),e(l,[2,5]),{1:[2,6],6:n,12:21,13:r,14:14,15:i,16:a,17:17,18:18,19:o,22:s},{6:n,9:22,12:11,13:r,14:14,15:i,16:a,17:17,18:18,19:o,22:s},{6:u,7:c,10:23,11:h},e(d,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:o,22:s}),e(d,[2,18]),e(d,[2,19]),e(d,[2,20]),e(d,[2,21]),e(d,[2,23]),e(d,[2,24]),e(d,[2,26],{19:[1,30]}),{20:[1,31]},{6:u,7:c,10:32,11:h},{1:[2,7],6:n,12:21,13:r,14:14,15:i,16:a,17:17,18:18,19:o,22:s},e(p,[2,14],{7:g,11:f}),e(v,[2,8]),e(v,[2,9]),e(v,[2,10]),e(d,[2,15]),e(d,[2,16]),e(d,[2,17]),{20:[1,35]},{21:[1,36]},e(p,[2,13],{7:g,11:f}),e(v,[2,11]),e(v,[2,12]),{21:[1,37]},e(d,[2,25]),e(d,[2,27])],defaultActions:{2:[2,1],6:[2,2]},parseError:function(e,t){if(!t.recoverable){var n=new Error(e);throw n.hash=t,n}this.trace(e)},parse:function(e){var t=[0],n=[],r=[null],i=[],a=this.table,o="",s=0,l=0,u=i.slice.call(arguments,1),c=Object.create(this.lexer),h={yy:{}};for(var d in this.yy)Object.prototype.hasOwnProperty.call(this.yy,d)&&(h.yy[d]=this.yy[d]);c.setInput(e,h.yy),h.yy.lexer=c,h.yy.parser=this,void 0===c.yylloc&&(c.yylloc={});var p=c.yylloc;i.push(p);var g=c.options&&c.options.ranges;"function"==typeof h.yy.parseError?this.parseError=h.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;for(var f,v,y,m,b,x,w,E,_,T={};;){if(v=t[t.length-1],this.defaultActions[v]?y=this.defaultActions[v]:(null==f&&(_=void 0,"number"!=typeof(_=n.pop()||c.lex()||1)&&(_ instanceof Array&&(_=(n=_).pop()),_=this.symbols_[_]||_),f=_),y=a[v]&&a[v][f]),void 0===y||!y.length||!y[0]){var D;for(b in E=[],a[v])this.terminals_[b]&&b>2&&E.push("'"+this.terminals_[b]+"'");D=c.showPosition?"Parse error on line "+(s+1)+":\n"+c.showPosition()+"\nExpecting "+E.join(", ")+", got '"+(this.terminals_[f]||f)+"'":"Parse error on line "+(s+1)+": Unexpected "+(1==f?"end of input":"'"+(this.terminals_[f]||f)+"'"),this.parseError(D,{text:c.match,token:this.terminals_[f]||f,line:c.yylineno,loc:p,expected:E})}if(y[0]instanceof Array&&y.length>1)throw new Error("Parse Error: multiple actions possible at state: "+v+", token: "+f);switch(y[0]){case 1:t.push(f),r.push(c.yytext),i.push(c.yylloc),t.push(y[1]),f=null,l=c.yyleng,o=c.yytext,s=c.yylineno,p=c.yylloc;break;case 2:if(x=this.productions_[y[1]][1],T.$=r[r.length-x],T._$={first_line:i[i.length-(x||1)].first_line,last_line:i[i.length-1].last_line,first_column:i[i.length-(x||1)].first_column,last_column:i[i.length-1].last_column},g&&(T._$.range=[i[i.length-(x||1)].range[0],i[i.length-1].range[1]]),void 0!==(m=this.performAction.apply(T,[o,l,s,h.yy,y[1],r,i].concat(u))))return m;x&&(t=t.slice(0,-1*x*2),r=r.slice(0,-1*x),i=i.slice(0,-1*x)),t.push(this.productions_[y[1]][0]),r.push(T.$),i.push(T._$),w=a[t[t.length-2]][t[t.length-1]],t.push(w);break;case 3:return!0}}return!0}},m={EOF:1,parseError:function(e,t){if(!this.yy.parser)throw new Error(e);this.yy.parser.parseError(e,t)},setInput:function(e,t){return this.yy=t||this.yy||{},this._input=e,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var e=this._input[0];return this.yytext+=e,this.yyleng++,this.offset++,this.match+=e,this.matched+=e,e.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),e},unput:function(e){var t=e.length,n=e.split(/(?:\r\n?|\n)/g);this._input=e+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-t),this.offset-=t;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),n.length-1&&(this.yylineno-=n.length-1);var i=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:n?(n.length===r.length?this.yylloc.first_column:0)+r[r.length-n.length].length-n[0].length:this.yylloc.first_column-t},this.options.ranges&&(this.yylloc.range=[i[0],i[0]+this.yyleng-t]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},less:function(e){this.unput(this.match.slice(e))},pastInput:function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(e.length>20?"...":"")+e.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var e=this.match;return e.length<20&&(e+=this._input.substr(0,20-e.length)),(e.substr(0,20)+(e.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var e=this.pastInput(),t=new Array(e.length+1).join("-");return e+this.upcomingInput()+"\n"+t+"^"},test_match:function(e,t){var n,r,i;if(this.options.backtrack_lexer&&(i={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(i.yylloc.range=this.yylloc.range.slice(0))),(r=e[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length},this.yytext+=e[0],this.match+=e[0],this.matches=e,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(e[0].length),this.matched+=e[0],n=this.performAction.call(this,this.yy,this,t,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),n)return n;if(this._backtrack){for(var a in i)this[a]=i[a];return!1}return!1},next:function(){if(this.done)return this.EOF;var e,t,n,r;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var i=this._currentRules(),a=0;a<i.length;a++)if((n=this._input.match(this.rules[i[a]]))&&(!t||n[0].length>t[0].length)){if(t=n,r=a,this.options.backtrack_lexer){if(!1!==(e=this.test_match(n,i[a])))return e;if(this._backtrack){t=!1;continue}return!1}if(!this.options.flex)break}return t?!1!==(e=this.test_match(t,i[r]))&&e:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){return this.next()||this.lex()},begin:function(e){this.conditionStack.push(e)},popState:function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(e){return(e=this.conditionStack.length-1-Math.abs(e||0))>=0?this.conditionStack[e]:"INITIAL"},pushState:function(e){this.begin(e)},stateStackSize:function(){return this.conditionStack.length},options:{"case-insensitive":!0},performAction:function(e,t,n,r){switch(n){case 0:return e.getLogger().trace("Found comment",t.yytext),6;case 1:return 8;case 2:this.begin("CLASS");break;case 3:return this.popState(),16;case 4:case 23:case 26:this.popState();break;case 5:e.getLogger().trace("Begin icon"),this.begin("ICON");break;case 6:return e.getLogger().trace("SPACELINE"),6;case 7:return 7;case 8:return 15;case 9:e.getLogger().trace("end icon"),this.popState();break;case 10:return e.getLogger().trace("Exploding node"),this.begin("NODE"),19;case 11:return e.getLogger().trace("Cloud"),this.begin("NODE"),19;case 12:return e.getLogger().trace("Explosion Bang"),this.begin("NODE"),19;case 13:return e.getLogger().trace("Cloud Bang"),this.begin("NODE"),19;case 14:case 15:case 16:case 17:return this.begin("NODE"),19;case 18:return 13;case 19:return 22;case 20:return 11;case 21:this.begin("NSTR2");break;case 22:return"NODE_DESCR";case 24:e.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 25:return e.getLogger().trace("description:",t.yytext),"NODE_DESCR";case 27:return this.popState(),e.getLogger().trace("node end ))"),"NODE_DEND";case 28:return this.popState(),e.getLogger().trace("node end )"),"NODE_DEND";case 29:return this.popState(),e.getLogger().trace("node end ...",t.yytext),"NODE_DEND";case 30:case 33:case 34:return this.popState(),e.getLogger().trace("node end (("),"NODE_DEND";case 31:case 32:return this.popState(),e.getLogger().trace("node end (-"),"NODE_DEND";case 35:case 36:return e.getLogger().trace("Long description:",t.yytext),20}},rules:[/^(?:\s*%%.*)/i,/^(?:mindmap\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\-\)\{\}]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{CLASS:{rules:[3,4],inclusive:!1},ICON:{rules:[8,9],inclusive:!1},NSTR2:{rules:[22,23],inclusive:!1},NSTR:{rules:[25,26],inclusive:!1},NODE:{rules:[21,24,27,28,29,30,31,32,33,34,35,36],inclusive:!1},INITIAL:{rules:[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],inclusive:!0}}};function b(){this.yy={}}return y.lexer=m,b.prototype=y,y.Parser=b,new b}());h.parser=h;const d=h,p=e=>(0,r.d)(e,(0,r.c)());let g=[],f=0,v={};const y={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},m=(e,t)=>{v[e]=t},b=e=>{switch(e){case y.DEFAULT:return"no-border";case y.RECT:return"rect";case y.ROUNDED_RECT:return"rounded-rect";case y.CIRCLE:return"circle";case y.CLOUD:return"cloud";case y.BANG:return"bang";case y.HEXAGON:return"hexgon";default:return"no-border"}};let x;const w=e=>v[e],E=Object.freeze(Object.defineProperty({__proto__:null,addNode:(e,t,n,i)=>{r.l.info("addNode",e,t,n,i);const a=(0,r.c)(),o={id:f++,nodeId:p(t),level:e,descr:p(n),type:i,children:[],width:(0,r.c)().mindmap.maxNodeWidth};switch(o.type){case y.ROUNDED_RECT:case y.RECT:case y.HEXAGON:o.padding=2*a.mindmap.padding;break;default:o.padding=a.mindmap.padding}const s=function(e){for(let t=g.length-1;t>=0;t--)if(g[t].level<e)return g[t];return null}(e);if(s)s.children.push(o),g.push(o);else{if(0!==g.length){let e=new Error('There can be only one root. No parent could be found for ("'+o.descr+'")');throw e.hash={text:"branch "+name,token:"branch "+name,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:['"checkout '+name+'"']},e}g.push(o)}},clear:()=>{g=[],f=0,v={}},decorateNode:e=>{const t=g[g.length-1];e&&e.icon&&(t.icon=p(e.icon)),e&&e.class&&(t.class=p(e.class))},getElementById:w,getLogger:()=>r.l,getMindmap:()=>g.length>0?g[0]:null,getNodeById:e=>g[e],getType:(e,t)=>{switch(r.l.debug("In get type",e,t),e){case"[":return y.RECT;case"(":return")"===t?y.ROUNDED_RECT:y.CLOUD;case"((":return y.CIRCLE;case")":return y.CLOUD;case"))":return y.BANG;case"{{":return y.HEXAGON;default:return y.DEFAULT}},nodeType:y,get parseError(){return x},sanitizeText:p,setElementForId:m,setErrorHandler:e=>{x=e},type2Str:b},Symbol.toStringTag,{value:"Module"}));function _(e,t,n,r){(function(e,t,n,r){const i=r.htmlLabels,o=n%11,s=e.append("g");t.section=o;let l="section-"+o;o<0&&(l+=" section-root"),s.attr("class",(t.class?t.class+" ":"")+"mindmap-node "+l);const u=s.append("g"),c=s.append("g"),h=t.descr.replace(/(<br\/*>)/g,"\n");(0,a.c)(c,h,{useHtmlLabels:i,width:t.width,classes:"mindmap-node-label"}),i||c.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");const d=c.node().getBBox(),p=r.fontSize.replace?r.fontSize.replace("px",""):r.fontSize;if(t.height=d.height*****p*.5+t.padding,t.width=d.width+2*t.padding,t.icon)if(t.type===y.CIRCLE)t.height+=50,t.width+=50,s.append("foreignObject").attr("height","50px").attr("width",t.width).attr("style","text-align: center;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+o+" "+t.icon),c.attr("transform","translate("+t.width/2+", "+(t.height/2-1.5*t.padding)+")");else{t.width+=50;const e=t.height;t.height=Math.max(e,60);const n=Math.abs(t.height-e);s.append("foreignObject").attr("width","60px").attr("height",t.height).attr("style","text-align: center;margin-top:"+n/2+"px;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+o+" "+t.icon),c.attr("transform","translate("+(25+t.width/2)+", "+(n/2+t.padding/2)+")")}else if(i){const e=(t.width-d.width)/2,n=(t.height-d.height)/2;c.attr("transform","translate("+e+", "+n+")")}else{const e=t.width/2,n=t.padding/2;c.attr("transform","translate("+e+", "+n+")")}switch(t.type){case y.DEFAULT:!function(e,t,n){e.append("path").attr("id","node-"+t.id).attr("class","node-bkg node-"+b(t.type)).attr("d",`M0 ${t.height-5} v${10-t.height} q0,-5 5,-5 h${t.width-10} q5,0 5,5 v${t.height-5} H0 Z`),e.append("line").attr("class","node-line-"+n).attr("x1",0).attr("y1",t.height).attr("x2",t.width).attr("y2",t.height)}(u,t,o);break;case y.ROUNDED_RECT:!function(e,t){e.append("rect").attr("id","node-"+t.id).attr("class","node-bkg node-"+b(t.type)).attr("height",t.height).attr("rx",t.padding).attr("ry",t.padding).attr("width",t.width)}(u,t);break;case y.RECT:!function(e,t){e.append("rect").attr("id","node-"+t.id).attr("class","node-bkg node-"+b(t.type)).attr("height",t.height).attr("width",t.width)}(u,t);break;case y.CIRCLE:u.attr("transform","translate("+t.width/2+", "+ +t.height/2+")"),function(e,t){e.append("circle").attr("id","node-"+t.id).attr("class","node-bkg node-"+b(t.type)).attr("r",t.width/2)}(u,t);break;case y.CLOUD:!function(e,t){const n=t.width,r=t.height,i=.15*n,a=.25*n,o=.35*n,s=.2*n;e.append("path").attr("id","node-"+t.id).attr("class","node-bkg node-"+b(t.type)).attr("d",`M0 0 a${i},${i} 0 0,1 ${.25*n},${-1*n*.1}\n      a${o},${o} 1 0,1 ${.4*n},${-1*n*.1}\n      a${a},${a} 1 0,1 ${.35*n},${1*n*.2}\n\n      a${i},${i} 1 0,1 ${.15*n},${1*r*.35}\n      a${s},${s} 1 0,1 ${-1*n*.15},${1*r*.65}\n\n      a${a},${i} 1 0,1 ${-1*n*.25},${.15*n}\n      a${o},${o} 1 0,1 ${-1*n*.5},0\n      a${i},${i} 1 0,1 ${-1*n*.25},${-1*n*.15}\n\n      a${i},${i} 1 0,1 ${-1*n*.1},${-1*r*.35}\n      a${s},${s} 1 0,1 ${.1*n},${-1*r*.65}\n\n    H0 V0 Z`)}(u,t);break;case y.BANG:!function(e,t){const n=t.width,r=t.height,i=.15*n;e.append("path").attr("id","node-"+t.id).attr("class","node-bkg node-"+b(t.type)).attr("d",`M0 0 a${i},${i} 1 0,0 ${.25*n},${-1*r*.1}\n      a${i},${i} 1 0,0 ${.25*n},0\n      a${i},${i} 1 0,0 ${.25*n},0\n      a${i},${i} 1 0,0 ${.25*n},${1*r*.1}\n\n      a${i},${i} 1 0,0 ${.15*n},${1*r*.33}\n      a${.8*i},${.8*i} 1 0,0 0,${1*r*.34}\n      a${i},${i} 1 0,0 ${-1*n*.15},${1*r*.33}\n\n      a${i},${i} 1 0,0 ${-1*n*.25},${.15*r}\n      a${i},${i} 1 0,0 ${-1*n*.25},0\n      a${i},${i} 1 0,0 ${-1*n*.25},0\n      a${i},${i} 1 0,0 ${-1*n*.25},${-1*r*.15}\n\n      a${i},${i} 1 0,0 ${-1*n*.1},${-1*r*.33}\n      a${.8*i},${.8*i} 1 0,0 0,${-1*r*.34}\n      a${i},${i} 1 0,0 ${.1*n},${-1*r*.33}\n\n    H0 V0 Z`)}(u,t);break;case y.HEXAGON:!function(e,t){const n=t.height,r=n/4,i=t.width-t.padding+2*r;!function(e,t,n,r,i){e.insert("polygon",":first-child").attr("points",r.map((function(e){return e.x+","+e.y})).join(" ")).attr("transform","translate("+(i.width-t)/2+", "+n+")")}(e,i,n,[{x:r,y:0},{x:i-r,y:0},{x:i,y:-n/2},{x:i-r,y:-n},{x:r,y:-n},{x:0,y:-n/2}],t)}(u,t)}m(t.id,s),t.height})(e,t,n,r),t.children&&t.children.forEach(((t,i)=>{_(e,t,n<0?i:n,r)}))}function T(e,t,n,r){t.add({group:"nodes",data:{id:e.id,labelText:e.descr,height:e.height,width:e.width,level:r,nodeId:e.id,padding:e.padding,type:e.type},position:{x:e.x,y:e.y}}),e.children&&e.children.forEach((i=>{T(i,t,n,r+1),t.add({group:"edges",data:{id:`${e.id}_${i.id}`,source:e.id,target:i.id,depth:r,section:i.section}})}))}function D(e,t){return new Promise((n=>{const a=(0,i.Ys)("body").append("div").attr("id","cy").attr("style","display:none"),s=o({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"bezier"}}]});a.remove(),T(e,s,t,0),s.nodes().forEach((function(e){e.layoutDimensions=()=>{const t=e.data();return{w:t.width,h:t.height}}})),s.layout({name:"cose-bilkent",quality:"proof",styleEnabled:!1,animate:!1}).run(),s.ready((e=>{r.l.info("Ready",e),n(s)}))}))}o.use(s);const C={db:E,renderer:{draw:async(e,t,n,a)=>{const o=(0,r.c)();o.htmlLabels=!1,a.db.clear(),a.parser.parse(e),r.l.debug("Rendering mindmap diagram\n"+e,a.parser);const s=(0,r.c)().securityLevel;let l;"sandbox"===s&&(l=(0,i.Ys)("#i"+t));const u=("sandbox"===s?(0,i.Ys)(l.nodes()[0].contentDocument.body):(0,i.Ys)("body")).select("#"+t);u.append("g");const c=a.db.getMindmap(),h=u.append("g");h.attr("class","mindmap-edges");const d=u.append("g");d.attr("class","mindmap-nodes"),_(d,c,-1,o);const p=await D(c,o);!function(e,t){t.edges().map(((t,n)=>{const i=t.data();if(t[0]._private.bodyBounds){const a=t[0]._private.rscratch;r.l.trace("Edge: ",n,i),e.insert("path").attr("d",`M ${a.startX},${a.startY} L ${a.midX},${a.midY} L${a.endX},${a.endY} `).attr("class","edge section-edge-"+i.section+" edge-depth-"+i.depth)}}))}(h,p),function(e){e.nodes().map(((e,t)=>{const n=e.data();n.x=e.position().x,n.y=e.position().y,function(e){const t=w(e.id),n=e.x||0,r=e.y||0;t.attr("transform","translate("+n+","+r+")")}(n);const i=w(n.nodeId);r.l.info("Id:",t,"Position: (",e.position().x,", ",e.position().y,")",n),i.attr("transform",`translate(${e.position().x-n.width/2}, ${e.position().y-n.height/2})`),i.attr("attr",`apa-${t})`)}))}(p),(0,r.p)(void 0,u,o.mindmap.padding,o.mindmap.useMaxWidth)}},parser:d,styles:e=>`\n  .edge {\n    stroke-width: 3;\n  }\n  ${(e=>{let t="";for(let t=0;t<e.THEME_COLOR_LIMIT;t++)e["lineColor"+t]=e["lineColor"+t]||e["cScaleInv"+t],(0,l.Z)(e["lineColor"+t])?e["lineColor"+t]=(0,u.Z)(e["lineColor"+t],20):e["lineColor"+t]=(0,c.Z)(e["lineColor"+t],20);for(let n=0;n<e.THEME_COLOR_LIMIT;n++){const r=""+(17-3*n);t+=`\n    .section-${n-1} rect, .section-${n-1} path, .section-${n-1} circle, .section-${n-1} polygon, .section-${n-1} path  {\n      fill: ${e["cScale"+n]};\n    }\n    .section-${n-1} text {\n     fill: ${e["cScaleLabel"+n]};\n    }\n    .node-icon-${n-1} {\n      font-size: 40px;\n      color: ${e["cScaleLabel"+n]};\n    }\n    .section-edge-${n-1}{\n      stroke: ${e["cScale"+n]};\n    }\n    .edge-depth-${n-1}{\n      stroke-width: ${r};\n    }\n    .section-${n-1} line {\n      stroke: ${e["cScaleInv"+n]} ;\n      stroke-width: 3;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `}return t})(e)}\n  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {\n    fill: ${e.git0};\n  }\n  .section-root text {\n    fill: ${e.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .mindmap-node-label {\n    dy: 1em;\n    alignment-baseline: middle;\n    text-anchor: middle;\n    dominant-baseline: middle;\n    text-align: center;\n  }\n`}}}]);