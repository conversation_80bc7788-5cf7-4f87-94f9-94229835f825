/*! For license information please see 637-3456bdaf.chunk.min.js.LICENSE.txt */
(self.webpackChunkgeekdoc=self.webpackChunkgeekdoc||[]).push([[637],{7967:function(t,e){"use strict";e.N=void 0;var i=/^([^\w]*)(javascript|data|vbscript)/im,r=/&#(\w+)(^\w|;)?/g,n=/&(newline|tab);/gi,o=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim,a=/^.+(:|&colon;)/gim,s=[".","/"];e.N=function(t){var e,l=(e=t||"",e.replace(r,(function(t,e){return String.fromCharCode(e)}))).replace(n,"").replace(o,"").trim();if(!l)return"about:blank";if(function(t){return s.indexOf(t[0])>-1}(l))return l;var h=l.match(a);if(!h)return l;var c=h[0];return i.test(c)?"about:blank":l}},7484:function(t){t.exports=function(){"use strict";var t=6e4,e=36e5,i="millisecond",r="second",n="minute",o="hour",a="day",s="week",l="month",h="quarter",c="year",u="date",d="Invalid Date",f=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,p=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,g={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(t){var e=["th","st","nd","rd"],i=t%100;return"["+t+(e[(i-20)%10]||e[i]||e[0])+"]"}},m=function(t,e,i){var r=String(t);return!r||r.length>=e?t:""+Array(e+1-r.length).join(i)+t},y={s:m,z:function(t){var e=-t.utcOffset(),i=Math.abs(e),r=Math.floor(i/60),n=i%60;return(e<=0?"+":"-")+m(r,2,"0")+":"+m(n,2,"0")},m:function t(e,i){if(e.date()<i.date())return-t(i,e);var r=12*(i.year()-e.year())+(i.month()-e.month()),n=e.clone().add(r,l),o=i-n<0,a=e.clone().add(r+(o?-1:1),l);return+(-(r+(i-n)/(o?n-a:a-n))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:l,y:c,w:s,d:a,D:u,h:o,m:n,s:r,ms:i,Q:h}[t]||String(t||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},_="en",b={};b[_]=g;var C=function(t){return t instanceof T},x=function t(e,i,r){var n;if(!e)return _;if("string"==typeof e){var o=e.toLowerCase();b[o]&&(n=o),i&&(b[o]=i,n=o);var a=e.split("-");if(!n&&a.length>1)return t(a[0])}else{var s=e.name;b[s]=e,n=s}return!r&&n&&(_=n),n||!r&&_},v=function(t,e){if(C(t))return t.clone();var i="object"==typeof e?e:{};return i.date=t,i.args=arguments,new T(i)},k=y;k.l=x,k.i=C,k.w=function(t,e){return v(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var T=function(){function g(t){this.$L=x(t.locale,null,!0),this.parse(t)}var m=g.prototype;return m.parse=function(t){this.$d=function(t){var e=t.date,i=t.utc;if(null===e)return new Date(NaN);if(k.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var r=e.match(f);if(r){var n=r[2]-1||0,o=(r[7]||"0").substring(0,3);return i?new Date(Date.UTC(r[1],n,r[3]||1,r[4]||0,r[5]||0,r[6]||0,o)):new Date(r[1],n,r[3]||1,r[4]||0,r[5]||0,r[6]||0,o)}}return new Date(e)}(t),this.$x=t.x||{},this.init()},m.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},m.$utils=function(){return k},m.isValid=function(){return!(this.$d.toString()===d)},m.isSame=function(t,e){var i=v(t);return this.startOf(e)<=i&&i<=this.endOf(e)},m.isAfter=function(t,e){return v(t)<this.startOf(e)},m.isBefore=function(t,e){return this.endOf(e)<v(t)},m.$g=function(t,e,i){return k.u(t)?this[e]:this.set(i,t)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(t,e){var i=this,h=!!k.u(e)||e,d=k.p(t),f=function(t,e){var r=k.w(i.$u?Date.UTC(i.$y,e,t):new Date(i.$y,e,t),i);return h?r:r.endOf(a)},p=function(t,e){return k.w(i.toDate()[t].apply(i.toDate("s"),(h?[0,0,0,0]:[23,59,59,999]).slice(e)),i)},g=this.$W,m=this.$M,y=this.$D,_="set"+(this.$u?"UTC":"");switch(d){case c:return h?f(1,0):f(31,11);case l:return h?f(1,m):f(0,m+1);case s:var b=this.$locale().weekStart||0,C=(g<b?g+7:g)-b;return f(h?y-C:y+(6-C),m);case a:case u:return p(_+"Hours",0);case o:return p(_+"Minutes",1);case n:return p(_+"Seconds",2);case r:return p(_+"Milliseconds",3);default:return this.clone()}},m.endOf=function(t){return this.startOf(t,!1)},m.$set=function(t,e){var s,h=k.p(t),d="set"+(this.$u?"UTC":""),f=(s={},s[a]=d+"Date",s[u]=d+"Date",s[l]=d+"Month",s[c]=d+"FullYear",s[o]=d+"Hours",s[n]=d+"Minutes",s[r]=d+"Seconds",s[i]=d+"Milliseconds",s)[h],p=h===a?this.$D+(e-this.$W):e;if(h===l||h===c){var g=this.clone().set(u,1);g.$d[f](p),g.init(),this.$d=g.set(u,Math.min(this.$D,g.daysInMonth())).$d}else f&&this.$d[f](p);return this.init(),this},m.set=function(t,e){return this.clone().$set(t,e)},m.get=function(t){return this[k.p(t)]()},m.add=function(i,h){var u,d=this;i=Number(i);var f=k.p(h),p=function(t){var e=v(d);return k.w(e.date(e.date()+Math.round(t*i)),d)};if(f===l)return this.set(l,this.$M+i);if(f===c)return this.set(c,this.$y+i);if(f===a)return p(1);if(f===s)return p(7);var g=(u={},u[n]=t,u[o]=e,u[r]=1e3,u)[f]||1,m=this.$d.getTime()+i*g;return k.w(m,this)},m.subtract=function(t,e){return this.add(-1*t,e)},m.format=function(t){var e=this,i=this.$locale();if(!this.isValid())return i.invalidDate||d;var r=t||"YYYY-MM-DDTHH:mm:ssZ",n=k.z(this),o=this.$H,a=this.$m,s=this.$M,l=i.weekdays,h=i.months,c=i.meridiem,u=function(t,i,n,o){return t&&(t[i]||t(e,r))||n[i].slice(0,o)},f=function(t){return k.s(o%12||12,t,"0")},g=c||function(t,e,i){var r=t<12?"AM":"PM";return i?r.toLowerCase():r};return r.replace(p,(function(t,r){return r||function(t){switch(t){case"YY":return String(e.$y).slice(-2);case"YYYY":return k.s(e.$y,4,"0");case"M":return s+1;case"MM":return k.s(s+1,2,"0");case"MMM":return u(i.monthsShort,s,h,3);case"MMMM":return u(h,s);case"D":return e.$D;case"DD":return k.s(e.$D,2,"0");case"d":return String(e.$W);case"dd":return u(i.weekdaysMin,e.$W,l,2);case"ddd":return u(i.weekdaysShort,e.$W,l,3);case"dddd":return l[e.$W];case"H":return String(o);case"HH":return k.s(o,2,"0");case"h":return f(1);case"hh":return f(2);case"a":return g(o,a,!0);case"A":return g(o,a,!1);case"m":return String(a);case"mm":return k.s(a,2,"0");case"s":return String(e.$s);case"ss":return k.s(e.$s,2,"0");case"SSS":return k.s(e.$ms,3,"0");case"Z":return n}return null}(t)||n.replace(":","")}))},m.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},m.diff=function(i,u,d){var f,p=this,g=k.p(u),m=v(i),y=(m.utcOffset()-this.utcOffset())*t,_=this-m,b=function(){return k.m(p,m)};switch(g){case c:f=b()/12;break;case l:f=b();break;case h:f=b()/3;break;case s:f=(_-y)/6048e5;break;case a:f=(_-y)/864e5;break;case o:f=_/e;break;case n:f=_/t;break;case r:f=_/1e3;break;default:f=_}return d?f:k.a(f)},m.daysInMonth=function(){return this.endOf(l).$D},m.$locale=function(){return b[this.$L]},m.locale=function(t,e){if(!t)return this.$L;var i=this.clone(),r=x(t,e,!0);return r&&(i.$L=r),i},m.clone=function(){return k.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},g}(),w=T.prototype;return v.prototype=w,[["$ms",i],["$s",r],["$m",n],["$H",o],["$W",a],["$M",l],["$y",c],["$D",u]].forEach((function(t){w[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),v.extend=function(t,e){return t.$i||(t(e,T,v),t.$i=!0),v},v.locale=x,v.isDayjs=C,v.unix=function(t){return v(1e3*t)},v.en=b[_],v.Ls=b,v.p={},v}()},7856:function(t){t.exports=function(){"use strict";const{entries:t,setPrototypeOf:e,isFrozen:i,getPrototypeOf:r,getOwnPropertyDescriptor:n}=Object;let{freeze:o,seal:a,create:s}=Object,{apply:l,construct:h}="undefined"!=typeof Reflect&&Reflect;l||(l=function(t,e,i){return t.apply(e,i)}),o||(o=function(t){return t}),a||(a=function(t){return t}),h||(h=function(t,e){return new t(...e)});const c=v(Array.prototype.forEach),u=v(Array.prototype.pop),d=v(Array.prototype.push),f=v(String.prototype.toLowerCase),p=v(String.prototype.toString),g=v(String.prototype.match),m=v(String.prototype.replace),y=v(String.prototype.indexOf),_=v(String.prototype.trim),b=v(RegExp.prototype.test),C=(x=TypeError,function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return h(x,e)});var x;function v(t){return function(e){for(var i=arguments.length,r=new Array(i>1?i-1:0),n=1;n<i;n++)r[n-1]=arguments[n];return l(t,e,r)}}function k(t,r,n){var o;n=null!==(o=n)&&void 0!==o?o:f,e&&e(t,null);let a=r.length;for(;a--;){let e=r[a];if("string"==typeof e){const t=n(e);t!==e&&(i(r)||(r[a]=t),e=t)}t[e]=!0}return t}function T(e){const i=s(null);for(const[r,n]of t(e))i[r]=n;return i}function w(t,e){for(;null!==t;){const i=n(t,e);if(i){if(i.get)return v(i.get);if("function"==typeof i.value)return v(i.value)}t=r(t)}return function(t){return console.warn("fallback value for",t),null}}const S=o(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),B=o(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),F=o(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),L=o(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),M=o(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),A=o(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),E=o(["#text"]),O=o(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),I=o(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),N=o(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),q=o(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),D=a(/\{\{[\w\W]*|[\w\W]*\}\}/gm),$=a(/<%[\w\W]*|[\w\W]*%>/gm),Z=a(/\${[\w\W]*}/gm),z=a(/^data-[\-\w.\u00B7-\uFFFF]/),j=a(/^aria-[\-\w]+$/),P=a(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),R=a(/^(?:\w+script|data):/i),W=a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),U=a(/^html$/i);var H=Object.freeze({__proto__:null,MUSTACHE_EXPR:D,ERB_EXPR:$,TMPLIT_EXPR:Z,DATA_ATTR:z,ARIA_ATTR:j,IS_ALLOWED_URI:P,IS_SCRIPT_OR_DATA:R,ATTR_WHITESPACE:W,DOCTYPE_NAME:U});const Y=()=>"undefined"==typeof window?null:window;return function e(){let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Y();const r=t=>e(t);if(r.version="3.0.3",r.removed=[],!i||!i.document||9!==i.document.nodeType)return r.isSupported=!1,r;const n=i.document,a=n.currentScript;let{document:s}=i;const{DocumentFragment:l,HTMLTemplateElement:h,Node:x,Element:v,NodeFilter:D,NamedNodeMap:$=i.NamedNodeMap||i.MozNamedAttrMap,HTMLFormElement:Z,DOMParser:z,trustedTypes:j}=i,R=v.prototype,W=w(R,"cloneNode"),V=w(R,"nextSibling"),G=w(R,"childNodes"),X=w(R,"parentNode");if("function"==typeof h){const t=s.createElement("template");t.content&&t.content.ownerDocument&&(s=t.content.ownerDocument)}let J,Q="";const{implementation:K,createNodeIterator:tt,createDocumentFragment:et,getElementsByTagName:it}=s,{importNode:rt}=n;let nt={};r.isSupported="function"==typeof t&&"function"==typeof X&&K&&void 0!==K.createHTMLDocument;const{MUSTACHE_EXPR:ot,ERB_EXPR:at,TMPLIT_EXPR:st,DATA_ATTR:lt,ARIA_ATTR:ht,IS_SCRIPT_OR_DATA:ct,ATTR_WHITESPACE:ut}=H;let{IS_ALLOWED_URI:dt}=H,ft=null;const pt=k({},[...S,...B,...F,...M,...E]);let gt=null;const mt=k({},[...O,...I,...N,...q]);let yt=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),_t=null,bt=null,Ct=!0,xt=!0,vt=!1,kt=!0,Tt=!1,wt=!1,St=!1,Bt=!1,Ft=!1,Lt=!1,Mt=!1,At=!0,Et=!1,Ot=!0,It=!1,Nt={},qt=null;const Dt=k({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let $t=null;const Zt=k({},["audio","video","img","source","image","track"]);let zt=null;const jt=k({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Pt="http://www.w3.org/1998/Math/MathML",Rt="http://www.w3.org/2000/svg",Wt="http://www.w3.org/1999/xhtml";let Ut=Wt,Ht=!1,Yt=null;const Vt=k({},[Pt,Rt,Wt],p);let Gt;const Xt=["application/xhtml+xml","text/html"];let Jt,Qt=null;const Kt=s.createElement("form"),te=function(t){return t instanceof RegExp||t instanceof Function},ee=function(t){if(!Qt||Qt!==t){if(t&&"object"==typeof t||(t={}),t=T(t),Gt=Gt=-1===Xt.indexOf(t.PARSER_MEDIA_TYPE)?"text/html":t.PARSER_MEDIA_TYPE,Jt="application/xhtml+xml"===Gt?p:f,ft="ALLOWED_TAGS"in t?k({},t.ALLOWED_TAGS,Jt):pt,gt="ALLOWED_ATTR"in t?k({},t.ALLOWED_ATTR,Jt):mt,Yt="ALLOWED_NAMESPACES"in t?k({},t.ALLOWED_NAMESPACES,p):Vt,zt="ADD_URI_SAFE_ATTR"in t?k(T(jt),t.ADD_URI_SAFE_ATTR,Jt):jt,$t="ADD_DATA_URI_TAGS"in t?k(T(Zt),t.ADD_DATA_URI_TAGS,Jt):Zt,qt="FORBID_CONTENTS"in t?k({},t.FORBID_CONTENTS,Jt):Dt,_t="FORBID_TAGS"in t?k({},t.FORBID_TAGS,Jt):{},bt="FORBID_ATTR"in t?k({},t.FORBID_ATTR,Jt):{},Nt="USE_PROFILES"in t&&t.USE_PROFILES,Ct=!1!==t.ALLOW_ARIA_ATTR,xt=!1!==t.ALLOW_DATA_ATTR,vt=t.ALLOW_UNKNOWN_PROTOCOLS||!1,kt=!1!==t.ALLOW_SELF_CLOSE_IN_ATTR,Tt=t.SAFE_FOR_TEMPLATES||!1,wt=t.WHOLE_DOCUMENT||!1,Ft=t.RETURN_DOM||!1,Lt=t.RETURN_DOM_FRAGMENT||!1,Mt=t.RETURN_TRUSTED_TYPE||!1,Bt=t.FORCE_BODY||!1,At=!1!==t.SANITIZE_DOM,Et=t.SANITIZE_NAMED_PROPS||!1,Ot=!1!==t.KEEP_CONTENT,It=t.IN_PLACE||!1,dt=t.ALLOWED_URI_REGEXP||P,Ut=t.NAMESPACE||Wt,yt=t.CUSTOM_ELEMENT_HANDLING||{},t.CUSTOM_ELEMENT_HANDLING&&te(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(yt.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&te(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(yt.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(yt.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Tt&&(xt=!1),Lt&&(Ft=!0),Nt&&(ft=k({},[...E]),gt=[],!0===Nt.html&&(k(ft,S),k(gt,O)),!0===Nt.svg&&(k(ft,B),k(gt,I),k(gt,q)),!0===Nt.svgFilters&&(k(ft,F),k(gt,I),k(gt,q)),!0===Nt.mathMl&&(k(ft,M),k(gt,N),k(gt,q))),t.ADD_TAGS&&(ft===pt&&(ft=T(ft)),k(ft,t.ADD_TAGS,Jt)),t.ADD_ATTR&&(gt===mt&&(gt=T(gt)),k(gt,t.ADD_ATTR,Jt)),t.ADD_URI_SAFE_ATTR&&k(zt,t.ADD_URI_SAFE_ATTR,Jt),t.FORBID_CONTENTS&&(qt===Dt&&(qt=T(qt)),k(qt,t.FORBID_CONTENTS,Jt)),Ot&&(ft["#text"]=!0),wt&&k(ft,["html","head","body"]),ft.table&&(k(ft,["tbody"]),delete _t.tbody),t.TRUSTED_TYPES_POLICY){if("function"!=typeof t.TRUSTED_TYPES_POLICY.createHTML)throw C('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof t.TRUSTED_TYPES_POLICY.createScriptURL)throw C('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');J=t.TRUSTED_TYPES_POLICY,Q=J.createHTML("")}else void 0===J&&(J=function(t,e){if("object"!=typeof t||"function"!=typeof t.createPolicy)return null;let i=null;const r="data-tt-policy-suffix";e&&e.hasAttribute(r)&&(i=e.getAttribute(r));const n="dompurify"+(i?"#"+i:"");try{return t.createPolicy(n,{createHTML(t){return t},createScriptURL(t){return t}})}catch(t){return console.warn("TrustedTypes policy "+n+" could not be created."),null}}(j,a)),null!==J&&"string"==typeof Q&&(Q=J.createHTML(""));o&&o(t),Qt=t}},ie=k({},["mi","mo","mn","ms","mtext"]),re=k({},["foreignobject","desc","title","annotation-xml"]),ne=k({},["title","style","font","a","script"]),oe=k({},B);k(oe,F),k(oe,L);const ae=k({},M);k(ae,A);const se=function(t){d(r.removed,{element:t});try{t.parentNode.removeChild(t)}catch(e){t.remove()}},le=function(t,e){try{d(r.removed,{attribute:e.getAttributeNode(t),from:e})}catch(t){d(r.removed,{attribute:null,from:e})}if(e.removeAttribute(t),"is"===t&&!gt[t])if(Ft||Lt)try{se(e)}catch(t){}else try{e.setAttribute(t,"")}catch(t){}},he=function(t){let e,i;if(Bt)t="<remove></remove>"+t;else{const e=g(t,/^[\r\n\t ]+/);i=e&&e[0]}"application/xhtml+xml"===Gt&&Ut===Wt&&(t='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+t+"</body></html>");const r=J?J.createHTML(t):t;if(Ut===Wt)try{e=(new z).parseFromString(r,Gt)}catch(t){}if(!e||!e.documentElement){e=K.createDocument(Ut,"template",null);try{e.documentElement.innerHTML=Ht?Q:r}catch(t){}}const n=e.body||e.documentElement;return t&&i&&n.insertBefore(s.createTextNode(i),n.childNodes[0]||null),Ut===Wt?it.call(e,wt?"html":"body")[0]:wt?e.documentElement:n},ce=function(t){return tt.call(t.ownerDocument||t,t,D.SHOW_ELEMENT|D.SHOW_COMMENT|D.SHOW_TEXT,null,!1)},ue=function(t){return"object"==typeof x?t instanceof x:t&&"object"==typeof t&&"number"==typeof t.nodeType&&"string"==typeof t.nodeName},de=function(t,e,i){nt[t]&&c(nt[t],(t=>{t.call(r,e,i,Qt)}))},fe=function(t){let e;if(de("beforeSanitizeElements",t,null),(i=t)instanceof Z&&("string"!=typeof i.nodeName||"string"!=typeof i.textContent||"function"!=typeof i.removeChild||!(i.attributes instanceof $)||"function"!=typeof i.removeAttribute||"function"!=typeof i.setAttribute||"string"!=typeof i.namespaceURI||"function"!=typeof i.insertBefore||"function"!=typeof i.hasChildNodes))return se(t),!0;var i;const n=Jt(t.nodeName);if(de("uponSanitizeElement",t,{tagName:n,allowedTags:ft}),t.hasChildNodes()&&!ue(t.firstElementChild)&&(!ue(t.content)||!ue(t.content.firstElementChild))&&b(/<[/\w]/g,t.innerHTML)&&b(/<[/\w]/g,t.textContent))return se(t),!0;if(!ft[n]||_t[n]){if(!_t[n]&&ge(n)){if(yt.tagNameCheck instanceof RegExp&&b(yt.tagNameCheck,n))return!1;if(yt.tagNameCheck instanceof Function&&yt.tagNameCheck(n))return!1}if(Ot&&!qt[n]){const e=X(t)||t.parentNode,i=G(t)||t.childNodes;if(i&&e)for(let r=i.length-1;r>=0;--r)e.insertBefore(W(i[r],!0),V(t))}return se(t),!0}return t instanceof v&&!function(t){let e=X(t);e&&e.tagName||(e={namespaceURI:Ut,tagName:"template"});const i=f(t.tagName),r=f(e.tagName);return!!Yt[t.namespaceURI]&&(t.namespaceURI===Rt?e.namespaceURI===Wt?"svg"===i:e.namespaceURI===Pt?"svg"===i&&("annotation-xml"===r||ie[r]):Boolean(oe[i]):t.namespaceURI===Pt?e.namespaceURI===Wt?"math"===i:e.namespaceURI===Rt?"math"===i&&re[r]:Boolean(ae[i]):t.namespaceURI===Wt?!(e.namespaceURI===Rt&&!re[r])&&!(e.namespaceURI===Pt&&!ie[r])&&!ae[i]&&(ne[i]||!oe[i]):!("application/xhtml+xml"!==Gt||!Yt[t.namespaceURI]))}(t)?(se(t),!0):"noscript"!==n&&"noembed"!==n||!b(/<\/no(script|embed)/i,t.innerHTML)?(Tt&&3===t.nodeType&&(e=t.textContent,e=m(e,ot," "),e=m(e,at," "),e=m(e,st," "),t.textContent!==e&&(d(r.removed,{element:t.cloneNode()}),t.textContent=e)),de("afterSanitizeElements",t,null),!1):(se(t),!0)},pe=function(t,e,i){if(At&&("id"===e||"name"===e)&&(i in s||i in Kt))return!1;if(xt&&!bt[e]&&b(lt,e));else if(Ct&&b(ht,e));else if(!gt[e]||bt[e]){if(!(ge(t)&&(yt.tagNameCheck instanceof RegExp&&b(yt.tagNameCheck,t)||yt.tagNameCheck instanceof Function&&yt.tagNameCheck(t))&&(yt.attributeNameCheck instanceof RegExp&&b(yt.attributeNameCheck,e)||yt.attributeNameCheck instanceof Function&&yt.attributeNameCheck(e))||"is"===e&&yt.allowCustomizedBuiltInElements&&(yt.tagNameCheck instanceof RegExp&&b(yt.tagNameCheck,i)||yt.tagNameCheck instanceof Function&&yt.tagNameCheck(i))))return!1}else if(zt[e]);else if(b(dt,m(i,ut,"")));else if("src"!==e&&"xlink:href"!==e&&"href"!==e||"script"===t||0!==y(i,"data:")||!$t[t])if(vt&&!b(ct,m(i,ut,"")));else if(i)return!1;return!0},ge=function(t){return t.indexOf("-")>0},me=function(t){let e,i,n,o;de("beforeSanitizeAttributes",t,null);const{attributes:a}=t;if(!a)return;const s={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:gt};for(o=a.length;o--;){e=a[o];const{name:l,namespaceURI:h}=e;if(i="value"===l?e.value:_(e.value),n=Jt(l),s.attrName=n,s.attrValue=i,s.keepAttr=!0,s.forceKeepAttr=void 0,de("uponSanitizeAttribute",t,s),i=s.attrValue,s.forceKeepAttr)continue;if(le(l,t),!s.keepAttr)continue;if(!kt&&b(/\/>/i,i)){le(l,t);continue}Tt&&(i=m(i,ot," "),i=m(i,at," "),i=m(i,st," "));const c=Jt(t.nodeName);if(pe(c,n,i)){if(!Et||"id"!==n&&"name"!==n||(le(l,t),i="user-content-"+i),J&&"object"==typeof j&&"function"==typeof j.getAttributeType)if(h);else switch(j.getAttributeType(c,n)){case"TrustedHTML":i=J.createHTML(i);break;case"TrustedScriptURL":i=J.createScriptURL(i)}try{h?t.setAttributeNS(h,l,i):t.setAttribute(l,i),u(r.removed)}catch(t){}}}de("afterSanitizeAttributes",t,null)},ye=function t(e){let i;const r=ce(e);for(de("beforeSanitizeShadowDOM",e,null);i=r.nextNode();)de("uponSanitizeShadowNode",i,null),fe(i)||(i.content instanceof l&&t(i.content),me(i));de("afterSanitizeShadowDOM",e,null)};return r.sanitize=function(t){let e,i,o,a,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Ht=!t,Ht&&(t="\x3c!--\x3e"),"string"!=typeof t&&!ue(t)){if("function"!=typeof t.toString)throw C("toString is not a function");if("string"!=typeof(t=t.toString()))throw C("dirty is not a string, aborting")}if(!r.isSupported)return t;if(St||ee(s),r.removed=[],"string"==typeof t&&(It=!1),It){if(t.nodeName){const e=Jt(t.nodeName);if(!ft[e]||_t[e])throw C("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof x)e=he("\x3c!----\x3e"),i=e.ownerDocument.importNode(t,!0),1===i.nodeType&&"BODY"===i.nodeName||"HTML"===i.nodeName?e=i:e.appendChild(i);else{if(!Ft&&!Tt&&!wt&&-1===t.indexOf("<"))return J&&Mt?J.createHTML(t):t;if(e=he(t),!e)return Ft?null:Mt?Q:""}e&&Bt&&se(e.firstChild);const h=ce(It?t:e);for(;o=h.nextNode();)fe(o)||(o.content instanceof l&&ye(o.content),me(o));if(It)return t;if(Ft){if(Lt)for(a=et.call(e.ownerDocument);e.firstChild;)a.appendChild(e.firstChild);else a=e;return(gt.shadowroot||gt.shadowrootmod)&&(a=rt.call(n,a,!0)),a}let c=wt?e.outerHTML:e.innerHTML;return wt&&ft["!doctype"]&&e.ownerDocument&&e.ownerDocument.doctype&&e.ownerDocument.doctype.name&&b(U,e.ownerDocument.doctype.name)&&(c="<!DOCTYPE "+e.ownerDocument.doctype.name+">\n"+c),Tt&&(c=m(c,ot," "),c=m(c,at," "),c=m(c,st," ")),J&&Mt?J.createHTML(c):c},r.setConfig=function(t){ee(t),St=!0},r.clearConfig=function(){Qt=null,St=!1},r.isValidAttribute=function(t,e,i){Qt||ee({});const r=Jt(t),n=Jt(e);return pe(r,n,i)},r.addHook=function(t,e){"function"==typeof e&&(nt[t]=nt[t]||[],d(nt[t],e))},r.removeHook=function(t){if(nt[t])return u(nt[t])},r.removeHooks=function(t){nt[t]&&(nt[t]=[])},r.removeAllHooks=function(){nt={}},r}()}()},8464:function(t,e,i){"use strict";function r(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var r=Array.from("string"==typeof t?[t]:t);r[r.length-1]=r[r.length-1].replace(/\r?\n([\t ]*)$/,"");var n=r.reduce((function(t,e){var i=e.match(/\n([\t ]+|(?!\s).)/g);return i?t.concat(i.map((function(t){var e,i;return null!==(i=null===(e=t.match(/[\t ]/g))||void 0===e?void 0:e.length)&&void 0!==i?i:0}))):t}),[]);if(n.length){var o=new RegExp("\n[\t ]{"+Math.min.apply(Math,n)+"}","g");r=r.map((function(t){return t.replace(o,"\n")}))}r[0]=r[0].replace(/^\r?\n/,"");var a=r[0];return e.forEach((function(t,e){var i=a.match(/(?:^|\n)( *)$/),n=i?i[1]:"",o=t;"string"==typeof t&&t.includes("\n")&&(o=String(t).split("\n").map((function(t,e){return 0===e?t:""+n+t})).join("\n")),a+=o+r[e+1]})),a}i.d(e,{Z:function(){return r}})},5740:function(t,e,i){"use strict";function r(t,e){let i;if(void 0===e)for(const e of t)null!=e&&(i<e||void 0===i&&e>=e)&&(i=e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(i<n||void 0===i&&n>=n)&&(i=n)}return i}function n(t,e){let i;if(void 0===e)for(const e of t)null!=e&&(i>e||void 0===i&&e>=e)&&(i=e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(i>n||void 0===i&&n>=n)&&(i=n)}return i}function o(t){return t}i.d(e,{Nb1:function(){return Ha},LLu:function(){return _},F5q:function(){return y},$0Z:function(){return os},Dts:function(){return ss},WQY:function(){return hs},qpX:function(){return us},u93:function(){return ds},tFB:function(){return ps},YY7:function(){return ys},OvA:function(){return bs},dCK:function(){return xs},zgE:function(){return Ts},fGX:function(){return Ss},$m7:function(){return Fs},c_6:function(){return Ga},fxm:function(){return Ms},FdL:function(){return $s},ak_:function(){return Zs},SxZ:function(){return Ps},eA_:function(){return Ws},jsv:function(){return Hs},iJ:function(){return Us},JHv:function(){return or},jvg:function(){return Qa},Fp7:function(){return r},VV$:function(){return n},ve8:function(){return es},BYU:function(){return Gr},PKp:function(){return tn},Xf:function(){return ya},Ys:function(){return _a},td_:function(){return ba},YPS:function(){return Zi},rr1:function(){return yn},i$Z:function(){return Gn},WQD:function(){return gn},Z_i:function(){return fn},F0B:function(){return qn},NGh:function(){return xn}});var a=1,s=2,l=3,h=4,c=1e-6;function u(t){return"translate("+t+",0)"}function d(t){return"translate(0,"+t+")"}function f(t){return e=>+t(e)}function p(t,e){return e=Math.max(0,t.bandwidth()-2*e)/2,t.round()&&(e=Math.round(e)),i=>+t(i)+e}function g(){return!this.__axis}function m(t,e){var i=[],r=null,n=null,m=6,y=6,_=3,b="undefined"!=typeof window&&window.devicePixelRatio>1?0:.5,C=t===a||t===h?-1:1,x=t===h||t===s?"x":"y",v=t===a||t===l?u:d;function k(u){var d=null==r?e.ticks?e.ticks.apply(e,i):e.domain():r,k=null==n?e.tickFormat?e.tickFormat.apply(e,i):o:n,T=Math.max(m,0)+_,w=e.range(),S=+w[0]+b,B=+w[w.length-1]+b,F=(e.bandwidth?p:f)(e.copy(),b),L=u.selection?u.selection():u,M=L.selectAll(".domain").data([null]),A=L.selectAll(".tick").data(d,e).order(),E=A.exit(),O=A.enter().append("g").attr("class","tick"),I=A.select("line"),N=A.select("text");M=M.merge(M.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),A=A.merge(O),I=I.merge(O.append("line").attr("stroke","currentColor").attr(x+"2",C*m)),N=N.merge(O.append("text").attr("fill","currentColor").attr(x,C*T).attr("dy",t===a?"0em":t===l?"0.71em":"0.32em")),u!==L&&(M=M.transition(u),A=A.transition(u),I=I.transition(u),N=N.transition(u),E=E.transition(u).attr("opacity",c).attr("transform",(function(t){return isFinite(t=F(t))?v(t+b):this.getAttribute("transform")})),O.attr("opacity",c).attr("transform",(function(t){var e=this.parentNode.__axis;return v((e&&isFinite(e=e(t))?e:F(t))+b)}))),E.remove(),M.attr("d",t===h||t===s?y?"M"+C*y+","+S+"H"+b+"V"+B+"H"+C*y:"M"+b+","+S+"V"+B:y?"M"+S+","+C*y+"V"+b+"H"+B+"V"+C*y:"M"+S+","+b+"H"+B),A.attr("opacity",1).attr("transform",(function(t){return v(F(t)+b)})),I.attr(x+"2",C*m),N.attr(x,C*T).text(k),L.filter(g).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===s?"start":t===h?"end":"middle"),L.each((function(){this.__axis=F}))}return k.scale=function(t){return arguments.length?(e=t,k):e},k.ticks=function(){return i=Array.from(arguments),k},k.tickArguments=function(t){return arguments.length?(i=null==t?[]:Array.from(t),k):i.slice()},k.tickValues=function(t){return arguments.length?(r=null==t?null:Array.from(t),k):r&&r.slice()},k.tickFormat=function(t){return arguments.length?(n=t,k):n},k.tickSize=function(t){return arguments.length?(m=y=+t,k):m},k.tickSizeInner=function(t){return arguments.length?(m=+t,k):m},k.tickSizeOuter=function(t){return arguments.length?(y=+t,k):y},k.tickPadding=function(t){return arguments.length?(_=+t,k):_},k.offset=function(t){return arguments.length?(b=+t,k):b},k}function y(t){return m(a,t)}function _(t){return m(l,t)}function b(){}function C(t){return null==t?b:function(){return this.querySelector(t)}}function x(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}function v(){return[]}function k(t){return null==t?v:function(){return this.querySelectorAll(t)}}function T(t){return function(){return this.matches(t)}}function w(t){return function(e){return e.matches(t)}}var S=Array.prototype.find;function B(){return this.firstElementChild}var F=Array.prototype.filter;function L(){return Array.from(this.children)}function M(t){return new Array(t.length)}function A(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}function E(t,e,i,r,n,o){for(var a,s=0,l=e.length,h=o.length;s<h;++s)(a=e[s])?(a.__data__=o[s],r[s]=a):i[s]=new A(t,o[s]);for(;s<l;++s)(a=e[s])&&(n[s]=a)}function O(t,e,i,r,n,o,a){var s,l,h,c=new Map,u=e.length,d=o.length,f=new Array(u);for(s=0;s<u;++s)(l=e[s])&&(f[s]=h=a.call(l,l.__data__,s,e)+"",c.has(h)?n[s]=l:c.set(h,l));for(s=0;s<d;++s)h=a.call(t,o[s],s,o)+"",(l=c.get(h))?(r[s]=l,l.__data__=o[s],c.delete(h)):i[s]=new A(t,o[s]);for(s=0;s<u;++s)(l=e[s])&&c.get(f[s])===l&&(n[s]=l)}function I(t){return t.__data__}function N(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function q(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}A.prototype={constructor:A,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var D="http://www.w3.org/1999/xhtml",$={svg:"http://www.w3.org/2000/svg",xhtml:D,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function Z(t){var e=t+="",i=e.indexOf(":");return i>=0&&"xmlns"!==(e=t.slice(0,i))&&(t=t.slice(i+1)),$.hasOwnProperty(e)?{space:$[e],local:t}:t}function z(t){return function(){this.removeAttribute(t)}}function j(t){return function(){this.removeAttributeNS(t.space,t.local)}}function P(t,e){return function(){this.setAttribute(t,e)}}function R(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}function W(t,e){return function(){var i=e.apply(this,arguments);null==i?this.removeAttribute(t):this.setAttribute(t,i)}}function U(t,e){return function(){var i=e.apply(this,arguments);null==i?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,i)}}function H(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function Y(t){return function(){this.style.removeProperty(t)}}function V(t,e,i){return function(){this.style.setProperty(t,e,i)}}function G(t,e,i){return function(){var r=e.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,i)}}function X(t,e){return t.style.getPropertyValue(e)||H(t).getComputedStyle(t,null).getPropertyValue(e)}function J(t){return function(){delete this[t]}}function Q(t,e){return function(){this[t]=e}}function K(t,e){return function(){var i=e.apply(this,arguments);null==i?delete this[t]:this[t]=i}}function tt(t){return t.trim().split(/^|\s+/)}function et(t){return t.classList||new it(t)}function it(t){this._node=t,this._names=tt(t.getAttribute("class")||"")}function rt(t,e){for(var i=et(t),r=-1,n=e.length;++r<n;)i.add(e[r])}function nt(t,e){for(var i=et(t),r=-1,n=e.length;++r<n;)i.remove(e[r])}function ot(t){return function(){rt(this,t)}}function at(t){return function(){nt(this,t)}}function st(t,e){return function(){(e.apply(this,arguments)?rt:nt)(this,t)}}function lt(){this.textContent=""}function ht(t){return function(){this.textContent=t}}function ct(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}function ut(){this.innerHTML=""}function dt(t){return function(){this.innerHTML=t}}function ft(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}function pt(){this.nextSibling&&this.parentNode.appendChild(this)}function gt(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function mt(t){return function(){var e=this.ownerDocument,i=this.namespaceURI;return i===D&&e.documentElement.namespaceURI===D?e.createElement(t):e.createElementNS(i,t)}}function yt(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function _t(t){var e=Z(t);return(e.local?yt:mt)(e)}function bt(){return null}function Ct(){var t=this.parentNode;t&&t.removeChild(this)}function xt(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function vt(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function kt(t){return function(){var e=this.__on;if(e){for(var i,r=0,n=-1,o=e.length;r<o;++r)i=e[r],t.type&&i.type!==t.type||i.name!==t.name?e[++n]=i:this.removeEventListener(i.type,i.listener,i.options);++n?e.length=n:delete this.__on}}}function Tt(t,e,i){return function(){var r,n=this.__on,o=function(t){return function(e){t.call(this,e,this.__data__)}}(e);if(n)for(var a=0,s=n.length;a<s;++a)if((r=n[a]).type===t.type&&r.name===t.name)return this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=i),void(r.value=e);this.addEventListener(t.type,o,i),r={type:t.type,name:t.name,value:e,listener:o,options:i},n?n.push(r):this.__on=[r]}}function wt(t,e,i){var r=H(t),n=r.CustomEvent;"function"==typeof n?n=new n(e,i):(n=r.document.createEvent("Event"),i?(n.initEvent(e,i.bubbles,i.cancelable),n.detail=i.detail):n.initEvent(e,!1,!1)),t.dispatchEvent(n)}function St(t,e){return function(){return wt(this,t,e)}}function Bt(t,e){return function(){return wt(this,t,e.apply(this,arguments))}}it.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var Ft=[null];function Lt(t,e){this._groups=t,this._parents=e}function Mt(){return new Lt([[document.documentElement]],Ft)}Lt.prototype=Mt.prototype={constructor:Lt,select:function(t){"function"!=typeof t&&(t=C(t));for(var e=this._groups,i=e.length,r=new Array(i),n=0;n<i;++n)for(var o,a,s=e[n],l=s.length,h=r[n]=new Array(l),c=0;c<l;++c)(o=s[c])&&(a=t.call(o,o.__data__,c,s))&&("__data__"in o&&(a.__data__=o.__data__),h[c]=a);return new Lt(r,this._parents)},selectAll:function(t){t="function"==typeof t?function(t){return function(){return x(t.apply(this,arguments))}}(t):k(t);for(var e=this._groups,i=e.length,r=[],n=[],o=0;o<i;++o)for(var a,s=e[o],l=s.length,h=0;h<l;++h)(a=s[h])&&(r.push(t.call(a,a.__data__,h,s)),n.push(a));return new Lt(r,n)},selectChild:function(t){return this.select(null==t?B:function(t){return function(){return S.call(this.children,t)}}("function"==typeof t?t:w(t)))},selectChildren:function(t){return this.selectAll(null==t?L:function(t){return function(){return F.call(this.children,t)}}("function"==typeof t?t:w(t)))},filter:function(t){"function"!=typeof t&&(t=T(t));for(var e=this._groups,i=e.length,r=new Array(i),n=0;n<i;++n)for(var o,a=e[n],s=a.length,l=r[n]=[],h=0;h<s;++h)(o=a[h])&&t.call(o,o.__data__,h,a)&&l.push(o);return new Lt(r,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,I);var i,r=e?O:E,n=this._parents,o=this._groups;"function"!=typeof t&&(i=t,t=function(){return i});for(var a=o.length,s=new Array(a),l=new Array(a),h=new Array(a),c=0;c<a;++c){var u=n[c],d=o[c],f=d.length,p=N(t.call(u,u&&u.__data__,c,n)),g=p.length,m=l[c]=new Array(g),y=s[c]=new Array(g);r(u,d,m,y,h[c]=new Array(f),p,e);for(var _,b,C=0,x=0;C<g;++C)if(_=m[C]){for(C>=x&&(x=C+1);!(b=y[x])&&++x<g;);_._next=b||null}}return(s=new Lt(s,n))._enter=l,s._exit=h,s},enter:function(){return new Lt(this._enter||this._groups.map(M),this._parents)},exit:function(){return new Lt(this._exit||this._groups.map(M),this._parents)},join:function(t,e,i){var r=this.enter(),n=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=e&&(n=e(n))&&(n=n.selection()),null==i?o.remove():i(o),r&&n?r.merge(n).order():n},merge:function(t){for(var e=t.selection?t.selection():t,i=this._groups,r=e._groups,n=i.length,o=r.length,a=Math.min(n,o),s=new Array(n),l=0;l<a;++l)for(var h,c=i[l],u=r[l],d=c.length,f=s[l]=new Array(d),p=0;p<d;++p)(h=c[p]||u[p])&&(f[p]=h);for(;l<n;++l)s[l]=i[l];return new Lt(s,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,i=t.length;++e<i;)for(var r,n=t[e],o=n.length-1,a=n[o];--o>=0;)(r=n[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function e(e,i){return e&&i?t(e.__data__,i.__data__):!e-!i}t||(t=q);for(var i=this._groups,r=i.length,n=new Array(r),o=0;o<r;++o){for(var a,s=i[o],l=s.length,h=n[o]=new Array(l),c=0;c<l;++c)(a=s[c])&&(h[c]=a);h.sort(e)}return new Lt(n,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,i=t.length;e<i;++e)for(var r=t[e],n=0,o=r.length;n<o;++n){var a=r[n];if(a)return a}return null},size:function(){let t=0;for(const e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,i=0,r=e.length;i<r;++i)for(var n,o=e[i],a=0,s=o.length;a<s;++a)(n=o[a])&&t.call(n,n.__data__,a,o);return this},attr:function(t,e){var i=Z(t);if(arguments.length<2){var r=this.node();return i.local?r.getAttributeNS(i.space,i.local):r.getAttribute(i)}return this.each((null==e?i.local?j:z:"function"==typeof e?i.local?U:W:i.local?R:P)(i,e))},style:function(t,e,i){return arguments.length>1?this.each((null==e?Y:"function"==typeof e?G:V)(t,e,null==i?"":i)):X(this.node(),t)},property:function(t,e){return arguments.length>1?this.each((null==e?J:"function"==typeof e?K:Q)(t,e)):this.node()[t]},classed:function(t,e){var i=tt(t+"");if(arguments.length<2){for(var r=et(this.node()),n=-1,o=i.length;++n<o;)if(!r.contains(i[n]))return!1;return!0}return this.each(("function"==typeof e?st:e?ot:at)(i,e))},text:function(t){return arguments.length?this.each(null==t?lt:("function"==typeof t?ct:ht)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?ut:("function"==typeof t?ft:dt)(t)):this.node().innerHTML},raise:function(){return this.each(pt)},lower:function(){return this.each(gt)},append:function(t){var e="function"==typeof t?t:_t(t);return this.select((function(){return this.appendChild(e.apply(this,arguments))}))},insert:function(t,e){var i="function"==typeof t?t:_t(t),r=null==e?bt:"function"==typeof e?e:C(e);return this.select((function(){return this.insertBefore(i.apply(this,arguments),r.apply(this,arguments)||null)}))},remove:function(){return this.each(Ct)},clone:function(t){return this.select(t?vt:xt)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,i){var r,n,o=function(t){return t.trim().split(/^|\s+/).map((function(t){var e="",i=t.indexOf(".");return i>=0&&(e=t.slice(i+1),t=t.slice(0,i)),{type:t,name:e}}))}(t+""),a=o.length;if(!(arguments.length<2)){for(s=e?Tt:kt,r=0;r<a;++r)this.each(s(o[r],e,i));return this}var s=this.node().__on;if(s)for(var l,h=0,c=s.length;h<c;++h)for(r=0,l=s[h];r<a;++r)if((n=o[r]).type===l.type&&n.name===l.name)return l.value},dispatch:function(t,e){return this.each(("function"==typeof e?Bt:St)(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,i=t.length;e<i;++e)for(var r,n=t[e],o=0,a=n.length;o<a;++o)(r=n[o])&&(yield r)}};var At=Mt,Et={value:()=>{}};function Ot(){for(var t,e=0,i=arguments.length,r={};e<i;++e){if(!(t=arguments[e]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new It(r)}function It(t){this._=t}function Nt(t,e){for(var i,r=0,n=t.length;r<n;++r)if((i=t[r]).name===e)return i.value}function qt(t,e,i){for(var r=0,n=t.length;r<n;++r)if(t[r].name===e){t[r]=Et,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=i&&t.push({name:e,value:i}),t}It.prototype=Ot.prototype={constructor:It,on:function(t,e){var i,r,n=this._,o=(r=n,(t+"").trim().split(/^|\s+/).map((function(t){var e="",i=t.indexOf(".");if(i>=0&&(e=t.slice(i+1),t=t.slice(0,i)),t&&!r.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:e}}))),a=-1,s=o.length;if(!(arguments.length<2)){if(null!=e&&"function"!=typeof e)throw new Error("invalid callback: "+e);for(;++a<s;)if(i=(t=o[a]).type)n[i]=qt(n[i],t.name,e);else if(null==e)for(i in n)n[i]=qt(n[i],t.name,null);return this}for(;++a<s;)if((i=(t=o[a]).type)&&(i=Nt(n[i],t.name)))return i},copy:function(){var t={},e=this._;for(var i in e)t[i]=e[i].slice();return new It(t)},call:function(t,e){if((i=arguments.length-2)>0)for(var i,r,n=new Array(i),o=0;o<i;++o)n[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=0,i=(r=this._[t]).length;o<i;++o)r[o].value.apply(e,n)},apply:function(t,e,i){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],n=0,o=r.length;n<o;++n)r[n].value.apply(e,i)}};var Dt,$t,Zt=Ot,zt=0,jt=0,Pt=0,Rt=1e3,Wt=0,Ut=0,Ht=0,Yt="object"==typeof performance&&performance.now?performance:Date,Vt="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function Gt(){return Ut||(Vt(Xt),Ut=Yt.now()+Ht)}function Xt(){Ut=0}function Jt(){this._call=this._time=this._next=null}function Qt(t,e,i){var r=new Jt;return r.restart(t,e,i),r}function Kt(){Ut=(Wt=Yt.now())+Ht,zt=jt=0;try{!function(){Gt(),++zt;for(var t,e=Dt;e;)(t=Ut-e._time)>=0&&e._call.call(void 0,t),e=e._next;--zt}()}finally{zt=0,function(){for(var t,e,i=Dt,r=1/0;i;)i._call?(r>i._time&&(r=i._time),t=i,i=i._next):(e=i._next,i._next=null,i=t?t._next=e:Dt=e);$t=t,ee(r)}(),Ut=0}}function te(){var t=Yt.now(),e=t-Wt;e>Rt&&(Ht-=e,Wt=t)}function ee(t){zt||(jt&&(jt=clearTimeout(jt)),t-Ut>24?(t<1/0&&(jt=setTimeout(Kt,t-Yt.now()-Ht)),Pt&&(Pt=clearInterval(Pt))):(Pt||(Wt=Yt.now(),Pt=setInterval(te,Rt)),zt=1,Vt(Kt)))}function ie(t,e,i){var r=new Jt;return e=null==e?0:+e,r.restart((i=>{r.stop(),t(i+e)}),e,i),r}Jt.prototype=Qt.prototype={constructor:Jt,restart:function(t,e,i){if("function"!=typeof t)throw new TypeError("callback is not a function");i=(null==i?Gt():+i)+(null==e?0:+e),this._next||$t===this||($t?$t._next=this:Dt=this,$t=this),this._call=t,this._time=i,ee()},stop:function(){this._call&&(this._call=null,this._time=1/0,ee())}};var re=Zt("start","end","cancel","interrupt"),ne=[],oe=0,ae=3;function se(t,e,i,r,n,o){var a=t.__transition;if(a){if(i in a)return}else t.__transition={};!function(t,e,i){var r,n=t.__transition;function o(l){var h,c,u,d;if(1!==i.state)return s();for(h in n)if((d=n[h]).name===i.name){if(d.state===ae)return ie(o);4===d.state?(d.state=6,d.timer.stop(),d.on.call("interrupt",t,t.__data__,d.index,d.group),delete n[h]):+h<e&&(d.state=6,d.timer.stop(),d.on.call("cancel",t,t.__data__,d.index,d.group),delete n[h])}if(ie((function(){i.state===ae&&(i.state=4,i.timer.restart(a,i.delay,i.time),a(l))})),i.state=2,i.on.call("start",t,t.__data__,i.index,i.group),2===i.state){for(i.state=ae,r=new Array(u=i.tween.length),h=0,c=-1;h<u;++h)(d=i.tween[h].value.call(t,t.__data__,i.index,i.group))&&(r[++c]=d);r.length=c+1}}function a(e){for(var n=e<i.duration?i.ease.call(null,e/i.duration):(i.timer.restart(s),i.state=5,1),o=-1,a=r.length;++o<a;)r[o].call(t,n);5===i.state&&(i.on.call("end",t,t.__data__,i.index,i.group),s())}function s(){for(var r in i.state=6,i.timer.stop(),delete n[e],n)return;delete t.__transition}n[e]=i,i.timer=Qt((function(t){i.state=1,i.timer.restart(o,i.delay,i.time),i.delay<=t&&o(t-i.delay)}),0,i.time)}(t,i,{name:e,index:r,group:n,on:re,tween:ne,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:oe})}function le(t,e){var i=ce(t,e);if(i.state>oe)throw new Error("too late; already scheduled");return i}function he(t,e){var i=ce(t,e);if(i.state>ae)throw new Error("too late; already running");return i}function ce(t,e){var i=t.__transition;if(!i||!(i=i[e]))throw new Error("transition not found");return i}function ue(t,e){return t=+t,e=+e,function(i){return t*(1-i)+e*i}}var de,fe=180/Math.PI,pe={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function ge(t,e,i,r,n,o){var a,s,l;return(a=Math.sqrt(t*t+e*e))&&(t/=a,e/=a),(l=t*i+e*r)&&(i-=t*l,r-=e*l),(s=Math.sqrt(i*i+r*r))&&(i/=s,r/=s,l/=s),t*r<e*i&&(t=-t,e=-e,l=-l,a=-a),{translateX:n,translateY:o,rotate:Math.atan2(e,t)*fe,skewX:Math.atan(l)*fe,scaleX:a,scaleY:s}}function me(t,e,i,r){function n(t){return t.length?t.pop()+" ":""}return function(o,a){var s=[],l=[];return o=t(o),a=t(a),function(t,r,n,o,a,s){if(t!==n||r!==o){var l=a.push("translate(",null,e,null,i);s.push({i:l-4,x:ue(t,n)},{i:l-2,x:ue(r,o)})}else(n||o)&&a.push("translate("+n+e+o+i)}(o.translateX,o.translateY,a.translateX,a.translateY,s,l),function(t,e,i,o){t!==e?(t-e>180?e+=360:e-t>180&&(t+=360),o.push({i:i.push(n(i)+"rotate(",null,r)-2,x:ue(t,e)})):e&&i.push(n(i)+"rotate("+e+r)}(o.rotate,a.rotate,s,l),function(t,e,i,o){t!==e?o.push({i:i.push(n(i)+"skewX(",null,r)-2,x:ue(t,e)}):e&&i.push(n(i)+"skewX("+e+r)}(o.skewX,a.skewX,s,l),function(t,e,i,r,o,a){if(t!==i||e!==r){var s=o.push(n(o)+"scale(",null,",",null,")");a.push({i:s-4,x:ue(t,i)},{i:s-2,x:ue(e,r)})}else 1===i&&1===r||o.push(n(o)+"scale("+i+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,s,l),o=a=null,function(t){for(var e,i=-1,r=l.length;++i<r;)s[(e=l[i]).i]=e.x(t);return s.join("")}}}var ye=me((function(t){const e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?pe:ge(e.a,e.b,e.c,e.d,e.e,e.f)}),"px, ","px)","deg)"),_e=me((function(t){return null==t?pe:(de||(de=document.createElementNS("http://www.w3.org/2000/svg","g")),de.setAttribute("transform",t),(t=de.transform.baseVal.consolidate())?ge((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):pe)}),", ",")",")");function be(t,e){var i,r;return function(){var n=he(this,t),o=n.tween;if(o!==i)for(var a=0,s=(r=i=o).length;a<s;++a)if(r[a].name===e){(r=r.slice()).splice(a,1);break}n.tween=r}}function Ce(t,e,i){var r,n;if("function"!=typeof i)throw new Error;return function(){var o=he(this,t),a=o.tween;if(a!==r){n=(r=a).slice();for(var s={name:e,value:i},l=0,h=n.length;l<h;++l)if(n[l].name===e){n[l]=s;break}l===h&&n.push(s)}o.tween=n}}function xe(t,e,i){var r=t._id;return t.each((function(){var t=he(this,r);(t.value||(t.value={}))[e]=i.apply(this,arguments)})),function(t){return ce(t,r).value[e]}}function ve(t,e,i){t.prototype=e.prototype=i,i.constructor=t}function ke(t,e){var i=Object.create(t.prototype);for(var r in e)i[r]=e[r];return i}function Te(){}var we=.7,Se=1/we,Be="\\s*([+-]?\\d+)\\s*",Fe="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Le="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Me=/^#([0-9a-f]{3,8})$/,Ae=new RegExp(`^rgb\\(${Be},${Be},${Be}\\)$`),Ee=new RegExp(`^rgb\\(${Le},${Le},${Le}\\)$`),Oe=new RegExp(`^rgba\\(${Be},${Be},${Be},${Fe}\\)$`),Ie=new RegExp(`^rgba\\(${Le},${Le},${Le},${Fe}\\)$`),Ne=new RegExp(`^hsl\\(${Fe},${Le},${Le}\\)$`),qe=new RegExp(`^hsla\\(${Fe},${Le},${Le},${Fe}\\)$`),De={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function $e(){return this.rgb().formatHex()}function Ze(){return this.rgb().formatRgb()}function ze(t){var e,i;return t=(t+"").trim().toLowerCase(),(e=Me.exec(t))?(i=e[1].length,e=parseInt(e[1],16),6===i?je(e):3===i?new Ue(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===i?Pe(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===i?Pe(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=Ae.exec(t))?new Ue(e[1],e[2],e[3],1):(e=Ee.exec(t))?new Ue(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=Oe.exec(t))?Pe(e[1],e[2],e[3],e[4]):(e=Ie.exec(t))?Pe(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=Ne.exec(t))?Je(e[1],e[2]/100,e[3]/100,1):(e=qe.exec(t))?Je(e[1],e[2]/100,e[3]/100,e[4]):De.hasOwnProperty(t)?je(De[t]):"transparent"===t?new Ue(NaN,NaN,NaN,0):null}function je(t){return new Ue(t>>16&255,t>>8&255,255&t,1)}function Pe(t,e,i,r){return r<=0&&(t=e=i=NaN),new Ue(t,e,i,r)}function Re(t){return t instanceof Te||(t=ze(t)),t?new Ue((t=t.rgb()).r,t.g,t.b,t.opacity):new Ue}function We(t,e,i,r){return 1===arguments.length?Re(t):new Ue(t,e,i,null==r?1:r)}function Ue(t,e,i,r){this.r=+t,this.g=+e,this.b=+i,this.opacity=+r}function He(){return`#${Xe(this.r)}${Xe(this.g)}${Xe(this.b)}`}function Ye(){const t=Ve(this.opacity);return`${1===t?"rgb(":"rgba("}${Ge(this.r)}, ${Ge(this.g)}, ${Ge(this.b)}${1===t?")":`, ${t})`}`}function Ve(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function Ge(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Xe(t){return((t=Ge(t))<16?"0":"")+t.toString(16)}function Je(t,e,i,r){return r<=0?t=e=i=NaN:i<=0||i>=1?t=e=NaN:e<=0&&(t=NaN),new Ke(t,e,i,r)}function Qe(t){if(t instanceof Ke)return new Ke(t.h,t.s,t.l,t.opacity);if(t instanceof Te||(t=ze(t)),!t)return new Ke;if(t instanceof Ke)return t;var e=(t=t.rgb()).r/255,i=t.g/255,r=t.b/255,n=Math.min(e,i,r),o=Math.max(e,i,r),a=NaN,s=o-n,l=(o+n)/2;return s?(a=e===o?(i-r)/s+6*(i<r):i===o?(r-e)/s+2:(e-i)/s+4,s/=l<.5?o+n:2-o-n,a*=60):s=l>0&&l<1?0:a,new Ke(a,s,l,t.opacity)}function Ke(t,e,i,r){this.h=+t,this.s=+e,this.l=+i,this.opacity=+r}function ti(t){return(t=(t||0)%360)<0?t+360:t}function ei(t){return Math.max(0,Math.min(1,t||0))}function ii(t,e,i){return 255*(t<60?e+(i-e)*t/60:t<180?i:t<240?e+(i-e)*(240-t)/60:e)}function ri(t,e,i,r,n){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*i+(1+3*t+3*o-3*a)*r+a*n)/6}ve(Te,ze,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:$e,formatHex:$e,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Qe(this).formatHsl()},formatRgb:Ze,toString:Ze}),ve(Ue,We,ke(Te,{brighter(t){return t=null==t?Se:Math.pow(Se,t),new Ue(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?we:Math.pow(we,t),new Ue(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Ue(Ge(this.r),Ge(this.g),Ge(this.b),Ve(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:He,formatHex:He,formatHex8:function(){return`#${Xe(this.r)}${Xe(this.g)}${Xe(this.b)}${Xe(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Ye,toString:Ye})),ve(Ke,(function(t,e,i,r){return 1===arguments.length?Qe(t):new Ke(t,e,i,null==r?1:r)}),ke(Te,{brighter(t){return t=null==t?Se:Math.pow(Se,t),new Ke(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?we:Math.pow(we,t),new Ke(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,i=this.l,r=i+(i<.5?i:1-i)*e,n=2*i-r;return new Ue(ii(t>=240?t-240:t+120,n,r),ii(t,n,r),ii(t<120?t+240:t-120,n,r),this.opacity)},clamp(){return new Ke(ti(this.h),ei(this.s),ei(this.l),Ve(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=Ve(this.opacity);return`${1===t?"hsl(":"hsla("}${ti(this.h)}, ${100*ei(this.s)}%, ${100*ei(this.l)}%${1===t?")":`, ${t})`}`}}));var ni=t=>()=>t;function oi(t,e){return function(i){return t+i*e}}function ai(t,e){var i=e-t;return i?oi(t,i):ni(isNaN(t)?e:t)}var si=function t(e){var i=function(t){return 1==(t=+t)?ai:function(e,i){return i-e?function(t,e,i){return t=Math.pow(t,i),e=Math.pow(e,i)-t,i=1/i,function(r){return Math.pow(t+r*e,i)}}(e,i,t):ni(isNaN(e)?i:e)}}(e);function r(t,e){var r=i((t=We(t)).r,(e=We(e)).r),n=i(t.g,e.g),o=i(t.b,e.b),a=ai(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=n(e),t.b=o(e),t.opacity=a(e),t+""}}return r.gamma=t,r}(1);function li(t){return function(e){var i,r,n=e.length,o=new Array(n),a=new Array(n),s=new Array(n);for(i=0;i<n;++i)r=We(e[i]),o[i]=r.r||0,a[i]=r.g||0,s[i]=r.b||0;return o=t(o),a=t(a),s=t(s),r.opacity=1,function(t){return r.r=o(t),r.g=a(t),r.b=s(t),r+""}}}li((function(t){var e=t.length-1;return function(i){var r=i<=0?i=0:i>=1?(i=1,e-1):Math.floor(i*e),n=t[r],o=t[r+1],a=r>0?t[r-1]:2*n-o,s=r<e-1?t[r+2]:2*o-n;return ri((i-r/e)*e,a,n,o,s)}})),li((function(t){var e=t.length;return function(i){var r=Math.floor(((i%=1)<0?++i:i)*e),n=t[(r+e-1)%e],o=t[r%e],a=t[(r+1)%e],s=t[(r+2)%e];return ri((i-r/e)*e,n,o,a,s)}}));var hi=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ci=new RegExp(hi.source,"g");function ui(t,e){var i,r,n,o=hi.lastIndex=ci.lastIndex=0,a=-1,s=[],l=[];for(t+="",e+="";(i=hi.exec(t))&&(r=ci.exec(e));)(n=r.index)>o&&(n=e.slice(o,n),s[a]?s[a]+=n:s[++a]=n),(i=i[0])===(r=r[0])?s[a]?s[a]+=r:s[++a]=r:(s[++a]=null,l.push({i:a,x:ue(i,r)})),o=ci.lastIndex;return o<e.length&&(n=e.slice(o),s[a]?s[a]+=n:s[++a]=n),s.length<2?l[0]?function(t){return function(e){return t(e)+""}}(l[0].x):function(t){return function(){return t}}(e):(e=l.length,function(t){for(var i,r=0;r<e;++r)s[(i=l[r]).i]=i.x(t);return s.join("")})}function di(t,e){var i;return("number"==typeof e?ue:e instanceof ze?si:(i=ze(e))?(e=i,si):ui)(t,e)}function fi(t){return function(){this.removeAttribute(t)}}function pi(t){return function(){this.removeAttributeNS(t.space,t.local)}}function gi(t,e,i){var r,n,o=i+"";return function(){var a=this.getAttribute(t);return a===o?null:a===r?n:n=e(r=a,i)}}function mi(t,e,i){var r,n,o=i+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===o?null:a===r?n:n=e(r=a,i)}}function yi(t,e,i){var r,n,o;return function(){var a,s,l=i(this);if(null!=l)return(a=this.getAttribute(t))===(s=l+"")?null:a===r&&s===n?o:(n=s,o=e(r=a,l));this.removeAttribute(t)}}function _i(t,e,i){var r,n,o;return function(){var a,s,l=i(this);if(null!=l)return(a=this.getAttributeNS(t.space,t.local))===(s=l+"")?null:a===r&&s===n?o:(n=s,o=e(r=a,l));this.removeAttributeNS(t.space,t.local)}}function bi(t,e){var i,r;function n(){var n=e.apply(this,arguments);return n!==r&&(i=(r=n)&&function(t,e){return function(i){this.setAttributeNS(t.space,t.local,e.call(this,i))}}(t,n)),i}return n._value=e,n}function Ci(t,e){var i,r;function n(){var n=e.apply(this,arguments);return n!==r&&(i=(r=n)&&function(t,e){return function(i){this.setAttribute(t,e.call(this,i))}}(t,n)),i}return n._value=e,n}function xi(t,e){return function(){le(this,t).delay=+e.apply(this,arguments)}}function vi(t,e){return e=+e,function(){le(this,t).delay=e}}function ki(t,e){return function(){he(this,t).duration=+e.apply(this,arguments)}}function Ti(t,e){return e=+e,function(){he(this,t).duration=e}}var wi=At.prototype.constructor;function Si(t){return function(){this.style.removeProperty(t)}}var Bi=0;function Fi(t,e,i,r){this._groups=t,this._parents=e,this._name=i,this._id=r}function Li(){return++Bi}var Mi=At.prototype;Fi.prototype=function(t){return At().transition(t)}.prototype={constructor:Fi,select:function(t){var e=this._name,i=this._id;"function"!=typeof t&&(t=C(t));for(var r=this._groups,n=r.length,o=new Array(n),a=0;a<n;++a)for(var s,l,h=r[a],c=h.length,u=o[a]=new Array(c),d=0;d<c;++d)(s=h[d])&&(l=t.call(s,s.__data__,d,h))&&("__data__"in s&&(l.__data__=s.__data__),u[d]=l,se(u[d],e,i,d,u,ce(s,i)));return new Fi(o,this._parents,e,i)},selectAll:function(t){var e=this._name,i=this._id;"function"!=typeof t&&(t=k(t));for(var r=this._groups,n=r.length,o=[],a=[],s=0;s<n;++s)for(var l,h=r[s],c=h.length,u=0;u<c;++u)if(l=h[u]){for(var d,f=t.call(l,l.__data__,u,h),p=ce(l,i),g=0,m=f.length;g<m;++g)(d=f[g])&&se(d,e,i,g,f,p);o.push(f),a.push(l)}return new Fi(o,a,e,i)},selectChild:Mi.selectChild,selectChildren:Mi.selectChildren,filter:function(t){"function"!=typeof t&&(t=T(t));for(var e=this._groups,i=e.length,r=new Array(i),n=0;n<i;++n)for(var o,a=e[n],s=a.length,l=r[n]=[],h=0;h<s;++h)(o=a[h])&&t.call(o,o.__data__,h,a)&&l.push(o);return new Fi(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var e=this._groups,i=t._groups,r=e.length,n=i.length,o=Math.min(r,n),a=new Array(r),s=0;s<o;++s)for(var l,h=e[s],c=i[s],u=h.length,d=a[s]=new Array(u),f=0;f<u;++f)(l=h[f]||c[f])&&(d[f]=l);for(;s<r;++s)a[s]=e[s];return new Fi(a,this._parents,this._name,this._id)},selection:function(){return new wi(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,i=Li(),r=this._groups,n=r.length,o=0;o<n;++o)for(var a,s=r[o],l=s.length,h=0;h<l;++h)if(a=s[h]){var c=ce(a,e);se(a,t,i,h,s,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new Fi(r,this._parents,t,i)},call:Mi.call,nodes:Mi.nodes,node:Mi.node,size:Mi.size,empty:Mi.empty,each:Mi.each,on:function(t,e){var i=this._id;return arguments.length<2?ce(this.node(),i).on.on(t):this.each(function(t,e,i){var r,n,o=function(t){return(t+"").trim().split(/^|\s+/).every((function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t}))}(e)?le:he;return function(){var a=o(this,t),s=a.on;s!==r&&(n=(r=s).copy()).on(e,i),a.on=n}}(i,t,e))},attr:function(t,e){var i=Z(t),r="transform"===i?_e:di;return this.attrTween(t,"function"==typeof e?(i.local?_i:yi)(i,r,xe(this,"attr."+t,e)):null==e?(i.local?pi:fi)(i):(i.local?mi:gi)(i,r,e))},attrTween:function(t,e){var i="attr."+t;if(arguments.length<2)return(i=this.tween(i))&&i._value;if(null==e)return this.tween(i,null);if("function"!=typeof e)throw new Error;var r=Z(t);return this.tween(i,(r.local?bi:Ci)(r,e))},style:function(t,e,i){var r="transform"==(t+="")?ye:di;return null==e?this.styleTween(t,function(t,e){var i,r,n;return function(){var o=X(this,t),a=(this.style.removeProperty(t),X(this,t));return o===a?null:o===i&&a===r?n:n=e(i=o,r=a)}}(t,r)).on("end.style."+t,Si(t)):"function"==typeof e?this.styleTween(t,function(t,e,i){var r,n,o;return function(){var a=X(this,t),s=i(this),l=s+"";return null==s&&(this.style.removeProperty(t),l=s=X(this,t)),a===l?null:a===r&&l===n?o:(n=l,o=e(r=a,s))}}(t,r,xe(this,"style."+t,e))).each(function(t,e){var i,r,n,o,a="style."+e,s="end."+a;return function(){var l=he(this,t),h=l.on,c=null==l.value[a]?o||(o=Si(e)):void 0;h===i&&n===c||(r=(i=h).copy()).on(s,n=c),l.on=r}}(this._id,t)):this.styleTween(t,function(t,e,i){var r,n,o=i+"";return function(){var a=X(this,t);return a===o?null:a===r?n:n=e(r=a,i)}}(t,r,e),i).on("end.style."+t,null)},styleTween:function(t,e,i){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==e)return this.tween(r,null);if("function"!=typeof e)throw new Error;return this.tween(r,function(t,e,i){var r,n;function o(){var o=e.apply(this,arguments);return o!==n&&(r=(n=o)&&function(t,e,i){return function(r){this.style.setProperty(t,e.call(this,r),i)}}(t,o,i)),r}return o._value=e,o}(t,e,null==i?"":i))},text:function(t){return this.tween("text","function"==typeof t?function(t){return function(){var e=t(this);this.textContent=null==e?"":e}}(xe(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!=typeof t)throw new Error;return this.tween(e,function(t){var e,i;function r(){var r=t.apply(this,arguments);return r!==i&&(e=(i=r)&&function(t){return function(e){this.textContent=t.call(this,e)}}(r)),e}return r._value=t,r}(t))},remove:function(){return this.on("end.remove",function(t){return function(){var e=this.parentNode;for(var i in this.__transition)if(+i!==t)return;e&&e.removeChild(this)}}(this._id))},tween:function(t,e){var i=this._id;if(t+="",arguments.length<2){for(var r,n=ce(this.node(),i).tween,o=0,a=n.length;o<a;++o)if((r=n[o]).name===t)return r.value;return null}return this.each((null==e?be:Ce)(i,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?xi:vi)(e,t)):ce(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?ki:Ti)(e,t)):ce(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(function(t,e){if("function"!=typeof e)throw new Error;return function(){he(this,t).ease=e}}(e,t)):ce(this.node(),e).ease},easeVarying:function(t){if("function"!=typeof t)throw new Error;return this.each(function(t,e){return function(){var i=e.apply(this,arguments);if("function"!=typeof i)throw new Error;he(this,t).ease=i}}(this._id,t))},end:function(){var t,e,i=this,r=i._id,n=i.size();return new Promise((function(o,a){var s={value:a},l={value:function(){0==--n&&o()}};i.each((function(){var i=he(this,r),n=i.on;n!==t&&((e=(t=n).copy())._.cancel.push(s),e._.interrupt.push(s),e._.end.push(l)),i.on=e})),0===n&&o()}))},[Symbol.iterator]:Mi[Symbol.iterator]};var Ai={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};function Ei(t,e){for(var i;!(i=t.__transition)||!(i=i[e]);)if(!(t=t.parentNode))throw new Error(`transition ${e} not found`);return i}At.prototype.interrupt=function(t){return this.each((function(){!function(t,e){var i,r,n,o=t.__transition,a=!0;if(o){for(n in e=null==e?null:e+"",o)(i=o[n]).name===e?(r=i.state>2&&i.state<5,i.state=6,i.timer.stop(),i.on.call(r?"interrupt":"cancel",t,t.__data__,i.index,i.group),delete o[n]):a=!1;a&&delete t.__transition}}(this,t)}))},At.prototype.transition=function(t){var e,i;t instanceof Fi?(e=t._id,t=t._name):(e=Li(),(i=Ai).time=Gt(),t=null==t?null:t+"");for(var r=this._groups,n=r.length,o=0;o<n;++o)for(var a,s=r[o],l=s.length,h=0;h<l;++h)(a=s[h])&&se(a,t,e,h,s,i||Ei(a,e));return new Fi(r,this._parents,t,e)};const{abs:Oi,max:Ii,min:Ni}=Math;function qi(t){return{type:t}}function Di(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.text()}function $i(t){return(e,i)=>function(t,e){return fetch(t,e).then(Di)}(e,i).then((e=>(new DOMParser).parseFromString(e,t)))}["w","e"].map(qi),["n","s"].map(qi),["n","w","e","s","nw","ne","sw","se"].map(qi),$i("application/xml"),$i("text/html");var Zi=$i("image/svg+xml");const zi=Math.PI/180,ji=180/Math.PI,Pi=.96422,Ri=1,Wi=.82521,Ui=4/29,Hi=6/29,Yi=3*Hi*Hi,Vi=Hi*Hi*Hi;function Gi(t){if(t instanceof Xi)return new Xi(t.l,t.a,t.b,t.opacity);if(t instanceof ir)return rr(t);t instanceof Ue||(t=Re(t));var e,i,r=tr(t.r),n=tr(t.g),o=tr(t.b),a=Ji((.2225045*r+.7168786*n+.0606169*o)/Ri);return r===n&&n===o?e=i=a:(e=Ji((.4360747*r+.3850649*n+.1430804*o)/Pi),i=Ji((.0139322*r+.0971045*n+.7141733*o)/Wi)),new Xi(116*a-16,500*(e-a),200*(a-i),t.opacity)}function Xi(t,e,i,r){this.l=+t,this.a=+e,this.b=+i,this.opacity=+r}function Ji(t){return t>Vi?Math.pow(t,1/3):t/Yi+Ui}function Qi(t){return t>Hi?t*t*t:Yi*(t-Ui)}function Ki(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function tr(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function er(t,e,i,r){return 1===arguments.length?function(t){if(t instanceof ir)return new ir(t.h,t.c,t.l,t.opacity);if(t instanceof Xi||(t=Gi(t)),0===t.a&&0===t.b)return new ir(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*ji;return new ir(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}(t):new ir(t,e,i,null==r?1:r)}function ir(t,e,i,r){this.h=+t,this.c=+e,this.l=+i,this.opacity=+r}function rr(t){if(isNaN(t.h))return new Xi(t.l,0,0,t.opacity);var e=t.h*zi;return new Xi(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}function nr(t){return function(e,i){var r=t((e=er(e)).h,(i=er(i)).h),n=ai(e.c,i.c),o=ai(e.l,i.l),a=ai(e.opacity,i.opacity);return function(t){return e.h=r(t),e.c=n(t),e.l=o(t),e.opacity=a(t),e+""}}}ve(Xi,(function(t,e,i,r){return 1===arguments.length?Gi(t):new Xi(t,e,i,null==r?1:r)}),ke(Te,{brighter(t){return new Xi(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker(t){return new Xi(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,i=isNaN(this.b)?t:t-this.b/200;return new Ue(Ki(3.1338561*(e=Pi*Qi(e))-1.6168667*(t=Ri*Qi(t))-.4906146*(i=Wi*Qi(i))),Ki(-.9787684*e+1.9161415*t+.033454*i),Ki(.0719453*e-.2289914*t+1.4052427*i),this.opacity)}})),ve(ir,er,ke(Te,{brighter(t){return new ir(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker(t){return new ir(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb(){return rr(this).rgb()}}));var or=nr((function(t,e){var i=e-t;return i?oi(t,i>180||i<-180?i-360*Math.round(i/360):i):ni(isNaN(t)?e:t)}));nr(ai);const ar=Math.sqrt(50),sr=Math.sqrt(10),lr=Math.sqrt(2);function hr(t,e,i){const r=(e-t)/Math.max(0,i),n=Math.floor(Math.log10(r)),o=r/Math.pow(10,n),a=o>=ar?10:o>=sr?5:o>=lr?2:1;let s,l,h;return n<0?(h=Math.pow(10,-n)/a,s=Math.round(t*h),l=Math.round(e*h),s/h<t&&++s,l/h>e&&--l,h=-h):(h=Math.pow(10,n)*a,s=Math.round(t/h),l=Math.round(e/h),s*h<t&&++s,l*h>e&&--l),l<s&&.5<=i&&i<2?hr(t,e,2*i):[s,l,h]}function cr(t,e,i){return hr(t=+t,e=+e,i=+i)[2]}function ur(t,e,i){i=+i;const r=(e=+e)<(t=+t),n=r?cr(e,t,i):cr(t,e,i);return(r?-1:1)*(n<0?1/-n:n)}function dr(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function fr(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function pr(t){let e,i,r;function n(t,r,n=0,o=t.length){if(n<o){if(0!==e(r,r))return o;do{const e=n+o>>>1;i(t[e],r)<0?n=e+1:o=e}while(n<o)}return n}return 2!==t.length?(e=dr,i=(e,i)=>dr(t(e),i),r=(e,i)=>t(e)-i):(e=t===dr||t===fr?t:gr,i=t,r=t),{left:n,center:function(t,e,i=0,o=t.length){const a=n(t,e,i,o-1);return a>i&&r(t[a-1],e)>-r(t[a],e)?a-1:a},right:function(t,r,n=0,o=t.length){if(n<o){if(0!==e(r,r))return o;do{const e=n+o>>>1;i(t[e],r)<=0?n=e+1:o=e}while(n<o)}return n}}}function gr(){return 0}const mr=pr(dr),yr=mr.right;mr.left,pr((function(t){return null===t?NaN:+t})).center;var _r=yr;function br(t,e){var i,r=e?e.length:0,n=t?Math.min(r,t.length):0,o=new Array(n),a=new Array(r);for(i=0;i<n;++i)o[i]=kr(t[i],e[i]);for(;i<r;++i)a[i]=e[i];return function(t){for(i=0;i<n;++i)a[i]=o[i](t);return a}}function Cr(t,e){var i=new Date;return t=+t,e=+e,function(r){return i.setTime(t*(1-r)+e*r),i}}function xr(t,e){var i,r={},n={};for(i in null!==t&&"object"==typeof t||(t={}),null!==e&&"object"==typeof e||(e={}),e)i in t?r[i]=kr(t[i],e[i]):n[i]=e[i];return function(t){for(i in r)n[i]=r[i](t);return n}}function vr(t,e){e||(e=[]);var i,r=t?Math.min(e.length,t.length):0,n=e.slice();return function(o){for(i=0;i<r;++i)n[i]=t[i]*(1-o)+e[i]*o;return n}}function kr(t,e){var i,r,n=typeof e;return null==e||"boolean"===n?ni(e):("number"===n?ue:"string"===n?(i=ze(e))?(e=i,si):ui:e instanceof ze?si:e instanceof Date?Cr:(r=e,!ArrayBuffer.isView(r)||r instanceof DataView?Array.isArray(e)?br:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?xr:ue:vr))(t,e)}function Tr(t,e){return t=+t,e=+e,function(i){return Math.round(t*(1-i)+e*i)}}function wr(t){return+t}var Sr=[0,1];function Br(t){return t}function Fr(t,e){return(e-=t=+t)?function(i){return(i-t)/e}:(i=isNaN(e)?NaN:.5,function(){return i});var i}function Lr(t,e,i){var r=t[0],n=t[1],o=e[0],a=e[1];return n<r?(r=Fr(n,r),o=i(a,o)):(r=Fr(r,n),o=i(o,a)),function(t){return o(r(t))}}function Mr(t,e,i){var r=Math.min(t.length,e.length)-1,n=new Array(r),o=new Array(r),a=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<r;)n[a]=Fr(t[a],t[a+1]),o[a]=i(e[a],e[a+1]);return function(e){var i=_r(t,e,1,r)-1;return o[i](n[i](e))}}function Ar(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function Er(){return function(){var t,e,i,r,n,o,a=Sr,s=Sr,l=kr,h=Br;function c(){var t,e,i,l=Math.min(a.length,s.length);return h!==Br&&(t=a[0],e=a[l-1],t>e&&(i=t,t=e,e=i),h=function(i){return Math.max(t,Math.min(e,i))}),r=l>2?Mr:Lr,n=o=null,u}function u(e){return null==e||isNaN(e=+e)?i:(n||(n=r(a.map(t),s,l)))(t(h(e)))}return u.invert=function(i){return h(e((o||(o=r(s,a.map(t),ue)))(i)))},u.domain=function(t){return arguments.length?(a=Array.from(t,wr),c()):a.slice()},u.range=function(t){return arguments.length?(s=Array.from(t),c()):s.slice()},u.rangeRound=function(t){return s=Array.from(t),l=Tr,c()},u.clamp=function(t){return arguments.length?(h=!!t||Br,c()):h!==Br},u.interpolate=function(t){return arguments.length?(l=t,c()):l},u.unknown=function(t){return arguments.length?(i=t,u):i},function(i,r){return t=i,e=r,c()}}()(Br,Br)}function Or(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}var Ir,Nr=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function qr(t){if(!(e=Nr.exec(t)))throw new Error("invalid format: "+t);var e;return new Dr({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function Dr(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function $r(t,e){if((i=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var i,r=t.slice(0,i);return[r.length>1?r[0]+r.slice(2):r,+t.slice(i+1)]}function Zr(t){return(t=$r(Math.abs(t)))?t[1]:NaN}function zr(t,e){var i=$r(t,e);if(!i)return t+"";var r=i[0],n=i[1];return n<0?"0."+new Array(-n).join("0")+r:r.length>n+1?r.slice(0,n+1)+"."+r.slice(n+1):r+new Array(n-r.length+2).join("0")}qr.prototype=Dr.prototype,Dr.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var jr={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>zr(100*t,e),r:zr,s:function(t,e){var i=$r(t,e);if(!i)return t+"";var r=i[0],n=i[1],o=n-(Ir=3*Math.max(-8,Math.min(8,Math.floor(n/3))))+1,a=r.length;return o===a?r:o>a?r+new Array(o-a+1).join("0"):o>0?r.slice(0,o)+"."+r.slice(o):"0."+new Array(1-o).join("0")+$r(t,Math.max(0,e+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function Pr(t){return t}var Rr,Wr,Ur,Hr=Array.prototype.map,Yr=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Vr(t){var e=t.domain;return t.ticks=function(t){var i=e();return function(t,e,i){if(!((i=+i)>0))return[];if((t=+t)==(e=+e))return[t];const r=e<t,[n,o,a]=r?hr(e,t,i):hr(t,e,i);if(!(o>=n))return[];const s=o-n+1,l=new Array(s);if(r)if(a<0)for(let t=0;t<s;++t)l[t]=(o-t)/-a;else for(let t=0;t<s;++t)l[t]=(o-t)*a;else if(a<0)for(let t=0;t<s;++t)l[t]=(n+t)/-a;else for(let t=0;t<s;++t)l[t]=(n+t)*a;return l}(i[0],i[i.length-1],null==t?10:t)},t.tickFormat=function(t,i){var r=e();return function(t,e,i,r){var n,o=ur(t,e,i);switch((r=qr(null==r?",f":r)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(e));return null!=r.precision||isNaN(n=function(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Zr(e)/3)))-Zr(Math.abs(t)))}(o,a))||(r.precision=n),Ur(r,a);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(n=function(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,Zr(e)-Zr(t))+1}(o,Math.max(Math.abs(t),Math.abs(e))))||(r.precision=n-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(n=function(t){return Math.max(0,-Zr(Math.abs(t)))}(o))||(r.precision=n-2*("%"===r.type))}return Wr(r)}(r[0],r[r.length-1],null==t?10:t,i)},t.nice=function(i){null==i&&(i=10);var r,n,o=e(),a=0,s=o.length-1,l=o[a],h=o[s],c=10;for(h<l&&(n=l,l=h,h=n,n=a,a=s,s=n);c-- >0;){if((n=cr(l,h,i))===r)return o[a]=l,o[s]=h,e(o);if(n>0)l=Math.floor(l/n)*n,h=Math.ceil(h/n)*n;else{if(!(n<0))break;l=Math.ceil(l*n)/n,h=Math.floor(h*n)/n}r=n}return t},t}function Gr(){var t=Er();return t.copy=function(){return Ar(t,Gr())},Or.apply(t,arguments),Vr(t)}Rr=function(t){var e,i,r=void 0===t.grouping||void 0===t.thousands?Pr:(e=Hr.call(t.grouping,Number),i=t.thousands+"",function(t,r){for(var n=t.length,o=[],a=0,s=e[0],l=0;n>0&&s>0&&(l+s+1>r&&(s=Math.max(1,r-l)),o.push(t.substring(n-=s,n+s)),!((l+=s+1)>r));)s=e[a=(a+1)%e.length];return o.reverse().join(i)}),n=void 0===t.currency?"":t.currency[0]+"",o=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",s=void 0===t.numerals?Pr:function(t){return function(e){return e.replace(/[0-9]/g,(function(e){return t[+e]}))}}(Hr.call(t.numerals,String)),l=void 0===t.percent?"%":t.percent+"",h=void 0===t.minus?"−":t.minus+"",c=void 0===t.nan?"NaN":t.nan+"";function u(t){var e=(t=qr(t)).fill,i=t.align,u=t.sign,d=t.symbol,f=t.zero,p=t.width,g=t.comma,m=t.precision,y=t.trim,_=t.type;"n"===_?(g=!0,_="g"):jr[_]||(void 0===m&&(m=12),y=!0,_="g"),(f||"0"===e&&"="===i)&&(f=!0,e="0",i="=");var b="$"===d?n:"#"===d&&/[boxX]/.test(_)?"0"+_.toLowerCase():"",C="$"===d?o:/[%p]/.test(_)?l:"",x=jr[_],v=/[defgprs%]/.test(_);function k(t){var n,o,l,d=b,k=C;if("c"===_)k=x(t)+k,t="";else{var T=(t=+t)<0||1/t<0;if(t=isNaN(t)?c:x(Math.abs(t),m),y&&(t=function(t){t:for(var e,i=t.length,r=1,n=-1;r<i;++r)switch(t[r]){case".":n=e=r;break;case"0":0===n&&(n=r),e=r;break;default:if(!+t[r])break t;n>0&&(n=0)}return n>0?t.slice(0,n)+t.slice(e+1):t}(t)),T&&0==+t&&"+"!==u&&(T=!1),d=(T?"("===u?u:h:"-"===u||"("===u?"":u)+d,k=("s"===_?Yr[8+Ir/3]:"")+k+(T&&"("===u?")":""),v)for(n=-1,o=t.length;++n<o;)if(48>(l=t.charCodeAt(n))||l>57){k=(46===l?a+t.slice(n+1):t.slice(n))+k,t=t.slice(0,n);break}}g&&!f&&(t=r(t,1/0));var w=d.length+t.length+k.length,S=w<p?new Array(p-w+1).join(e):"";switch(g&&f&&(t=r(S+t,S.length?p-k.length:1/0),S=""),i){case"<":t=d+t+k+S;break;case"=":t=d+S+t+k;break;case"^":t=S.slice(0,w=S.length>>1)+d+t+k+S.slice(w);break;default:t=S+d+t+k}return s(t)}return m=void 0===m?6:/[gprs]/.test(_)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),k.toString=function(){return t+""},k}return{format:u,formatPrefix:function(t,e){var i=u(((t=qr(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(Zr(e)/3))),n=Math.pow(10,-r),o=Yr[8+r/3];return function(t){return i(n*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]}),Wr=Rr.format,Ur=Rr.formatPrefix;class Xr extends Map{constructor(t,e=Qr){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[e,i]of t)this.set(e,i)}get(t){return super.get(Jr(this,t))}has(t){return super.has(Jr(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},i){const r=e(i);return t.has(r)?t.get(r):(t.set(r,i),i)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},i){const r=e(i);return t.has(r)&&(i=t.get(r),t.delete(r)),i}(this,t))}}function Jr({_intern:t,_key:e},i){const r=e(i);return t.has(r)?t.get(r):i}function Qr(t){return null!==t&&"object"==typeof t?t.valueOf():t}const Kr=Symbol("implicit");function tn(){var t=new Xr,e=[],i=[],r=Kr;function n(n){let o=t.get(n);if(void 0===o){if(r!==Kr)return r;t.set(n,o=e.push(n)-1)}return i[o%i.length]}return n.domain=function(i){if(!arguments.length)return e.slice();e=[],t=new Xr;for(const r of i)t.has(r)||t.set(r,e.push(r)-1);return n},n.range=function(t){return arguments.length?(i=Array.from(t),n):i.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return tn(e,i).unknown(r)},Or.apply(n,arguments),n}const en=1e3,rn=6e4,nn=36e5,on=864e5,an=6048e5,sn=31536e6,ln=new Date,hn=new Date;function cn(t,e,i,r){function n(e){return t(e=0===arguments.length?new Date:new Date(+e)),e}return n.floor=e=>(t(e=new Date(+e)),e),n.ceil=i=>(t(i=new Date(i-1)),e(i,1),t(i),i),n.round=t=>{const e=n(t),i=n.ceil(t);return t-e<i-t?e:i},n.offset=(t,i)=>(e(t=new Date(+t),null==i?1:Math.floor(i)),t),n.range=(i,r,o)=>{const a=[];if(i=n.ceil(i),o=null==o?1:Math.floor(o),!(i<r&&o>0))return a;let s;do{a.push(s=new Date(+i)),e(i,o),t(i)}while(s<i&&i<r);return a},n.filter=i=>cn((e=>{if(e>=e)for(;t(e),!i(e);)e.setTime(e-1)}),((t,r)=>{if(t>=t)if(r<0)for(;++r<=0;)for(;e(t,-1),!i(t););else for(;--r>=0;)for(;e(t,1),!i(t););})),i&&(n.count=(e,r)=>(ln.setTime(+e),hn.setTime(+r),t(ln),t(hn),Math.floor(i(ln,hn))),n.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?n.filter(r?e=>r(e)%t==0:e=>n.count(0,e)%t==0):n:null)),n}const un=cn((()=>{}),((t,e)=>{t.setTime(+t+e)}),((t,e)=>e-t));un.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?cn((e=>{e.setTime(Math.floor(e/t)*t)}),((e,i)=>{e.setTime(+e+i*t)}),((e,i)=>(i-e)/t)):un:null),un.range;const dn=cn((t=>{t.setTime(t-t.getMilliseconds())}),((t,e)=>{t.setTime(+t+e*en)}),((t,e)=>(e-t)/en),(t=>t.getUTCSeconds())),fn=(dn.range,cn((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*en)}),((t,e)=>{t.setTime(+t+e*rn)}),((t,e)=>(e-t)/rn),(t=>t.getMinutes()))),pn=(fn.range,cn((t=>{t.setUTCSeconds(0,0)}),((t,e)=>{t.setTime(+t+e*rn)}),((t,e)=>(e-t)/rn),(t=>t.getUTCMinutes()))),gn=(pn.range,cn((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*en-t.getMinutes()*rn)}),((t,e)=>{t.setTime(+t+e*nn)}),((t,e)=>(e-t)/nn),(t=>t.getHours()))),mn=(gn.range,cn((t=>{t.setUTCMinutes(0,0,0)}),((t,e)=>{t.setTime(+t+e*nn)}),((t,e)=>(e-t)/nn),(t=>t.getUTCHours()))),yn=(mn.range,cn((t=>t.setHours(0,0,0,0)),((t,e)=>t.setDate(t.getDate()+e)),((t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*rn)/on),(t=>t.getDate()-1))),_n=(yn.range,cn((t=>{t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+e)}),((t,e)=>(e-t)/on),(t=>t.getUTCDate()-1))),bn=(_n.range,cn((t=>{t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+e)}),((t,e)=>(e-t)/on),(t=>Math.floor(t/on))));function Cn(t){return cn((e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)}),((t,e)=>{t.setDate(t.getDate()+7*e)}),((t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*rn)/an))}bn.range;const xn=Cn(0),vn=Cn(1),kn=Cn(2),Tn=Cn(3),wn=Cn(4),Sn=Cn(5),Bn=Cn(6);function Fn(t){return cn((e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)}),((t,e)=>(e-t)/an))}xn.range,vn.range,kn.range,Tn.range,wn.range,Sn.range,Bn.range;const Ln=Fn(0),Mn=Fn(1),An=Fn(2),En=Fn(3),On=Fn(4),In=Fn(5),Nn=Fn(6),qn=(Ln.range,Mn.range,An.range,En.range,On.range,In.range,Nn.range,cn((t=>{t.setDate(1),t.setHours(0,0,0,0)}),((t,e)=>{t.setMonth(t.getMonth()+e)}),((t,e)=>e.getMonth()-t.getMonth()+12*(e.getFullYear()-t.getFullYear())),(t=>t.getMonth()))),Dn=(qn.range,cn((t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)}),((t,e)=>e.getUTCMonth()-t.getUTCMonth()+12*(e.getUTCFullYear()-t.getUTCFullYear())),(t=>t.getUTCMonth()))),$n=(Dn.range,cn((t=>{t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,e)=>{t.setFullYear(t.getFullYear()+e)}),((t,e)=>e.getFullYear()-t.getFullYear()),(t=>t.getFullYear())));$n.every=t=>isFinite(t=Math.floor(t))&&t>0?cn((e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)}),((e,i)=>{e.setFullYear(e.getFullYear()+i*t)})):null,$n.range;const Zn=cn((t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)}),((t,e)=>e.getUTCFullYear()-t.getUTCFullYear()),(t=>t.getUTCFullYear()));function zn(t,e,i,r,n,o){const a=[[dn,1,en],[dn,5,5e3],[dn,15,15e3],[dn,30,3e4],[o,1,rn],[o,5,3e5],[o,15,9e5],[o,30,18e5],[n,1,nn],[n,3,108e5],[n,6,216e5],[n,12,432e5],[r,1,on],[r,2,1728e5],[i,1,an],[e,1,2592e6],[e,3,7776e6],[t,1,sn]];function s(e,i,r){const n=Math.abs(i-e)/r,o=pr((([,,t])=>t)).right(a,n);if(o===a.length)return t.every(ur(e/sn,i/sn,r));if(0===o)return un.every(Math.max(ur(e,i,r),1));const[s,l]=a[n/a[o-1][2]<a[o][2]/n?o-1:o];return s.every(l)}return[function(t,e,i){const r=e<t;r&&([t,e]=[e,t]);const n=i&&"function"==typeof i.range?i:s(t,e,i),o=n?n.range(t,+e+1):[];return r?o.reverse():o},s]}Zn.every=t=>isFinite(t=Math.floor(t))&&t>0?cn((e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)}),((e,i)=>{e.setUTCFullYear(e.getUTCFullYear()+i*t)})):null,Zn.range;const[jn,Pn]=zn(Zn,Dn,Ln,bn,mn,pn),[Rn,Wn]=zn($n,qn,xn,yn,gn,fn);function Un(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function Hn(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function Yn(t,e,i){return{y:t,m:e,d:i,H:0,M:0,S:0,L:0}}var Vn,Gn,Xn={"-":"",_:" ",0:"0"},Jn=/^\s*\d+/,Qn=/^%/,Kn=/[\\^$*+?|[\]().{}]/g;function to(t,e,i){var r=t<0?"-":"",n=(r?-t:t)+"",o=n.length;return r+(o<i?new Array(i-o+1).join(e)+n:n)}function eo(t){return t.replace(Kn,"\\$&")}function io(t){return new RegExp("^(?:"+t.map(eo).join("|")+")","i")}function ro(t){return new Map(t.map(((t,e)=>[t.toLowerCase(),e])))}function no(t,e,i){var r=Jn.exec(e.slice(i,i+1));return r?(t.w=+r[0],i+r[0].length):-1}function oo(t,e,i){var r=Jn.exec(e.slice(i,i+1));return r?(t.u=+r[0],i+r[0].length):-1}function ao(t,e,i){var r=Jn.exec(e.slice(i,i+2));return r?(t.U=+r[0],i+r[0].length):-1}function so(t,e,i){var r=Jn.exec(e.slice(i,i+2));return r?(t.V=+r[0],i+r[0].length):-1}function lo(t,e,i){var r=Jn.exec(e.slice(i,i+2));return r?(t.W=+r[0],i+r[0].length):-1}function ho(t,e,i){var r=Jn.exec(e.slice(i,i+4));return r?(t.y=+r[0],i+r[0].length):-1}function co(t,e,i){var r=Jn.exec(e.slice(i,i+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),i+r[0].length):-1}function uo(t,e,i){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(i,i+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),i+r[0].length):-1}function fo(t,e,i){var r=Jn.exec(e.slice(i,i+1));return r?(t.q=3*r[0]-3,i+r[0].length):-1}function po(t,e,i){var r=Jn.exec(e.slice(i,i+2));return r?(t.m=r[0]-1,i+r[0].length):-1}function go(t,e,i){var r=Jn.exec(e.slice(i,i+2));return r?(t.d=+r[0],i+r[0].length):-1}function mo(t,e,i){var r=Jn.exec(e.slice(i,i+3));return r?(t.m=0,t.d=+r[0],i+r[0].length):-1}function yo(t,e,i){var r=Jn.exec(e.slice(i,i+2));return r?(t.H=+r[0],i+r[0].length):-1}function _o(t,e,i){var r=Jn.exec(e.slice(i,i+2));return r?(t.M=+r[0],i+r[0].length):-1}function bo(t,e,i){var r=Jn.exec(e.slice(i,i+2));return r?(t.S=+r[0],i+r[0].length):-1}function Co(t,e,i){var r=Jn.exec(e.slice(i,i+3));return r?(t.L=+r[0],i+r[0].length):-1}function xo(t,e,i){var r=Jn.exec(e.slice(i,i+6));return r?(t.L=Math.floor(r[0]/1e3),i+r[0].length):-1}function vo(t,e,i){var r=Qn.exec(e.slice(i,i+1));return r?i+r[0].length:-1}function ko(t,e,i){var r=Jn.exec(e.slice(i));return r?(t.Q=+r[0],i+r[0].length):-1}function To(t,e,i){var r=Jn.exec(e.slice(i));return r?(t.s=+r[0],i+r[0].length):-1}function wo(t,e){return to(t.getDate(),e,2)}function So(t,e){return to(t.getHours(),e,2)}function Bo(t,e){return to(t.getHours()%12||12,e,2)}function Fo(t,e){return to(1+yn.count($n(t),t),e,3)}function Lo(t,e){return to(t.getMilliseconds(),e,3)}function Mo(t,e){return Lo(t,e)+"000"}function Ao(t,e){return to(t.getMonth()+1,e,2)}function Eo(t,e){return to(t.getMinutes(),e,2)}function Oo(t,e){return to(t.getSeconds(),e,2)}function Io(t){var e=t.getDay();return 0===e?7:e}function No(t,e){return to(xn.count($n(t)-1,t),e,2)}function qo(t){var e=t.getDay();return e>=4||0===e?wn(t):wn.ceil(t)}function Do(t,e){return t=qo(t),to(wn.count($n(t),t)+(4===$n(t).getDay()),e,2)}function $o(t){return t.getDay()}function Zo(t,e){return to(vn.count($n(t)-1,t),e,2)}function zo(t,e){return to(t.getFullYear()%100,e,2)}function jo(t,e){return to((t=qo(t)).getFullYear()%100,e,2)}function Po(t,e){return to(t.getFullYear()%1e4,e,4)}function Ro(t,e){var i=t.getDay();return to((t=i>=4||0===i?wn(t):wn.ceil(t)).getFullYear()%1e4,e,4)}function Wo(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+to(e/60|0,"0",2)+to(e%60,"0",2)}function Uo(t,e){return to(t.getUTCDate(),e,2)}function Ho(t,e){return to(t.getUTCHours(),e,2)}function Yo(t,e){return to(t.getUTCHours()%12||12,e,2)}function Vo(t,e){return to(1+_n.count(Zn(t),t),e,3)}function Go(t,e){return to(t.getUTCMilliseconds(),e,3)}function Xo(t,e){return Go(t,e)+"000"}function Jo(t,e){return to(t.getUTCMonth()+1,e,2)}function Qo(t,e){return to(t.getUTCMinutes(),e,2)}function Ko(t,e){return to(t.getUTCSeconds(),e,2)}function ta(t){var e=t.getUTCDay();return 0===e?7:e}function ea(t,e){return to(Ln.count(Zn(t)-1,t),e,2)}function ia(t){var e=t.getUTCDay();return e>=4||0===e?On(t):On.ceil(t)}function ra(t,e){return t=ia(t),to(On.count(Zn(t),t)+(4===Zn(t).getUTCDay()),e,2)}function na(t){return t.getUTCDay()}function oa(t,e){return to(Mn.count(Zn(t)-1,t),e,2)}function aa(t,e){return to(t.getUTCFullYear()%100,e,2)}function sa(t,e){return to((t=ia(t)).getUTCFullYear()%100,e,2)}function la(t,e){return to(t.getUTCFullYear()%1e4,e,4)}function ha(t,e){var i=t.getUTCDay();return to((t=i>=4||0===i?On(t):On.ceil(t)).getUTCFullYear()%1e4,e,4)}function ca(){return"+0000"}function ua(){return"%"}function da(t){return+t}function fa(t){return Math.floor(+t/1e3)}function pa(t){return new Date(t)}function ga(t){return t instanceof Date?+t:+new Date(+t)}function ma(t,e,i,r,n,o,a,s,l,h){var c=Er(),u=c.invert,d=c.domain,f=h(".%L"),p=h(":%S"),g=h("%I:%M"),m=h("%I %p"),y=h("%a %d"),_=h("%b %d"),b=h("%B"),C=h("%Y");function x(t){return(l(t)<t?f:s(t)<t?p:a(t)<t?g:o(t)<t?m:r(t)<t?n(t)<t?y:_:i(t)<t?b:C)(t)}return c.invert=function(t){return new Date(u(t))},c.domain=function(t){return arguments.length?d(Array.from(t,ga)):d().map(pa)},c.ticks=function(e){var i=d();return t(i[0],i[i.length-1],null==e?10:e)},c.tickFormat=function(t,e){return null==e?x:h(e)},c.nice=function(t){var i=d();return t&&"function"==typeof t.range||(t=e(i[0],i[i.length-1],null==t?10:t)),t?d(function(t,e){var i,r=0,n=(t=t.slice()).length-1,o=t[r],a=t[n];return a<o&&(i=r,r=n,n=i,i=o,o=a,a=i),t[r]=e.floor(o),t[n]=e.ceil(a),t}(i,t)):c},c.copy=function(){return Ar(c,ma(t,e,i,r,n,o,a,s,l,h))},c}function ya(){return Or.apply(ma(Rn,Wn,$n,qn,xn,yn,gn,fn,dn,Gn).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function _a(t){return"string"==typeof t?new Lt([[document.querySelector(t)]],[document.documentElement]):new Lt([[t]],Ft)}function ba(t){return"string"==typeof t?new Lt([document.querySelectorAll(t)],[document.documentElement]):new Lt([x(t)],Ft)}function Ca(t){return function(){return t}}Vn=function(t){var e=t.dateTime,i=t.date,r=t.time,n=t.periods,o=t.days,a=t.shortDays,s=t.months,l=t.shortMonths,h=io(n),c=ro(n),u=io(o),d=ro(o),f=io(a),p=ro(a),g=io(s),m=ro(s),y=io(l),_=ro(l),b={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return l[t.getMonth()]},B:function(t){return s[t.getMonth()]},c:null,d:wo,e:wo,f:Mo,g:jo,G:Ro,H:So,I:Bo,j:Fo,L:Lo,m:Ao,M:Eo,p:function(t){return n[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:da,s:fa,S:Oo,u:Io,U:No,V:Do,w:$o,W:Zo,x:null,X:null,y:zo,Y:Po,Z:Wo,"%":ua},C={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return l[t.getUTCMonth()]},B:function(t){return s[t.getUTCMonth()]},c:null,d:Uo,e:Uo,f:Xo,g:sa,G:ha,H:Ho,I:Yo,j:Vo,L:Go,m:Jo,M:Qo,p:function(t){return n[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:da,s:fa,S:Ko,u:ta,U:ea,V:ra,w:na,W:oa,x:null,X:null,y:aa,Y:la,Z:ca,"%":ua},x={a:function(t,e,i){var r=f.exec(e.slice(i));return r?(t.w=p.get(r[0].toLowerCase()),i+r[0].length):-1},A:function(t,e,i){var r=u.exec(e.slice(i));return r?(t.w=d.get(r[0].toLowerCase()),i+r[0].length):-1},b:function(t,e,i){var r=y.exec(e.slice(i));return r?(t.m=_.get(r[0].toLowerCase()),i+r[0].length):-1},B:function(t,e,i){var r=g.exec(e.slice(i));return r?(t.m=m.get(r[0].toLowerCase()),i+r[0].length):-1},c:function(t,i,r){return T(t,e,i,r)},d:go,e:go,f:xo,g:co,G:ho,H:yo,I:yo,j:mo,L:Co,m:po,M:_o,p:function(t,e,i){var r=h.exec(e.slice(i));return r?(t.p=c.get(r[0].toLowerCase()),i+r[0].length):-1},q:fo,Q:ko,s:To,S:bo,u:oo,U:ao,V:so,w:no,W:lo,x:function(t,e,r){return T(t,i,e,r)},X:function(t,e,i){return T(t,r,e,i)},y:co,Y:ho,Z:uo,"%":vo};function v(t,e){return function(i){var r,n,o,a=[],s=-1,l=0,h=t.length;for(i instanceof Date||(i=new Date(+i));++s<h;)37===t.charCodeAt(s)&&(a.push(t.slice(l,s)),null!=(n=Xn[r=t.charAt(++s)])?r=t.charAt(++s):n="e"===r?" ":"0",(o=e[r])&&(r=o(i,n)),a.push(r),l=s+1);return a.push(t.slice(l,s)),a.join("")}}function k(t,e){return function(i){var r,n,o=Yn(1900,void 0,1);if(T(o,t,i+="",0)!=i.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(e&&!("Z"in o)&&(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(n=(r=Hn(Yn(o.y,0,1))).getUTCDay(),r=n>4||0===n?Mn.ceil(r):Mn(r),r=_n.offset(r,7*(o.V-1)),o.y=r.getUTCFullYear(),o.m=r.getUTCMonth(),o.d=r.getUTCDate()+(o.w+6)%7):(n=(r=Un(Yn(o.y,0,1))).getDay(),r=n>4||0===n?vn.ceil(r):vn(r),r=yn.offset(r,7*(o.V-1)),o.y=r.getFullYear(),o.m=r.getMonth(),o.d=r.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:"W"in o?1:0),n="Z"in o?Hn(Yn(o.y,0,1)).getUTCDay():Un(Yn(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(n+5)%7:o.w+7*o.U-(n+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,Hn(o)):Un(o)}}function T(t,e,i,r){for(var n,o,a=0,s=e.length,l=i.length;a<s;){if(r>=l)return-1;if(37===(n=e.charCodeAt(a++))){if(n=e.charAt(a++),!(o=x[n in Xn?e.charAt(a++):n])||(r=o(t,i,r))<0)return-1}else if(n!=i.charCodeAt(r++))return-1}return r}return b.x=v(i,b),b.X=v(r,b),b.c=v(e,b),C.x=v(i,C),C.X=v(r,C),C.c=v(e,C),{format:function(t){var e=v(t+="",b);return e.toString=function(){return t},e},parse:function(t){var e=k(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=v(t+="",C);return e.toString=function(){return t},e},utcParse:function(t){var e=k(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]}),Gn=Vn.format,Vn.parse,Vn.utcFormat,Vn.utcParse;const xa=Math.abs,va=Math.atan2,ka=Math.cos,Ta=Math.max,wa=Math.min,Sa=Math.sin,Ba=Math.sqrt,Fa=1e-12,La=Math.PI,Ma=La/2,Aa=2*La;function Ea(t){return t>=1?Ma:t<=-1?-Ma:Math.asin(t)}const Oa=Math.PI,Ia=2*Oa,Na=1e-6,qa=Ia-Na;function Da(t){this._+=t[0];for(let e=1,i=t.length;e<i;++e)this._+=arguments[e]+t[e]}class $a{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?Da:function(t){let e=Math.floor(t);if(!(e>=0))throw new Error(`invalid digits: ${t}`);if(e>15)return Da;const i=10**e;return function(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=Math.round(arguments[e]*i)/i+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,i,r){this._append`Q${+t},${+e},${this._x1=+i},${this._y1=+r}`}bezierCurveTo(t,e,i,r,n,o){this._append`C${+t},${+e},${+i},${+r},${this._x1=+n},${this._y1=+o}`}arcTo(t,e,i,r,n){if(t=+t,e=+e,i=+i,r=+r,(n=+n)<0)throw new Error(`negative radius: ${n}`);let o=this._x1,a=this._y1,s=i-t,l=r-e,h=o-t,c=a-e,u=h*h+c*c;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(u>Na)if(Math.abs(c*s-l*h)>Na&&n){let d=i-o,f=r-a,p=s*s+l*l,g=d*d+f*f,m=Math.sqrt(p),y=Math.sqrt(u),_=n*Math.tan((Oa-Math.acos((p+u-g)/(2*m*y)))/2),b=_/y,C=_/m;Math.abs(b-1)>Na&&this._append`L${t+b*h},${e+b*c}`,this._append`A${n},${n},0,0,${+(c*d>h*f)},${this._x1=t+C*s},${this._y1=e+C*l}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,i,r,n,o){if(t=+t,e=+e,o=!!o,(i=+i)<0)throw new Error(`negative radius: ${i}`);let a=i*Math.cos(r),s=i*Math.sin(r),l=t+a,h=e+s,c=1^o,u=o?r-n:n-r;null===this._x1?this._append`M${l},${h}`:(Math.abs(this._x1-l)>Na||Math.abs(this._y1-h)>Na)&&this._append`L${l},${h}`,i&&(u<0&&(u=u%Ia+Ia),u>qa?this._append`A${i},${i},0,1,${c},${t-a},${e-s}A${i},${i},0,1,${c},${this._x1=l},${this._y1=h}`:u>Na&&this._append`A${i},${i},0,${+(u>=Oa)},${c},${this._x1=t+i*Math.cos(n)},${this._y1=e+i*Math.sin(n)}`)}rect(t,e,i,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${i=+i}v${+r}h${-i}Z`}toString(){return this._}}function Za(t){let e=3;return t.digits=function(i){if(!arguments.length)return e;if(null==i)e=null;else{const t=Math.floor(i);if(!(t>=0))throw new RangeError(`invalid digits: ${i}`);e=t}return t},()=>new $a(e)}function za(t){return t.innerRadius}function ja(t){return t.outerRadius}function Pa(t){return t.startAngle}function Ra(t){return t.endAngle}function Wa(t){return t&&t.padAngle}function Ua(t,e,i,r,n,o,a){var s=t-i,l=e-r,h=(a?o:-o)/Ba(s*s+l*l),c=h*l,u=-h*s,d=t+c,f=e+u,p=i+c,g=r+u,m=(d+p)/2,y=(f+g)/2,_=p-d,b=g-f,C=_*_+b*b,x=n-o,v=d*g-p*f,k=(b<0?-1:1)*Ba(Ta(0,x*x*C-v*v)),T=(v*b-_*k)/C,w=(-v*_-b*k)/C,S=(v*b+_*k)/C,B=(-v*_+b*k)/C,F=T-m,L=w-y,M=S-m,A=B-y;return F*F+L*L>M*M+A*A&&(T=S,w=B),{cx:T,cy:w,x01:-c,y01:-u,x11:T*(n/x-1),y11:w*(n/x-1)}}function Ha(){var t=za,e=ja,i=Ca(0),r=null,n=Pa,o=Ra,a=Wa,s=null,l=Za(h);function h(){var h,c,u,d=+t.apply(this,arguments),f=+e.apply(this,arguments),p=n.apply(this,arguments)-Ma,g=o.apply(this,arguments)-Ma,m=xa(g-p),y=g>p;if(s||(s=h=l()),f<d&&(c=f,f=d,d=c),f>Fa)if(m>Aa-Fa)s.moveTo(f*ka(p),f*Sa(p)),s.arc(0,0,f,p,g,!y),d>Fa&&(s.moveTo(d*ka(g),d*Sa(g)),s.arc(0,0,d,g,p,y));else{var _,b,C=p,x=g,v=p,k=g,T=m,w=m,S=a.apply(this,arguments)/2,B=S>Fa&&(r?+r.apply(this,arguments):Ba(d*d+f*f)),F=wa(xa(f-d)/2,+i.apply(this,arguments)),L=F,M=F;if(B>Fa){var A=Ea(B/d*Sa(S)),E=Ea(B/f*Sa(S));(T-=2*A)>Fa?(v+=A*=y?1:-1,k-=A):(T=0,v=k=(p+g)/2),(w-=2*E)>Fa?(C+=E*=y?1:-1,x-=E):(w=0,C=x=(p+g)/2)}var O=f*ka(C),I=f*Sa(C),N=d*ka(k),q=d*Sa(k);if(F>Fa){var D,$=f*ka(x),Z=f*Sa(x),z=d*ka(v),j=d*Sa(v);if(m<La)if(D=function(t,e,i,r,n,o,a,s){var l=i-t,h=r-e,c=a-n,u=s-o,d=u*l-c*h;if(!(d*d<Fa))return[t+(d=(c*(e-o)-u*(t-n))/d)*l,e+d*h]}(O,I,z,j,$,Z,N,q)){var P=O-D[0],R=I-D[1],W=$-D[0],U=Z-D[1],H=1/Sa(((u=(P*W+R*U)/(Ba(P*P+R*R)*Ba(W*W+U*U)))>1?0:u<-1?La:Math.acos(u))/2),Y=Ba(D[0]*D[0]+D[1]*D[1]);L=wa(F,(d-Y)/(H-1)),M=wa(F,(f-Y)/(H+1))}else L=M=0}w>Fa?M>Fa?(_=Ua(z,j,O,I,f,M,y),b=Ua($,Z,N,q,f,M,y),s.moveTo(_.cx+_.x01,_.cy+_.y01),M<F?s.arc(_.cx,_.cy,M,va(_.y01,_.x01),va(b.y01,b.x01),!y):(s.arc(_.cx,_.cy,M,va(_.y01,_.x01),va(_.y11,_.x11),!y),s.arc(0,0,f,va(_.cy+_.y11,_.cx+_.x11),va(b.cy+b.y11,b.cx+b.x11),!y),s.arc(b.cx,b.cy,M,va(b.y11,b.x11),va(b.y01,b.x01),!y))):(s.moveTo(O,I),s.arc(0,0,f,C,x,!y)):s.moveTo(O,I),d>Fa&&T>Fa?L>Fa?(_=Ua(N,q,$,Z,d,-L,y),b=Ua(O,I,z,j,d,-L,y),s.lineTo(_.cx+_.x01,_.cy+_.y01),L<F?s.arc(_.cx,_.cy,L,va(_.y01,_.x01),va(b.y01,b.x01),!y):(s.arc(_.cx,_.cy,L,va(_.y01,_.x01),va(_.y11,_.x11),!y),s.arc(0,0,d,va(_.cy+_.y11,_.cx+_.x11),va(b.cy+b.y11,b.cx+b.x11),y),s.arc(b.cx,b.cy,L,va(b.y11,b.x11),va(b.y01,b.x01),!y))):s.arc(0,0,d,k,v,y):s.lineTo(N,q)}else s.moveTo(0,0);if(s.closePath(),h)return s=null,h+""||null}return h.centroid=function(){var i=(+t.apply(this,arguments)+ +e.apply(this,arguments))/2,r=(+n.apply(this,arguments)+ +o.apply(this,arguments))/2-La/2;return[ka(r)*i,Sa(r)*i]},h.innerRadius=function(e){return arguments.length?(t="function"==typeof e?e:Ca(+e),h):t},h.outerRadius=function(t){return arguments.length?(e="function"==typeof t?t:Ca(+t),h):e},h.cornerRadius=function(t){return arguments.length?(i="function"==typeof t?t:Ca(+t),h):i},h.padRadius=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:Ca(+t),h):r},h.startAngle=function(t){return arguments.length?(n="function"==typeof t?t:Ca(+t),h):n},h.endAngle=function(t){return arguments.length?(o="function"==typeof t?t:Ca(+t),h):o},h.padAngle=function(t){return arguments.length?(a="function"==typeof t?t:Ca(+t),h):a},h.context=function(t){return arguments.length?(s=null==t?null:t,h):s},h}function Ya(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function Va(t){this._context=t}function Ga(t){return new Va(t)}function Xa(t){return t[0]}function Ja(t){return t[1]}function Qa(t,e){var i=Ca(!0),r=null,n=Ga,o=null,a=Za(s);function s(s){var l,h,c,u=(s=Ya(s)).length,d=!1;for(null==r&&(o=n(c=a())),l=0;l<=u;++l)!(l<u&&i(h=s[l],l,s))===d&&((d=!d)?o.lineStart():o.lineEnd()),d&&o.point(+t(h,l,s),+e(h,l,s));if(c)return o=null,c+""||null}return t="function"==typeof t?t:void 0===t?Xa:Ca(t),e="function"==typeof e?e:void 0===e?Ja:Ca(e),s.x=function(e){return arguments.length?(t="function"==typeof e?e:Ca(+e),s):t},s.y=function(t){return arguments.length?(e="function"==typeof t?t:Ca(+t),s):e},s.defined=function(t){return arguments.length?(i="function"==typeof t?t:Ca(!!t),s):i},s.curve=function(t){return arguments.length?(n=t,null!=r&&(o=n(r)),s):n},s.context=function(t){return arguments.length?(null==t?r=o=null:o=n(r=t),s):r},s}function Ka(t,e){return e<t?-1:e>t?1:e>=t?0:NaN}function ts(t){return t}function es(){var t=ts,e=Ka,i=null,r=Ca(0),n=Ca(Aa),o=Ca(0);function a(a){var s,l,h,c,u,d=(a=Ya(a)).length,f=0,p=new Array(d),g=new Array(d),m=+r.apply(this,arguments),y=Math.min(Aa,Math.max(-Aa,n.apply(this,arguments)-m)),_=Math.min(Math.abs(y)/d,o.apply(this,arguments)),b=_*(y<0?-1:1);for(s=0;s<d;++s)(u=g[p[s]=s]=+t(a[s],s,a))>0&&(f+=u);for(null!=e?p.sort((function(t,i){return e(g[t],g[i])})):null!=i&&p.sort((function(t,e){return i(a[t],a[e])})),s=0,h=f?(y-d*b)/f:0;s<d;++s,m=c)l=p[s],c=m+((u=g[l])>0?u*h:0)+b,g[l]={data:a[l],index:s,value:u,startAngle:m,endAngle:c,padAngle:_};return g}return a.value=function(e){return arguments.length?(t="function"==typeof e?e:Ca(+e),a):t},a.sortValues=function(t){return arguments.length?(e=t,i=null,a):e},a.sort=function(t){return arguments.length?(i=t,e=null,a):i},a.startAngle=function(t){return arguments.length?(r="function"==typeof t?t:Ca(+t),a):r},a.endAngle=function(t){return arguments.length?(n="function"==typeof t?t:Ca(+t),a):n},a.padAngle=function(t){return arguments.length?(o="function"==typeof t?t:Ca(+t),a):o},a}function is(){}function rs(t,e,i){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+i)/6)}function ns(t){this._context=t}function os(t){return new ns(t)}function as(t){this._context=t}function ss(t){return new as(t)}function ls(t){this._context=t}function hs(t){return new ls(t)}$a.prototype,Array.prototype.slice,Va.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},ns.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:rs(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:rs(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},as.prototype={areaStart:is,areaEnd:is,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:rs(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},ls.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var i=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(i,r):this._context.moveTo(i,r);break;case 3:this._point=4;default:rs(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class cs{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function us(t){return new cs(t,!0)}function ds(t){return new cs(t,!1)}function fs(t,e){this._basis=new ns(t),this._beta=e}fs.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,e=this._y,i=t.length-1;if(i>0)for(var r,n=t[0],o=e[0],a=t[i]-n,s=e[i]-o,l=-1;++l<=i;)r=l/i,this._basis.point(this._beta*t[l]+(1-this._beta)*(n+r*a),this._beta*e[l]+(1-this._beta)*(o+r*s));this._x=this._y=null,this._basis.lineEnd()},point:function(t,e){this._x.push(+t),this._y.push(+e)}};var ps=function t(e){function i(t){return 1===e?new ns(t):new fs(t,e)}return i.beta=function(e){return t(+e)},i}(.85);function gs(t,e,i){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-e),t._y2+t._k*(t._y1-i),t._x2,t._y2)}function ms(t,e){this._context=t,this._k=(1-e)/6}ms.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:gs(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2,this._x1=t,this._y1=e;break;case 2:this._point=3;default:gs(this,t,e)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};var ys=function t(e){function i(t){return new ms(t,e)}return i.tension=function(e){return t(+e)},i}(0);function _s(t,e){this._context=t,this._k=(1-e)/6}_s.prototype={areaStart:is,areaEnd:is,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x3=t,this._y3=e;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=e);break;case 2:this._point=3,this._x5=t,this._y5=e;break;default:gs(this,t,e)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};var bs=function t(e){function i(t){return new _s(t,e)}return i.tension=function(e){return t(+e)},i}(0);function Cs(t,e){this._context=t,this._k=(1-e)/6}Cs.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:gs(this,t,e)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};var xs=function t(e){function i(t){return new Cs(t,e)}return i.tension=function(e){return t(+e)},i}(0);function vs(t,e,i){var r=t._x1,n=t._y1,o=t._x2,a=t._y2;if(t._l01_a>Fa){var s=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,l=3*t._l01_a*(t._l01_a+t._l12_a);r=(r*s-t._x0*t._l12_2a+t._x2*t._l01_2a)/l,n=(n*s-t._y0*t._l12_2a+t._y2*t._l01_2a)/l}if(t._l23_a>Fa){var h=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,c=3*t._l23_a*(t._l23_a+t._l12_a);o=(o*h+t._x1*t._l23_2a-e*t._l12_2a)/c,a=(a*h+t._y1*t._l23_2a-i*t._l12_2a)/c}t._context.bezierCurveTo(r,n,o,a,t._x2,t._y2)}function ks(t,e){this._context=t,this._alpha=e}ks.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){if(t=+t,e=+e,this._point){var i=this._x2-t,r=this._y2-e;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(i*i+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3;default:vs(this,t,e)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};var Ts=function t(e){function i(t){return e?new ks(t,e):new ms(t,0)}return i.alpha=function(e){return t(+e)},i}(.5);function ws(t,e){this._context=t,this._alpha=e}ws.prototype={areaStart:is,areaEnd:is,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,e){if(t=+t,e=+e,this._point){var i=this._x2-t,r=this._y2-e;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(i*i+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=e;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=e);break;case 2:this._point=3,this._x5=t,this._y5=e;break;default:vs(this,t,e)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};var Ss=function t(e){function i(t){return e?new ws(t,e):new _s(t,0)}return i.alpha=function(e){return t(+e)},i}(.5);function Bs(t,e){this._context=t,this._alpha=e}Bs.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){if(t=+t,e=+e,this._point){var i=this._x2-t,r=this._y2-e;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(i*i+r*r,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:vs(this,t,e)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};var Fs=function t(e){function i(t){return e?new Bs(t,e):new Cs(t,0)}return i.alpha=function(e){return t(+e)},i}(.5);function Ls(t){this._context=t}function Ms(t){return new Ls(t)}function As(t){return t<0?-1:1}function Es(t,e,i){var r=t._x1-t._x0,n=e-t._x1,o=(t._y1-t._y0)/(r||n<0&&-0),a=(i-t._y1)/(n||r<0&&-0),s=(o*n+a*r)/(r+n);return(As(o)+As(a))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs(s))||0}function Os(t,e){var i=t._x1-t._x0;return i?(3*(t._y1-t._y0)/i-e)/2:e}function Is(t,e,i){var r=t._x0,n=t._y0,o=t._x1,a=t._y1,s=(o-r)/3;t._context.bezierCurveTo(r+s,n+s*e,o-s,a-s*i,o,a)}function Ns(t){this._context=t}function qs(t){this._context=new Ds(t)}function Ds(t){this._context=t}function $s(t){return new Ns(t)}function Zs(t){return new qs(t)}function zs(t){this._context=t}function js(t){var e,i,r=t.length-1,n=new Array(r),o=new Array(r),a=new Array(r);for(n[0]=0,o[0]=2,a[0]=t[0]+2*t[1],e=1;e<r-1;++e)n[e]=1,o[e]=4,a[e]=4*t[e]+2*t[e+1];for(n[r-1]=2,o[r-1]=7,a[r-1]=8*t[r-1]+t[r],e=1;e<r;++e)i=n[e]/o[e-1],o[e]-=i,a[e]-=i*a[e-1];for(n[r-1]=a[r-1]/o[r-1],e=r-2;e>=0;--e)n[e]=(a[e]-n[e+1])/o[e];for(o[r-1]=(t[r]+n[r-1])/2,e=0;e<r-1;++e)o[e]=2*t[e+1]-n[e+1];return[n,o]}function Ps(t){return new zs(t)}function Rs(t,e){this._context=t,this._t=e}function Ws(t){return new Rs(t,.5)}function Us(t){return new Rs(t,0)}function Hs(t){return new Rs(t,1)}function Ys(t,e,i){this.k=t,this.x=e,this.y=i}Ls.prototype={areaStart:is,areaEnd:is,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},Ns.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Is(this,this._t0,Os(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var i=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,Is(this,Os(this,i=Es(this,t,e)),i);break;default:Is(this,this._t0,i=Es(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=i}}},(qs.prototype=Object.create(Ns.prototype)).point=function(t,e){Ns.prototype.point.call(this,e,t)},Ds.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,i,r,n,o){this._context.bezierCurveTo(e,t,r,i,o,n)}},zs.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,i=t.length;if(i)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===i)this._context.lineTo(t[1],e[1]);else for(var r=js(t),n=js(e),o=0,a=1;a<i;++o,++a)this._context.bezierCurveTo(r[0][o],n[0][o],r[1][o],n[1][o],t[a],e[a]);(this._line||0!==this._line&&1===i)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},Rs.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var i=this._x*(1-this._t)+t*this._t;this._context.lineTo(i,this._y),this._context.lineTo(i,e)}}this._x=t,this._y=e}},Ys.prototype={constructor:Ys,scale:function(t){return 1===t?this:new Ys(this.k*t,this.x,this.y)},translate:function(t,e){return 0===t&0===e?this:new Ys(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}},new Ys(1,0,0),Ys.prototype},4549:function(t,e,i){"use strict";i.d(e,{Z:function(){return o}});var r=i(5971),n=i(2142),o=new class{constructor(t,e){this.color=e,this.changed=!1,this.data=t,this.type=new class{constructor(){this.type=n.w.ALL}get(){return this.type}set(t){if(this.type&&this.type!==t)throw new Error("Cannot change both RGB and HSL channels at the same time");this.type=t}reset(){this.type=n.w.ALL}is(t){return this.type===t}}}set(t,e){return this.color=e,this.changed=!1,this.data=t,this.type.type=n.w.ALL,this}_ensureHSL(){const t=this.data,{h:e,s:i,l:n}=t;void 0===e&&(t.h=r.Z.channel.rgb2hsl(t,"h")),void 0===i&&(t.s=r.Z.channel.rgb2hsl(t,"s")),void 0===n&&(t.l=r.Z.channel.rgb2hsl(t,"l"))}_ensureRGB(){const t=this.data,{r:e,g:i,b:n}=t;void 0===e&&(t.r=r.Z.channel.hsl2rgb(t,"r")),void 0===i&&(t.g=r.Z.channel.hsl2rgb(t,"g")),void 0===n&&(t.b=r.Z.channel.hsl2rgb(t,"b"))}get r(){const t=this.data,e=t.r;return this.type.is(n.w.HSL)||void 0===e?(this._ensureHSL(),r.Z.channel.hsl2rgb(t,"r")):e}get g(){const t=this.data,e=t.g;return this.type.is(n.w.HSL)||void 0===e?(this._ensureHSL(),r.Z.channel.hsl2rgb(t,"g")):e}get b(){const t=this.data,e=t.b;return this.type.is(n.w.HSL)||void 0===e?(this._ensureHSL(),r.Z.channel.hsl2rgb(t,"b")):e}get h(){const t=this.data,e=t.h;return this.type.is(n.w.RGB)||void 0===e?(this._ensureRGB(),r.Z.channel.rgb2hsl(t,"h")):e}get s(){const t=this.data,e=t.s;return this.type.is(n.w.RGB)||void 0===e?(this._ensureRGB(),r.Z.channel.rgb2hsl(t,"s")):e}get l(){const t=this.data,e=t.l;return this.type.is(n.w.RGB)||void 0===e?(this._ensureRGB(),r.Z.channel.rgb2hsl(t,"l")):e}get a(){return this.data.a}set r(t){this.type.set(n.w.RGB),this.changed=!0,this.data.r=t}set g(t){this.type.set(n.w.RGB),this.changed=!0,this.data.g=t}set b(t){this.type.set(n.w.RGB),this.changed=!0,this.data.b=t}set h(t){this.type.set(n.w.HSL),this.changed=!0,this.data.h=t}set s(t){this.type.set(n.w.HSL),this.changed=!0,this.data.s=t}set l(t){this.type.set(n.w.HSL),this.changed=!0,this.data.l=t}set a(t){this.changed=!0,this.data.a=t}}({r:0,g:0,b:0,a:0},"transparent")},1767:function(t,e,i){"use strict";i.d(e,{Z:function(){return g}});var r=i(4549),n=i(2142);const o={re:/^#((?:[a-f0-9]{2}){2,4}|[a-f0-9]{3})$/i,parse:t=>{if(35!==t.charCodeAt(0))return;const e=t.match(o.re);if(!e)return;const i=e[1],n=parseInt(i,16),a=i.length,s=a%4==0,l=a>4,h=l?1:17,c=l?8:4,u=s?0:-1,d=l?255:15;return r.Z.set({r:(n>>c*(u+3)&d)*h,g:(n>>c*(u+2)&d)*h,b:(n>>c*(u+1)&d)*h,a:s?(n&d)*h/255:1},t)},stringify:t=>{const{r:e,g:i,b:r,a:o}=t;return o<1?`#${n.Q[Math.round(e)]}${n.Q[Math.round(i)]}${n.Q[Math.round(r)]}${n.Q[Math.round(255*o)]}`:`#${n.Q[Math.round(e)]}${n.Q[Math.round(i)]}${n.Q[Math.round(r)]}`}};var a=o,s=i(5971);const l={re:/^hsla?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(?:deg|grad|rad|turn)?)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(%)?))?\s*?\)$/i,hueRe:/^(.+?)(deg|grad|rad|turn)$/i,_hue2deg:t=>{const e=t.match(l.hueRe);if(e){const[,t,i]=e;switch(i){case"grad":return s.Z.channel.clamp.h(.9*parseFloat(t));case"rad":return s.Z.channel.clamp.h(180*parseFloat(t)/Math.PI);case"turn":return s.Z.channel.clamp.h(360*parseFloat(t))}}return s.Z.channel.clamp.h(parseFloat(t))},parse:t=>{const e=t.charCodeAt(0);if(104!==e&&72!==e)return;const i=t.match(l.re);if(!i)return;const[,n,o,a,h,c]=i;return r.Z.set({h:l._hue2deg(n),s:s.Z.channel.clamp.s(parseFloat(o)),l:s.Z.channel.clamp.l(parseFloat(a)),a:h?s.Z.channel.clamp.a(c?parseFloat(h)/100:parseFloat(h)):1},t)},stringify:t=>{const{h:e,s:i,l:r,a:n}=t;return n<1?`hsla(${s.Z.lang.round(e)}, ${s.Z.lang.round(i)}%, ${s.Z.lang.round(r)}%, ${n})`:`hsl(${s.Z.lang.round(e)}, ${s.Z.lang.round(i)}%, ${s.Z.lang.round(r)}%)`}};var h=l;const c={colors:{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyanaqua:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",transparent:"#00000000",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},parse:t=>{t=t.toLowerCase();const e=c.colors[t];if(e)return a.parse(e)},stringify:t=>{const e=a.stringify(t);for(const t in c.colors)if(c.colors[t]===e)return t}};var u=c;const d={re:/^rgba?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?)))?\s*?\)$/i,parse:t=>{const e=t.charCodeAt(0);if(114!==e&&82!==e)return;const i=t.match(d.re);if(!i)return;const[,n,o,a,l,h,c,u,f]=i;return r.Z.set({r:s.Z.channel.clamp.r(o?2.55*parseFloat(n):parseFloat(n)),g:s.Z.channel.clamp.g(l?2.55*parseFloat(a):parseFloat(a)),b:s.Z.channel.clamp.b(c?2.55*parseFloat(h):parseFloat(h)),a:u?s.Z.channel.clamp.a(f?parseFloat(u)/100:parseFloat(u)):1},t)},stringify:t=>{const{r:e,g:i,b:r,a:n}=t;return n<1?`rgba(${s.Z.lang.round(e)}, ${s.Z.lang.round(i)}, ${s.Z.lang.round(r)}, ${s.Z.lang.round(n)})`:`rgb(${s.Z.lang.round(e)}, ${s.Z.lang.round(i)}, ${s.Z.lang.round(r)})`}};var f=d;const p={format:{keyword:c,hex:a,rgb:d,rgba:d,hsl:l,hsla:l},parse:t=>{if("string"!=typeof t)return t;const e=a.parse(t)||f.parse(t)||h.parse(t)||u.parse(t);if(e)return e;throw new Error(`Unsupported color format: "${t}"`)},stringify:t=>!t.changed&&t.color?t.color:t.type.is(n.w.HSL)||void 0===t.data.r?h.stringify(t):t.a<1||!Number.isInteger(t.r)||!Number.isInteger(t.g)||!Number.isInteger(t.b)?f.stringify(t):a.stringify(t)};var g=p},2142:function(t,e,i){"use strict";i.d(e,{Q:function(){return n},w:function(){return o}});var r=i(5971);const n={};for(let t=0;t<=255;t++)n[t]=r.Z.unit.dec2hex(t);const o={ALL:0,RGB:1,HSL:2}},6174:function(t,e,i){"use strict";var r=i(5971),n=i(1767);e.Z=(t,e,i)=>{const o=n.Z.parse(t),a=o[e],s=r.Z.channel.clamp[e](a+i);return a!==s&&(o[e]=s),n.Z.stringify(o)}},3438:function(t,e,i){"use strict";var r=i(5971),n=i(1767);e.Z=(t,e)=>{const i=n.Z.parse(t);for(const t in e)i[t]=r.Z.channel.clamp[t](e[t]);return n.Z.stringify(i)}},7201:function(t,e,i){"use strict";var r=i(6174);e.Z=(t,e)=>(0,r.Z)(t,"l",-e)},6500:function(t,e,i){"use strict";i.d(e,{Z:function(){return a}});var r=i(5971),n=i(1767),o=t=>(t=>{const{r:e,g:i,b:o}=n.Z.parse(t),a=.2126*r.Z.channel.toLinear(e)+.7152*r.Z.channel.toLinear(i)+.0722*r.Z.channel.toLinear(o);return r.Z.lang.round(a)})(t)>=.5,a=t=>!o(t)},2281:function(t,e,i){"use strict";var r=i(6174);e.Z=(t,e)=>(0,r.Z)(t,"l",e)},1117:function(t,e,i){"use strict";var r=i(5971),n=i(4549),o=i(1767),a=i(3438);e.Z=(t,e,i=0,s=1)=>{if("number"!=typeof t)return(0,a.Z)(t,{a:e});const l=n.Z.set({r:r.Z.channel.clamp.r(t),g:r.Z.channel.clamp.g(e),b:r.Z.channel.clamp.b(i),a:r.Z.channel.clamp.a(s)});return o.Z.stringify(l)}},5971:function(t,e,i){"use strict";i.d(e,{Z:function(){return n}});const r={min:{r:0,g:0,b:0,s:0,l:0,a:0},max:{r:255,g:255,b:255,h:360,s:100,l:100,a:1},clamp:{r:t=>t>=255?255:t<0?0:t,g:t=>t>=255?255:t<0?0:t,b:t=>t>=255?255:t<0?0:t,h:t=>t%360,s:t=>t>=100?100:t<0?0:t,l:t=>t>=100?100:t<0?0:t,a:t=>t>=1?1:t<0?0:t},toLinear:t=>{const e=t/255;return t>.03928?Math.pow((e+.055)/1.055,2.4):e/12.92},hue2rgb:(t,e,i)=>(i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t),hsl2rgb:({h:t,s:e,l:i},n)=>{if(!e)return 2.55*i;t/=360,e/=100;const o=(i/=100)<.5?i*(1+e):i+e-i*e,a=2*i-o;switch(n){case"r":return 255*r.hue2rgb(a,o,t+1/3);case"g":return 255*r.hue2rgb(a,o,t);case"b":return 255*r.hue2rgb(a,o,t-1/3)}},rgb2hsl:({r:t,g:e,b:i},r)=>{t/=255,e/=255,i/=255;const n=Math.max(t,e,i),o=Math.min(t,e,i),a=(n+o)/2;if("l"===r)return 100*a;if(n===o)return 0;const s=n-o;if("s"===r)return 100*(a>.5?s/(2-n-o):s/(n+o));switch(n){case t:return 60*((e-i)/s+(e<i?6:0));case e:return 60*((i-t)/s+2);case i:return 60*((t-e)/s+4);default:return-1}}};var n={channel:r,lang:{clamp:(t,e,i)=>e>i?Math.min(e,Math.max(i,t)):Math.min(i,Math.max(e,t)),round:t=>Math.round(1e10*t)/1e10},unit:{dec2hex:t=>{const e=Math.round(t).toString(16);return e.length>1?e:`0${e}`}}}},2536:function(t,e,i){"use strict";i.d(e,{Z:function(){return s}});var r=i(9651),n=function(t,e){for(var i=t.length;i--;)if((0,r.Z)(t[i][0],e))return i;return-1},o=Array.prototype.splice;function a(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}a.prototype.clear=function(){this.__data__=[],this.size=0},a.prototype.delete=function(t){var e=this.__data__,i=n(e,t);return!(i<0||(i==e.length-1?e.pop():o.call(e,i,1),--this.size,0))},a.prototype.get=function(t){var e=this.__data__,i=n(e,t);return i<0?void 0:e[i][1]},a.prototype.has=function(t){return n(this.__data__,t)>-1},a.prototype.set=function(t,e){var i=this.__data__,r=n(i,t);return r<0?(++this.size,i.push([t,e])):i[r][1]=e,this};var s=a},6183:function(t,e,i){"use strict";var r=i(2119),n=i(6092),o=(0,r.Z)(n.Z,"Map");e.Z=o},520:function(t,e,i){"use strict";i.d(e,{Z:function(){return d}});var r=(0,i(2119).Z)(Object,"create"),n=Object.prototype.hasOwnProperty,o=Object.prototype.hasOwnProperty;function a(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}a.prototype.clear=function(){this.__data__=r?r(null):{},this.size=0},a.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},a.prototype.get=function(t){var e=this.__data__;if(r){var i=e[t];return"__lodash_hash_undefined__"===i?void 0:i}return n.call(e,t)?e[t]:void 0},a.prototype.has=function(t){var e=this.__data__;return r?void 0!==e[t]:o.call(e,t)},a.prototype.set=function(t,e){var i=this.__data__;return this.size+=this.has(t)?0:1,i[t]=r&&void 0===e?"__lodash_hash_undefined__":e,this};var s=a,l=i(2536),h=i(6183),c=function(t,e){var i,r,n=t.__data__;return("string"==(r=typeof(i=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==i:null===i)?n["string"==typeof e?"string":"hash"]:n.map};function u(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}u.prototype.clear=function(){this.size=0,this.__data__={hash:new s,map:new(h.Z||l.Z),string:new s}},u.prototype.delete=function(t){var e=c(this,t).delete(t);return this.size-=e?1:0,e},u.prototype.get=function(t){return c(this,t).get(t)},u.prototype.has=function(t){return c(this,t).has(t)},u.prototype.set=function(t,e){var i=c(this,t),r=i.size;return i.set(t,e),this.size+=i.size==r?0:1,this};var d=u},3203:function(t,e,i){"use strict";var r=i(2119),n=i(6092),o=(0,r.Z)(n.Z,"Set");e.Z=o},7685:function(t,e,i){"use strict";var r=i(6092).Z.Symbol;e.Z=r},1922:function(t,e,i){"use strict";i.d(e,{Z:function(){return c}});var r=i(7685),n=Object.prototype,o=n.hasOwnProperty,a=n.toString,s=r.Z?r.Z.toStringTag:void 0,l=Object.prototype.toString,h=r.Z?r.Z.toStringTag:void 0,c=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":h&&h in Object(t)?function(t){var e=o.call(t,s),i=t[s];try{t[s]=void 0;var r=!0}catch(t){}var n=a.call(t);return r&&(e?t[s]=i:delete t[s]),n}(t):function(t){return l.call(t)}(t)}},8448:function(t,e,i){"use strict";i.d(e,{Z:function(){return a}});var r=i(2764),n=(0,i(1851).Z)(Object.keys,Object),o=Object.prototype.hasOwnProperty,a=function(t){if(!(0,r.Z)(t))return n(t);var e=[];for(var i in Object(t))o.call(t,i)&&"constructor"!=i&&e.push(i);return e}},1162:function(t,e){"use strict";e.Z=function(t){return function(e){return t(e)}}},3413:function(t,e){"use strict";var i="object"==typeof global&&global&&global.Object===Object&&global;e.Z=i},2119:function(t,e,i){"use strict";i.d(e,{Z:function(){return m}});var r,n=i(3234),o=i(6092).Z["__core-js_shared__"],a=(r=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"",s=i(7226),l=i(19),h=/^\[object .+?Constructor\]$/,c=Function.prototype,u=Object.prototype,d=c.toString,f=u.hasOwnProperty,p=RegExp("^"+d.call(f).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),g=function(t){return!(!(0,s.Z)(t)||(e=t,a&&a in e))&&((0,n.Z)(t)?p:h).test((0,l.Z)(t));var e},m=function(t,e){var i=function(t,e){return null==t?void 0:t[e]}(t,e);return g(i)?i:void 0}},6155:function(t,e,i){"use strict";i.d(e,{Z:function(){return k}});var r=i(2119),n=i(6092),o=(0,r.Z)(n.Z,"DataView"),a=i(6183),s=(0,r.Z)(n.Z,"Promise"),l=i(3203),h=(0,r.Z)(n.Z,"WeakMap"),c=i(1922),u=i(19),d="[object Map]",f="[object Promise]",p="[object Set]",g="[object WeakMap]",m="[object DataView]",y=(0,u.Z)(o),_=(0,u.Z)(a.Z),b=(0,u.Z)(s),C=(0,u.Z)(l.Z),x=(0,u.Z)(h),v=c.Z;(o&&v(new o(new ArrayBuffer(1)))!=m||a.Z&&v(new a.Z)!=d||s&&v(s.resolve())!=f||l.Z&&v(new l.Z)!=p||h&&v(new h)!=g)&&(v=function(t){var e=(0,c.Z)(t),i="[object Object]"==e?t.constructor:void 0,r=i?(0,u.Z)(i):"";if(r)switch(r){case y:return m;case _:return d;case b:return f;case C:return p;case x:return g}return e});var k=v},2764:function(t,e){"use strict";var i=Object.prototype;e.Z=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||i)}},4254:function(t,e,i){"use strict";var r=i(3413),n="object"==typeof exports&&exports&&!exports.nodeType&&exports,o=n&&"object"==typeof module&&module&&!module.nodeType&&module,a=o&&o.exports===n&&r.Z.process,s=function(){try{return o&&o.require&&o.require("util").types||a&&a.binding&&a.binding("util")}catch(t){}}();e.Z=s},1851:function(t,e){"use strict";e.Z=function(t,e){return function(i){return t(e(i))}}},6092:function(t,e,i){"use strict";var r=i(3413),n="object"==typeof self&&self&&self.Object===Object&&self,o=r.Z||n||Function("return this")();e.Z=o},19:function(t,e){"use strict";var i=Function.prototype.toString;e.Z=function(t){if(null!=t){try{return i.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},9651:function(t,e){"use strict";e.Z=function(t,e){return t===e||t!=t&&e!=e}},4732:function(t,e,i){"use strict";i.d(e,{Z:function(){return c}});var r=i(1922),n=i(8533),o=function(t){return(0,n.Z)(t)&&"[object Arguments]"==(0,r.Z)(t)},a=Object.prototype,s=a.hasOwnProperty,l=a.propertyIsEnumerable,h=o(function(){return arguments}())?o:function(t){return(0,n.Z)(t)&&s.call(t,"callee")&&!l.call(t,"callee")},c=h},7771:function(t,e){"use strict";var i=Array.isArray;e.Z=i},585:function(t,e,i){"use strict";var r=i(3234),n=i(1656);e.Z=function(t){return null!=t&&(0,n.Z)(t.length)&&!(0,r.Z)(t)}},6706:function(t,e,i){"use strict";i.d(e,{Z:function(){return s}});var r=i(6092),n="object"==typeof exports&&exports&&!exports.nodeType&&exports,o=n&&"object"==typeof module&&module&&!module.nodeType&&module,a=o&&o.exports===n?r.Z.Buffer:void 0,s=(a?a.isBuffer:void 0)||function(){return!1}},9697:function(t,e,i){"use strict";var r=i(8448),n=i(6155),o=i(4732),a=i(7771),s=i(585),l=i(6706),h=i(2764),c=i(7212),u=Object.prototype.hasOwnProperty;e.Z=function(t){if(null==t)return!0;if((0,s.Z)(t)&&((0,a.Z)(t)||"string"==typeof t||"function"==typeof t.splice||(0,l.Z)(t)||(0,c.Z)(t)||(0,o.Z)(t)))return!t.length;var e=(0,n.Z)(t);if("[object Map]"==e||"[object Set]"==e)return!t.size;if((0,h.Z)(t))return!(0,r.Z)(t).length;for(var i in t)if(u.call(t,i))return!1;return!0}},3234:function(t,e,i){"use strict";var r=i(1922),n=i(7226);e.Z=function(t){if(!(0,n.Z)(t))return!1;var e=(0,r.Z)(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},1656:function(t,e){"use strict";e.Z=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},7226:function(t,e){"use strict";e.Z=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},8533:function(t,e){"use strict";e.Z=function(t){return null!=t&&"object"==typeof t}},7212:function(t,e,i){"use strict";i.d(e,{Z:function(){return c}});var r=i(1922),n=i(1656),o=i(8533),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1;var s=i(1162),l=i(4254),h=l.Z&&l.Z.isTypedArray,c=h?(0,s.Z)(h):function(t){return(0,o.Z)(t)&&(0,n.Z)(t.length)&&!!a[(0,r.Z)(t)]}},2454:function(t,e,i){"use strict";var r=i(520);function n(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var i=function(){var r=arguments,n=e?e.apply(this,r):r[0],o=i.cache;if(o.has(n))return o.get(n);var a=t.apply(this,r);return i.cache=o.set(n,a)||o,a};return i.cache=new(n.Cache||r.Z),i}n.Cache=r.Z,e.Z=n},5103:function(t,e,i){"use strict";i.d(e,{A:function(){return Zi},B:function(){return _r},C:function(){return _t},D:function(){return Tt},E:function(){return cn},F:function(){return ar},G:function(){return ei},H:function(){return pn},I:function(){return Ai},J:function(){return Oi},K:function(){return Ln},Z:function(){return Ji},a:function(){return Bi},b:function(){return Si},c:function(){return hi},d:function(){return ht},e:function(){return pt},f:function(){return ti},g:function(){return wi},h:function(){return dr},i:function(){return mi},j:function(){return ur},k:function(){return nr},l:function(){return rt},m:function(){return bn},n:function(){return dt},o:function(){return ir},p:function(){return yi},q:function(){return li},r:function(){return Fi},s:function(){return Ti},t:function(){return Li},u:function(){return br},v:function(){return ki},w:function(){return hr},x:function(){return ft},y:function(){return sr},z:function(){return Di}});var r=i(8464),n=i(7484),o=i(7967),a=i(5740),s=i(7856),l=i(1767),h=i(3438),c=(t,e)=>{const i=l.Z.parse(t),r={};for(const t in e)e[t]&&(r[t]=i[t]+e[t]);return(0,h.Z)(t,r)},u=i(1117),d=(t,e=100)=>{const i=l.Z.parse(t);return i.r=255-i.r,i.g=255-i.g,i.b=255-i.b,((t,e,i=50)=>{const{r:r,g:n,b:o,a:a}=l.Z.parse(t),{r:s,g:h,b:c,a:d}=l.Z.parse(e),f=i/100,p=2*f-1,g=a-d,m=((p*g==-1?p:(p+g)/(1+p*g))+1)/2,y=1-m,_=r*m+s*y,b=n*m+h*y,C=o*m+c*y,x=a*f+d*(1-f);return(0,u.Z)(_,b,C,x)})(i,t,e)},f=i(7201),p=i(2281),g=i(6500),m=i(2454),y="comm",_="rule",b="decl",C=Math.abs,x=String.fromCharCode;function v(t){return t.trim()}function k(t,e,i){return t.replace(e,i)}function T(t,e){return t.indexOf(e)}function w(t,e){return 0|t.charCodeAt(e)}function S(t,e,i){return t.slice(e,i)}function B(t){return t.length}function F(t,e){return e.push(t),t}function L(t,e){for(var i="",r=0;r<t.length;r++)i+=e(t[r],r,t,e)||"";return i}function M(t,e,i,r){switch(t.type){case"@layer":if(t.children.length)break;case"@import":case b:return t.return=t.return||t.value;case y:return"";case"@keyframes":return t.return=t.value+"{"+L(t.children,r)+"}";case _:if(!B(t.value=t.props.join(",")))return""}return B(i=L(t.children,r))?t.return=t.value+"{"+i+"}":""}Object.assign;var A=1,E=1,O=0,I=0,N=0,q="";function D(t,e,i,r,n,o,a,s){return{value:t,root:e,parent:i,type:r,props:n,children:o,line:A,column:E,length:a,return:"",siblings:s}}function $(){return N=I>0?w(q,--I):0,E--,10===N&&(E=1,A--),N}function Z(){return N=I<O?w(q,I++):0,E++,10===N&&(E=1,A++),N}function z(){return w(q,I)}function j(){return I}function P(t,e){return S(q,t,e)}function R(t){switch(t){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function W(t){return v(P(I-1,Y(91===t?t+2:40===t?t+1:t)))}function U(t){for(;(N=z())&&N<33;)Z();return R(t)>2||R(N)>3?"":" "}function H(t,e){for(;--e&&Z()&&!(N<48||N>102||N>57&&N<65||N>70&&N<97););return P(t,j()+(e<6&&32==z()&&32==Z()))}function Y(t){for(;Z();)switch(N){case t:return I;case 34:case 39:34!==t&&39!==t&&Y(N);break;case 40:41===t&&Y(t);break;case 92:Z()}return I}function V(t,e){for(;Z()&&t+N!==57&&(t+N!==84||47!==z()););return"/*"+P(e,I-1)+"*"+x(47===t?t:Z())}function G(t){for(;!R(z());)Z();return P(t,I)}function X(t){return function(t){return q="",t}(J("",null,null,null,[""],t=function(t){return A=E=1,O=B(q=t),I=0,[]}(t),0,[0],t))}function J(t,e,i,r,n,o,a,s,l){for(var h=0,c=0,u=a,d=0,f=0,p=0,g=1,m=1,y=1,_=0,b="",C=n,v=o,S=r,L=b;m;)switch(p=_,_=Z()){case 40:if(108!=p&&58==w(L,u-1)){-1!=T(L+=k(W(_),"&","&\f"),"&\f")&&(y=-1);break}case 34:case 39:case 91:L+=W(_);break;case 9:case 10:case 13:case 32:L+=U(p);break;case 92:L+=H(j()-1,7);continue;case 47:switch(z()){case 42:case 47:F(K(V(Z(),j()),e,i,l),l);break;default:L+="/"}break;case 123*g:s[h++]=B(L)*y;case 125*g:case 59:case 0:switch(_){case 0:case 125:m=0;case 59+c:-1==y&&(L=k(L,/\f/g,"")),f>0&&B(L)-u&&F(f>32?tt(L+";",r,i,u-1,l):tt(k(L," ","")+";",r,i,u-2,l),l);break;case 59:L+=";";default:if(F(S=Q(L,e,i,h,c,n,s,b,C=[],v=[],u,o),o),123===_)if(0===c)J(L,e,S,S,C,o,u,s,v);else switch(99===d&&110===w(L,3)?100:d){case 100:case 108:case 109:case 115:J(t,S,S,r&&F(Q(t,S,S,0,0,n,s,b,n,C=[],u,v),v),n,v,u,s,r?C:v);break;default:J(L,S,S,S,[""],v,0,s,v)}}h=c=f=0,g=y=1,b=L="",u=a;break;case 58:u=1+B(L),f=p;default:if(g<1)if(123==_)--g;else if(125==_&&0==g++&&125==$())continue;switch(L+=x(_),_*g){case 38:y=c>0?1:(L+="\f",-1);break;case 44:s[h++]=(B(L)-1)*y,y=1;break;case 64:45===z()&&(L+=W(Z())),d=z(),c=u=B(b=L+=G(j())),_++;break;case 45:45===p&&2==B(L)&&(g=0)}}return o}function Q(t,e,i,r,n,o,a,s,l,h,c,u){for(var d=n-1,f=0===n?o:[""],p=function(t){return t.length}(f),g=0,m=0,y=0;g<r;++g)for(var b=0,x=S(t,d+1,d=C(m=a[g])),T=t;b<p;++b)(T=v(m>0?f[b]+" "+x:k(x,/&\f/g,f[b])))&&(l[y++]=T);return D(t,e,i,0===n?_:s,l,h,c,u)}function K(t,e,i,r){return D(t,e,i,y,x(N),S(t,2,-2),0,r)}function tt(t,e,i,r,n){return D(t,e,i,b,S(t,0,r),S(t,r+1,-1),r,n)}var et=i(9697);const it={trace:0,debug:1,info:2,warn:3,error:4,fatal:5},rt={trace:(...t)=>{},debug:(...t)=>{},info:(...t)=>{},warn:(...t)=>{},error:(...t)=>{},fatal:(...t)=>{}},nt=function(t="fatal"){let e=it.fatal;"string"==typeof t?(t=t.toLowerCase())in it&&(e=it[t]):"number"==typeof t&&(e=t),rt.trace=()=>{},rt.debug=()=>{},rt.info=()=>{},rt.warn=()=>{},rt.error=()=>{},rt.fatal=()=>{},e<=it.fatal&&(rt.fatal=console.error?console.error.bind(console,ot("FATAL"),"color: orange"):console.log.bind(console,"[35m",ot("FATAL"))),e<=it.error&&(rt.error=console.error?console.error.bind(console,ot("ERROR"),"color: orange"):console.log.bind(console,"[31m",ot("ERROR"))),e<=it.warn&&(rt.warn=console.warn?console.warn.bind(console,ot("WARN"),"color: orange"):console.log.bind(console,"[33m",ot("WARN"))),e<=it.info&&(rt.info=console.info?console.info.bind(console,ot("INFO"),"color: lightblue"):console.log.bind(console,"[34m",ot("INFO"))),e<=it.debug&&(rt.debug=console.debug?console.debug.bind(console,ot("DEBUG"),"color: lightgreen"):console.log.bind(console,"[32m",ot("DEBUG"))),e<=it.trace&&(rt.trace=console.debug?console.debug.bind(console,ot("TRACE"),"color: lightgreen"):console.log.bind(console,"[32m",ot("TRACE")))},ot=t=>`%c${n().format("ss.SSS")} : ${t} : `,at=/<br\s*\/?>/gi,st=t=>s.sanitize(t),lt=(t,e)=>{var i;if(!1!==(null==(i=e.flowchart)?void 0:i.htmlLabels)){const i=e.securityLevel;"antiscript"===i||"strict"===i?t=st(t):"loose"!==i&&(t=(t=(t=ut(t)).replace(/</g,"&lt;").replace(/>/g,"&gt;")).replace(/=/g,"&equals;"),t=ct(t))}return t},ht=(t,e)=>t?t=e.dompurifyConfig?s.sanitize(lt(t,e),e.dompurifyConfig).toString():s.sanitize(lt(t,e),{FORBID_TAGS:["style"]}).toString():t,ct=t=>t.replace(/#br#/g,"<br/>"),ut=t=>t.replace(at,"#br#"),dt=t=>!1!==t&&!["false","null","0"].includes(String(t).trim().toLowerCase()),ft=function(t){let e=t;if(t.split("~").length-1>=2){let t=e;do{e=t,t=e.replace(/~([^\s,:;]+)~/,"<$1>")}while(t!=e);return ft(t)}return e},pt={getRows:t=>t?ut(t).replace(/\\n/g,"#br#").split("#br#"):[""],sanitizeText:ht,sanitizeTextOrArray:(t,e)=>"string"==typeof t?ht(t,e):t.flat().map((t=>ht(t,e))),hasBreaks:t=>at.test(t),splitBreaks:t=>t.split(at),lineBreakRegex:at,removeScript:st,getUrl:t=>{let e="";return t&&(e=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,e=e.replaceAll(/\(/g,"\\("),e=e.replaceAll(/\)/g,"\\)")),e},evaluate:dt,getMax:function(...t){const e=t.filter((t=>!isNaN(t)));return Math.max(...e)},getMin:function(...t){const e=t.filter((t=>!isNaN(t)));return Math.min(...e)}},gt=(t,e)=>c(t,e?{s:-40,l:10}:{s:-40,l:-10}),mt="#ffffff",yt="#f2f2f2",_t=t=>{const e=new class{constructor(){this.background="#f4f4f4",this.primaryColor="#ECECFF",this.secondaryColor=c(this.primaryColor,{h:120}),this.secondaryColor="#ffffde",this.tertiaryColor=c(this.primaryColor,{h:-160}),this.primaryBorderColor=gt(this.primaryColor,this.darkMode),this.secondaryBorderColor=gt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=gt(this.tertiaryColor,this.darkMode),this.primaryTextColor=d(this.primaryColor),this.secondaryTextColor=d(this.secondaryColor),this.tertiaryTextColor=d(this.tertiaryColor),this.lineColor=d(this.background),this.textColor=d(this.background),this.background="white",this.mainBkg="#ECECFF",this.secondBkg="#ffffde",this.lineColor="#333333",this.border1="#9370DB",this.border2="#aaaa33",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="#e8e8e8",this.textColor="#333",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="grey",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="calculated",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="calculated",this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor="calculated",this.taskTextOutsideColor=this.taskTextDarkColor,this.taskTextClickableColor="calculated",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBorderColor="calculated",this.critBkgColor="calculated",this.todayLineColor="calculated",this.sectionBkgColor=(0,u.Z)(102,102,255,.49),this.altSectionBkgColor="white",this.sectionBkgColor2="#fff400",this.taskBorderColor="#534fbc",this.taskBkgColor="#8a90dd",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="#534fbc",this.activeTaskBkgColor="#bfc7ff",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222",this.updateColors()}updateColors(){this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||c(this.primaryColor,{h:30}),this.cScale4=this.cScale4||c(this.primaryColor,{h:60}),this.cScale5=this.cScale5||c(this.primaryColor,{h:90}),this.cScale6=this.cScale6||c(this.primaryColor,{h:120}),this.cScale7=this.cScale7||c(this.primaryColor,{h:150}),this.cScale8=this.cScale8||c(this.primaryColor,{h:210}),this.cScale9=this.cScale9||c(this.primaryColor,{h:270}),this.cScale10=this.cScale10||c(this.primaryColor,{h:300}),this.cScale11=this.cScale11||c(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||(0,f.Z)(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||(0,f.Z)(this.tertiaryColor,40);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScale"+t]=(0,f.Z)(this["cScale"+t],10),this["cScalePeer"+t]=this["cScalePeer"+t]||(0,f.Z)(this["cScale"+t],25);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleInv"+t]=this["cScaleInv"+t]||c(this["cScale"+t],{h:180});for(let t=0;t<5;t++)this["surface"+t]=this["surface"+t]||c(this.mainBkg,{h:30,l:-(5+5*t)}),this["surfacePeer"+t]=this["surfacePeer"+t]||c(this.mainBkg,{h:30,l:-(7+5*t)});if(this.scaleLabelColor="calculated"!==this.scaleLabelColor&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor,"calculated"!==this.labelTextColor){this.cScaleLabel0=this.cScaleLabel0||d(this.labelTextColor),this.cScaleLabel3=this.cScaleLabel3||d(this.labelTextColor);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.labelTextColor}this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.textColor,this.edgeLabelBackground=this.labelBackground,this.actorBorder=(0,p.Z)(this.border1,23),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.signalColor=this.textColor,this.signalTextColor=this.textColor,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=c(this.primaryColor,{h:64}),this.fillType3=c(this.secondaryColor,{h:64}),this.fillType4=c(this.primaryColor,{h:-64}),this.fillType5=c(this.secondaryColor,{h:-64}),this.fillType6=c(this.primaryColor,{h:128}),this.fillType7=c(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||c(this.tertiaryColor,{l:-40}),this.pie4=this.pie4||c(this.primaryColor,{l:-10}),this.pie5=this.pie5||c(this.secondaryColor,{l:-30}),this.pie6=this.pie6||c(this.tertiaryColor,{l:-20}),this.pie7=this.pie7||c(this.primaryColor,{h:60,l:-20}),this.pie8=this.pie8||c(this.primaryColor,{h:-60,l:-40}),this.pie9=this.pie9||c(this.primaryColor,{h:120,l:-40}),this.pie10=this.pie10||c(this.primaryColor,{h:60,l:-40}),this.pie11=this.pie11||c(this.primaryColor,{h:-90,l:-40}),this.pie12=this.pie12||c(this.primaryColor,{h:120,l:-30}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||c(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||c(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||c(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||c(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||c(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||c(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||(0,g.Z)(this.quadrant1Fill)?(0,p.Z)(this.quadrant1Fill):(0,f.Z)(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.labelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||c(this.primaryColor,{h:-30}),this.git4=this.git4||c(this.primaryColor,{h:-60}),this.git5=this.git5||c(this.primaryColor,{h:-90}),this.git6=this.git6||c(this.primaryColor,{h:60}),this.git7=this.git7||c(this.primaryColor,{h:120}),this.darkMode?(this.git0=(0,p.Z)(this.git0,25),this.git1=(0,p.Z)(this.git1,25),this.git2=(0,p.Z)(this.git2,25),this.git3=(0,p.Z)(this.git3,25),this.git4=(0,p.Z)(this.git4,25),this.git5=(0,p.Z)(this.git5,25),this.git6=(0,p.Z)(this.git6,25),this.git7=(0,p.Z)(this.git7,25)):(this.git0=(0,f.Z)(this.git0,25),this.git1=(0,f.Z)(this.git1,25),this.git2=(0,f.Z)(this.git2,25),this.git3=(0,f.Z)(this.git3,25),this.git4=(0,f.Z)(this.git4,25),this.git5=(0,f.Z)(this.git5,25),this.git6=(0,f.Z)(this.git6,25),this.git7=(0,f.Z)(this.git7,25)),this.gitInv0=this.gitInv0||(0,f.Z)(d(this.git0),25),this.gitInv1=this.gitInv1||d(this.git1),this.gitInv2=this.gitInv2||d(this.git2),this.gitInv3=this.gitInv3||d(this.git3),this.gitInv4=this.gitInv4||d(this.git4),this.gitInv5=this.gitInv5||d(this.git5),this.gitInv6=this.gitInv6||d(this.git6),this.gitInv7=this.gitInv7||d(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||d(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||d(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||mt,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||yt}calculate(t){if("object"!=typeof t)return void this.updateColors();const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]})),this.updateColors(),e.forEach((e=>{this[e]=t[e]}))}};return e.calculate(t),e};class bt{constructor(){this.primaryColor="#eee",this.contrast="#707070",this.secondaryColor=(0,p.Z)(this.contrast,55),this.background="#ffffff",this.tertiaryColor=c(this.primaryColor,{h:-160}),this.primaryBorderColor=gt(this.primaryColor,this.darkMode),this.secondaryBorderColor=gt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=gt(this.tertiaryColor,this.darkMode),this.primaryTextColor=d(this.primaryColor),this.secondaryTextColor=d(this.secondaryColor),this.tertiaryTextColor=d(this.tertiaryColor),this.lineColor=d(this.background),this.textColor=d(this.background),this.mainBkg="#eee",this.secondBkg="calculated",this.lineColor="#666",this.border1="#999",this.border2="calculated",this.note="#ffa",this.text="#333",this.critical="#d42",this.done="#bbb",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="white",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="calculated",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="white",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBkgColor="calculated",this.critBorderColor="calculated",this.todayLineColor="calculated",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){this.secondBkg=(0,p.Z)(this.contrast,55),this.border2=this.contrast,this.actorBorder=(0,p.Z)(this.border1,23),this.actorBkg=this.mainBkg,this.actorTextColor=this.text,this.actorLineColor=this.lineColor,this.signalColor=this.text,this.signalTextColor=this.text,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.text,this.loopTextColor=this.text,this.noteBorderColor="#999",this.noteBkgColor="#666",this.noteTextColor="#fff",this.cScale0=this.cScale0||"#555",this.cScale1=this.cScale1||"#F4F4F4",this.cScale2=this.cScale2||"#555",this.cScale3=this.cScale3||"#BBB",this.cScale4=this.cScale4||"#777",this.cScale5=this.cScale5||"#999",this.cScale6=this.cScale6||"#DDD",this.cScale7=this.cScale7||"#FFF",this.cScale8=this.cScale8||"#DDD",this.cScale9=this.cScale9||"#BBB",this.cScale10=this.cScale10||"#999",this.cScale11=this.cScale11||"#777";for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleInv"+t]=this["cScaleInv"+t]||d(this["cScale"+t]);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this.darkMode?this["cScalePeer"+t]=this["cScalePeer"+t]||(0,p.Z)(this["cScale"+t],10):this["cScalePeer"+t]=this["cScalePeer"+t]||(0,f.Z)(this["cScale"+t],10);this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor),this.cScaleLabel0=this.cScaleLabel0||this.cScale1,this.cScaleLabel2=this.cScaleLabel2||this.cScale1;for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.scaleLabelColor;for(let t=0;t<5;t++)this["surface"+t]=this["surface"+t]||c(this.mainBkg,{l:-(5+5*t)}),this["surfacePeer"+t]=this["surfacePeer"+t]||c(this.mainBkg,{l:-(8+5*t)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.text,this.sectionBkgColor=(0,p.Z)(this.contrast,30),this.sectionBkgColor2=(0,p.Z)(this.contrast,30),this.taskBorderColor=(0,f.Z)(this.contrast,10),this.taskBkgColor=this.contrast,this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor=this.text,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.gridColor=(0,p.Z)(this.border1,30),this.doneTaskBkgColor=this.done,this.doneTaskBorderColor=this.lineColor,this.critBkgColor=this.critical,this.critBorderColor=(0,f.Z)(this.critBkgColor,10),this.todayLineColor=this.critBkgColor,this.transitionColor=this.transitionColor||"#000",this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f4f4f4",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.stateBorder=this.stateBorder||"#000",this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#222",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=c(this.primaryColor,{h:64}),this.fillType3=c(this.secondaryColor,{h:64}),this.fillType4=c(this.primaryColor,{h:-64}),this.fillType5=c(this.secondaryColor,{h:-64}),this.fillType6=c(this.primaryColor,{h:128}),this.fillType7=c(this.secondaryColor,{h:128});for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["pie"+t]=this["cScale"+t];this.pie12=this.pie0,this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||c(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||c(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||c(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||c(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||c(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||c(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||(0,g.Z)(this.quadrant1Fill)?(0,p.Z)(this.quadrant1Fill):(0,f.Z)(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=(0,f.Z)(this.pie1,25)||this.primaryColor,this.git1=this.pie2||this.secondaryColor,this.git2=this.pie3||this.tertiaryColor,this.git3=this.pie4||c(this.primaryColor,{h:-30}),this.git4=this.pie5||c(this.primaryColor,{h:-60}),this.git5=this.pie6||c(this.primaryColor,{h:-90}),this.git6=this.pie7||c(this.primaryColor,{h:60}),this.git7=this.pie8||c(this.primaryColor,{h:120}),this.gitInv0=this.gitInv0||d(this.git0),this.gitInv1=this.gitInv1||d(this.git1),this.gitInv2=this.gitInv2||d(this.git2),this.gitInv3=this.gitInv3||d(this.git3),this.gitInv4=this.gitInv4||d(this.git4),this.gitInv5=this.gitInv5||d(this.git5),this.gitInv6=this.gitInv6||d(this.git6),this.gitInv7=this.gitInv7||d(this.git7),this.branchLabelColor=this.branchLabelColor||this.labelTextColor,this.gitBranchLabel0=this.branchLabelColor,this.gitBranchLabel1="white",this.gitBranchLabel2=this.branchLabelColor,this.gitBranchLabel3="white",this.gitBranchLabel4=this.branchLabelColor,this.gitBranchLabel5=this.branchLabelColor,this.gitBranchLabel6=this.branchLabelColor,this.gitBranchLabel7=this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||mt,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||yt}calculate(t){if("object"!=typeof t)return void this.updateColors();const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]})),this.updateColors(),e.forEach((e=>{this[e]=t[e]}))}}const Ct={base:{getThemeVariables:t=>{const e=new class{constructor(){this.background="#f4f4f4",this.primaryColor="#fff4dd",this.noteBkgColor="#fff5ad",this.noteTextColor="#333",this.THEME_COLOR_LIMIT=12,this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px"}updateColors(){if(this.primaryTextColor=this.primaryTextColor||(this.darkMode?"#eee":"#333"),this.secondaryColor=this.secondaryColor||c(this.primaryColor,{h:-120}),this.tertiaryColor=this.tertiaryColor||c(this.primaryColor,{h:180,l:5}),this.primaryBorderColor=this.primaryBorderColor||gt(this.primaryColor,this.darkMode),this.secondaryBorderColor=this.secondaryBorderColor||gt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=this.tertiaryBorderColor||gt(this.tertiaryColor,this.darkMode),this.noteBorderColor=this.noteBorderColor||gt(this.noteBkgColor,this.darkMode),this.noteBkgColor=this.noteBkgColor||"#fff5ad",this.noteTextColor=this.noteTextColor||"#333",this.secondaryTextColor=this.secondaryTextColor||d(this.secondaryColor),this.tertiaryTextColor=this.tertiaryTextColor||d(this.tertiaryColor),this.lineColor=this.lineColor||d(this.background),this.arrowheadColor=this.arrowheadColor||d(this.background),this.textColor=this.textColor||this.primaryTextColor,this.border2=this.border2||this.tertiaryBorderColor,this.nodeBkg=this.nodeBkg||this.primaryColor,this.mainBkg=this.mainBkg||this.primaryColor,this.nodeBorder=this.nodeBorder||this.primaryBorderColor,this.clusterBkg=this.clusterBkg||this.tertiaryColor,this.clusterBorder=this.clusterBorder||this.tertiaryBorderColor,this.defaultLinkColor=this.defaultLinkColor||this.lineColor,this.titleColor=this.titleColor||this.tertiaryTextColor,this.edgeLabelBackground=this.edgeLabelBackground||(this.darkMode?(0,f.Z)(this.secondaryColor,30):this.secondaryColor),this.nodeTextColor=this.nodeTextColor||this.primaryTextColor,this.actorBorder=this.actorBorder||this.primaryBorderColor,this.actorBkg=this.actorBkg||this.mainBkg,this.actorTextColor=this.actorTextColor||this.primaryTextColor,this.actorLineColor=this.actorLineColor||"grey",this.labelBoxBkgColor=this.labelBoxBkgColor||this.actorBkg,this.signalColor=this.signalColor||this.textColor,this.signalTextColor=this.signalTextColor||this.textColor,this.labelBoxBorderColor=this.labelBoxBorderColor||this.actorBorder,this.labelTextColor=this.labelTextColor||this.actorTextColor,this.loopTextColor=this.loopTextColor||this.actorTextColor,this.activationBorderColor=this.activationBorderColor||(0,f.Z)(this.secondaryColor,10),this.activationBkgColor=this.activationBkgColor||this.secondaryColor,this.sequenceNumberColor=this.sequenceNumberColor||d(this.lineColor),this.sectionBkgColor=this.sectionBkgColor||this.tertiaryColor,this.altSectionBkgColor=this.altSectionBkgColor||"white",this.sectionBkgColor=this.sectionBkgColor||this.secondaryColor,this.sectionBkgColor2=this.sectionBkgColor2||this.primaryColor,this.excludeBkgColor=this.excludeBkgColor||"#eeeeee",this.taskBorderColor=this.taskBorderColor||this.primaryBorderColor,this.taskBkgColor=this.taskBkgColor||this.primaryColor,this.activeTaskBorderColor=this.activeTaskBorderColor||this.primaryColor,this.activeTaskBkgColor=this.activeTaskBkgColor||(0,p.Z)(this.primaryColor,23),this.gridColor=this.gridColor||"lightgrey",this.doneTaskBkgColor=this.doneTaskBkgColor||"lightgrey",this.doneTaskBorderColor=this.doneTaskBorderColor||"grey",this.critBorderColor=this.critBorderColor||"#ff8888",this.critBkgColor=this.critBkgColor||"red",this.todayLineColor=this.todayLineColor||"red",this.taskTextColor=this.taskTextColor||this.textColor,this.taskTextOutsideColor=this.taskTextOutsideColor||this.textColor,this.taskTextLightColor=this.taskTextLightColor||this.textColor,this.taskTextColor=this.taskTextColor||this.primaryTextColor,this.taskTextDarkColor=this.taskTextDarkColor||this.textColor,this.taskTextClickableColor=this.taskTextClickableColor||"#003163",this.personBorder=this.personBorder||this.primaryBorderColor,this.personBkg=this.personBkg||this.mainBkg,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||this.tertiaryColor,this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.specialStateColor=this.lineColor,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||c(this.primaryColor,{h:30}),this.cScale4=this.cScale4||c(this.primaryColor,{h:60}),this.cScale5=this.cScale5||c(this.primaryColor,{h:90}),this.cScale6=this.cScale6||c(this.primaryColor,{h:120}),this.cScale7=this.cScale7||c(this.primaryColor,{h:150}),this.cScale8=this.cScale8||c(this.primaryColor,{h:210,l:150}),this.cScale9=this.cScale9||c(this.primaryColor,{h:270}),this.cScale10=this.cScale10||c(this.primaryColor,{h:300}),this.cScale11=this.cScale11||c(this.primaryColor,{h:330}),this.darkMode)for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScale"+t]=(0,f.Z)(this["cScale"+t],75);else for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScale"+t]=(0,f.Z)(this["cScale"+t],25);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleInv"+t]=this["cScaleInv"+t]||d(this["cScale"+t]);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this.darkMode?this["cScalePeer"+t]=this["cScalePeer"+t]||(0,p.Z)(this["cScale"+t],10):this["cScalePeer"+t]=this["cScalePeer"+t]||(0,f.Z)(this["cScale"+t],10);this.scaleLabelColor=this.scaleLabelColor||this.labelTextColor;for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.scaleLabelColor;const t=this.darkMode?-4:-1;for(let e=0;e<5;e++)this["surface"+e]=this["surface"+e]||c(this.mainBkg,{h:180,s:-15,l:t*(5+3*e)}),this["surfacePeer"+e]=this["surfacePeer"+e]||c(this.mainBkg,{h:180,s:-15,l:t*(8+3*e)});this.classText=this.classText||this.textColor,this.fillType0=this.fillType0||this.primaryColor,this.fillType1=this.fillType1||this.secondaryColor,this.fillType2=this.fillType2||c(this.primaryColor,{h:64}),this.fillType3=this.fillType3||c(this.secondaryColor,{h:64}),this.fillType4=this.fillType4||c(this.primaryColor,{h:-64}),this.fillType5=this.fillType5||c(this.secondaryColor,{h:-64}),this.fillType6=this.fillType6||c(this.primaryColor,{h:128}),this.fillType7=this.fillType7||c(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||c(this.primaryColor,{l:-10}),this.pie5=this.pie5||c(this.secondaryColor,{l:-10}),this.pie6=this.pie6||c(this.tertiaryColor,{l:-10}),this.pie7=this.pie7||c(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||c(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||c(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||c(this.primaryColor,{h:60,l:-20}),this.pie11=this.pie11||c(this.primaryColor,{h:-60,l:-20}),this.pie12=this.pie12||c(this.primaryColor,{h:120,l:-10}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||c(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||c(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||c(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||c(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||c(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||c(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||(0,g.Z)(this.quadrant1Fill)?(0,p.Z)(this.quadrant1Fill):(0,f.Z)(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?(0,f.Z)(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||c(this.primaryColor,{h:-30}),this.git4=this.git4||c(this.primaryColor,{h:-60}),this.git5=this.git5||c(this.primaryColor,{h:-90}),this.git6=this.git6||c(this.primaryColor,{h:60}),this.git7=this.git7||c(this.primaryColor,{h:120}),this.darkMode?(this.git0=(0,p.Z)(this.git0,25),this.git1=(0,p.Z)(this.git1,25),this.git2=(0,p.Z)(this.git2,25),this.git3=(0,p.Z)(this.git3,25),this.git4=(0,p.Z)(this.git4,25),this.git5=(0,p.Z)(this.git5,25),this.git6=(0,p.Z)(this.git6,25),this.git7=(0,p.Z)(this.git7,25)):(this.git0=(0,f.Z)(this.git0,25),this.git1=(0,f.Z)(this.git1,25),this.git2=(0,f.Z)(this.git2,25),this.git3=(0,f.Z)(this.git3,25),this.git4=(0,f.Z)(this.git4,25),this.git5=(0,f.Z)(this.git5,25),this.git6=(0,f.Z)(this.git6,25),this.git7=(0,f.Z)(this.git7,25)),this.gitInv0=this.gitInv0||d(this.git0),this.gitInv1=this.gitInv1||d(this.git1),this.gitInv2=this.gitInv2||d(this.git2),this.gitInv3=this.gitInv3||d(this.git3),this.gitInv4=this.gitInv4||d(this.git4),this.gitInv5=this.gitInv5||d(this.git5),this.gitInv6=this.gitInv6||d(this.git6),this.gitInv7=this.gitInv7||d(this.git7),this.branchLabelColor=this.branchLabelColor||(this.darkMode?"black":this.labelTextColor),this.gitBranchLabel0=this.gitBranchLabel0||this.branchLabelColor,this.gitBranchLabel1=this.gitBranchLabel1||this.branchLabelColor,this.gitBranchLabel2=this.gitBranchLabel2||this.branchLabelColor,this.gitBranchLabel3=this.gitBranchLabel3||this.branchLabelColor,this.gitBranchLabel4=this.gitBranchLabel4||this.branchLabelColor,this.gitBranchLabel5=this.gitBranchLabel5||this.branchLabelColor,this.gitBranchLabel6=this.gitBranchLabel6||this.branchLabelColor,this.gitBranchLabel7=this.gitBranchLabel7||this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||mt,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||yt}calculate(t){if("object"!=typeof t)return void this.updateColors();const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]})),this.updateColors(),e.forEach((e=>{this[e]=t[e]}))}};return e.calculate(t),e}},dark:{getThemeVariables:t=>{const e=new class{constructor(){this.background="#333",this.primaryColor="#1f2020",this.secondaryColor=(0,p.Z)(this.primaryColor,16),this.tertiaryColor=c(this.primaryColor,{h:-160}),this.primaryBorderColor=d(this.background),this.secondaryBorderColor=gt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=gt(this.tertiaryColor,this.darkMode),this.primaryTextColor=d(this.primaryColor),this.secondaryTextColor=d(this.secondaryColor),this.tertiaryTextColor=d(this.tertiaryColor),this.lineColor=d(this.background),this.textColor=d(this.background),this.mainBkg="#1f2020",this.secondBkg="calculated",this.mainContrastColor="lightgrey",this.darkTextColor=(0,p.Z)(d("#323D47"),10),this.lineColor="calculated",this.border1="#81B1DB",this.border2=(0,u.Z)(255,255,255,.25),this.arrowheadColor="calculated",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="#181818",this.textColor="#ccc",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#F9FFFE",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="calculated",this.activationBkgColor="calculated",this.sequenceNumberColor="black",this.sectionBkgColor=(0,f.Z)("#EAE8D9",30),this.altSectionBkgColor="calculated",this.sectionBkgColor2="#EAE8D9",this.excludeBkgColor=(0,f.Z)(this.sectionBkgColor,10),this.taskBorderColor=(0,u.Z)(255,255,255,70),this.taskBkgColor="calculated",this.taskTextColor="calculated",this.taskTextLightColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor=(0,u.Z)(255,255,255,50),this.activeTaskBkgColor="#81B1DB",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="grey",this.critBorderColor="#E83737",this.critBkgColor="#E83737",this.taskTextDarkColor="calculated",this.todayLineColor="#DB5757",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.labelColor="calculated",this.errorBkgColor="#a44141",this.errorTextColor="#ddd"}updateColors(){this.secondBkg=(0,p.Z)(this.mainBkg,16),this.lineColor=this.mainContrastColor,this.arrowheadColor=this.mainContrastColor,this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.edgeLabelBackground=(0,p.Z)(this.labelBackground,25),this.actorBorder=this.border1,this.actorBkg=this.mainBkg,this.actorTextColor=this.mainContrastColor,this.actorLineColor=this.mainContrastColor,this.signalColor=this.mainContrastColor,this.signalTextColor=this.mainContrastColor,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.mainContrastColor,this.loopTextColor=this.mainContrastColor,this.noteBorderColor=this.secondaryBorderColor,this.noteBkgColor=this.secondBkg,this.noteTextColor=this.secondaryTextColor,this.activationBorderColor=this.border1,this.activationBkgColor=this.secondBkg,this.altSectionBkgColor=this.background,this.taskBkgColor=(0,p.Z)(this.mainBkg,23),this.taskTextColor=this.darkTextColor,this.taskTextLightColor=this.mainContrastColor,this.taskTextOutsideColor=this.taskTextLightColor,this.gridColor=this.mainContrastColor,this.doneTaskBkgColor=this.mainContrastColor,this.taskTextDarkColor=this.darkTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#555",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#f4f4f4",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=c(this.primaryColor,{h:64}),this.fillType3=c(this.secondaryColor,{h:64}),this.fillType4=c(this.primaryColor,{h:-64}),this.fillType5=c(this.secondaryColor,{h:-64}),this.fillType6=c(this.primaryColor,{h:128}),this.fillType7=c(this.secondaryColor,{h:128}),this.cScale1=this.cScale1||"#0b0000",this.cScale2=this.cScale2||"#4d1037",this.cScale3=this.cScale3||"#3f5258",this.cScale4=this.cScale4||"#4f2f1b",this.cScale5=this.cScale5||"#6e0a0a",this.cScale6=this.cScale6||"#3b0048",this.cScale7=this.cScale7||"#995a01",this.cScale8=this.cScale8||"#154706",this.cScale9=this.cScale9||"#161722",this.cScale10=this.cScale10||"#00296f",this.cScale11=this.cScale11||"#01629c",this.cScale12=this.cScale12||"#010029",this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||c(this.primaryColor,{h:30}),this.cScale4=this.cScale4||c(this.primaryColor,{h:60}),this.cScale5=this.cScale5||c(this.primaryColor,{h:90}),this.cScale6=this.cScale6||c(this.primaryColor,{h:120}),this.cScale7=this.cScale7||c(this.primaryColor,{h:150}),this.cScale8=this.cScale8||c(this.primaryColor,{h:210}),this.cScale9=this.cScale9||c(this.primaryColor,{h:270}),this.cScale10=this.cScale10||c(this.primaryColor,{h:300}),this.cScale11=this.cScale11||c(this.primaryColor,{h:330});for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleInv"+t]=this["cScaleInv"+t]||d(this["cScale"+t]);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScalePeer"+t]=this["cScalePeer"+t]||(0,p.Z)(this["cScale"+t],10);for(let t=0;t<5;t++)this["surface"+t]=this["surface"+t]||c(this.mainBkg,{h:30,s:-30,l:-(4*t-10)}),this["surfacePeer"+t]=this["surfacePeer"+t]||c(this.mainBkg,{h:30,s:-30,l:-(4*t-7)});this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.scaleLabelColor;for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["pie"+t]=this["cScale"+t];this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||c(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||c(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||c(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||c(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||c(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||c(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||(0,g.Z)(this.quadrant1Fill)?(0,p.Z)(this.quadrant1Fill):(0,f.Z)(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.classText=this.primaryTextColor,this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?(0,f.Z)(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=(0,p.Z)(this.secondaryColor,20),this.git1=(0,p.Z)(this.pie2||this.secondaryColor,20),this.git2=(0,p.Z)(this.pie3||this.tertiaryColor,20),this.git3=(0,p.Z)(this.pie4||c(this.primaryColor,{h:-30}),20),this.git4=(0,p.Z)(this.pie5||c(this.primaryColor,{h:-60}),20),this.git5=(0,p.Z)(this.pie6||c(this.primaryColor,{h:-90}),10),this.git6=(0,p.Z)(this.pie7||c(this.primaryColor,{h:60}),10),this.git7=(0,p.Z)(this.pie8||c(this.primaryColor,{h:120}),20),this.gitInv0=this.gitInv0||d(this.git0),this.gitInv1=this.gitInv1||d(this.git1),this.gitInv2=this.gitInv2||d(this.git2),this.gitInv3=this.gitInv3||d(this.git3),this.gitInv4=this.gitInv4||d(this.git4),this.gitInv5=this.gitInv5||d(this.git5),this.gitInv6=this.gitInv6||d(this.git6),this.gitInv7=this.gitInv7||d(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||d(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||d(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||(0,p.Z)(this.background,12),this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||(0,p.Z)(this.background,2)}calculate(t){if("object"!=typeof t)return void this.updateColors();const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]})),this.updateColors(),e.forEach((e=>{this[e]=t[e]}))}};return e.calculate(t),e}},default:{getThemeVariables:_t},forest:{getThemeVariables:t=>{const e=new class{constructor(){this.background="#f4f4f4",this.primaryColor="#cde498",this.secondaryColor="#cdffb2",this.background="white",this.mainBkg="#cde498",this.secondBkg="#cdffb2",this.lineColor="green",this.border1="#13540c",this.border2="#6eaa49",this.arrowheadColor="green",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.tertiaryColor=(0,p.Z)("#cde498",10),this.primaryBorderColor=gt(this.primaryColor,this.darkMode),this.secondaryBorderColor=gt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=gt(this.tertiaryColor,this.darkMode),this.primaryTextColor=d(this.primaryColor),this.secondaryTextColor=d(this.secondaryColor),this.tertiaryTextColor=d(this.primaryColor),this.lineColor=d(this.background),this.textColor=d(this.background),this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#333",this.edgeLabelBackground="#e8e8e8",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="grey",this.signalColor="#333",this.signalTextColor="#333",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="#326932",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="#6eaa49",this.altSectionBkgColor="white",this.sectionBkgColor2="#6eaa49",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="#487e3a",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){this.actorBorder=(0,f.Z)(this.mainBkg,20),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||c(this.primaryColor,{h:30}),this.cScale4=this.cScale4||c(this.primaryColor,{h:60}),this.cScale5=this.cScale5||c(this.primaryColor,{h:90}),this.cScale6=this.cScale6||c(this.primaryColor,{h:120}),this.cScale7=this.cScale7||c(this.primaryColor,{h:150}),this.cScale8=this.cScale8||c(this.primaryColor,{h:210}),this.cScale9=this.cScale9||c(this.primaryColor,{h:270}),this.cScale10=this.cScale10||c(this.primaryColor,{h:300}),this.cScale11=this.cScale11||c(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||(0,f.Z)(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||(0,f.Z)(this.tertiaryColor,40);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScale"+t]=(0,f.Z)(this["cScale"+t],10),this["cScalePeer"+t]=this["cScalePeer"+t]||(0,f.Z)(this["cScale"+t],25);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleInv"+t]=this["cScaleInv"+t]||c(this["cScale"+t],{h:180});this.scaleLabelColor="calculated"!==this.scaleLabelColor&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor;for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.scaleLabelColor;for(let t=0;t<5;t++)this["surface"+t]=this["surface"+t]||c(this.mainBkg,{h:30,s:-30,l:-(5+5*t)}),this["surfacePeer"+t]=this["surfacePeer"+t]||c(this.mainBkg,{h:30,s:-30,l:-(8+5*t)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.taskBorderColor=this.border1,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=c(this.primaryColor,{h:64}),this.fillType3=c(this.secondaryColor,{h:64}),this.fillType4=c(this.primaryColor,{h:-64}),this.fillType5=c(this.secondaryColor,{h:-64}),this.fillType6=c(this.primaryColor,{h:128}),this.fillType7=c(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||c(this.primaryColor,{l:-30}),this.pie5=this.pie5||c(this.secondaryColor,{l:-30}),this.pie6=this.pie6||c(this.tertiaryColor,{h:40,l:-40}),this.pie7=this.pie7||c(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||c(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||c(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||c(this.primaryColor,{h:60,l:-50}),this.pie11=this.pie11||c(this.primaryColor,{h:-60,l:-50}),this.pie12=this.pie12||c(this.primaryColor,{h:120,l:-50}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||c(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||c(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||c(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||c(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||c(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||c(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||(0,g.Z)(this.quadrant1Fill)?(0,p.Z)(this.quadrant1Fill):(0,f.Z)(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||c(this.primaryColor,{h:-30}),this.git4=this.git4||c(this.primaryColor,{h:-60}),this.git5=this.git5||c(this.primaryColor,{h:-90}),this.git6=this.git6||c(this.primaryColor,{h:60}),this.git7=this.git7||c(this.primaryColor,{h:120}),this.darkMode?(this.git0=(0,p.Z)(this.git0,25),this.git1=(0,p.Z)(this.git1,25),this.git2=(0,p.Z)(this.git2,25),this.git3=(0,p.Z)(this.git3,25),this.git4=(0,p.Z)(this.git4,25),this.git5=(0,p.Z)(this.git5,25),this.git6=(0,p.Z)(this.git6,25),this.git7=(0,p.Z)(this.git7,25)):(this.git0=(0,f.Z)(this.git0,25),this.git1=(0,f.Z)(this.git1,25),this.git2=(0,f.Z)(this.git2,25),this.git3=(0,f.Z)(this.git3,25),this.git4=(0,f.Z)(this.git4,25),this.git5=(0,f.Z)(this.git5,25),this.git6=(0,f.Z)(this.git6,25),this.git7=(0,f.Z)(this.git7,25)),this.gitInv0=this.gitInv0||d(this.git0),this.gitInv1=this.gitInv1||d(this.git1),this.gitInv2=this.gitInv2||d(this.git2),this.gitInv3=this.gitInv3||d(this.git3),this.gitInv4=this.gitInv4||d(this.git4),this.gitInv5=this.gitInv5||d(this.git5),this.gitInv6=this.gitInv6||d(this.git6),this.gitInv7=this.gitInv7||d(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||d(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||d(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||mt,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||yt}calculate(t){if("object"!=typeof t)return void this.updateColors();const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]})),this.updateColors(),e.forEach((e=>{this[e]=t[e]}))}};return e.calculate(t),e}},neutral:{getThemeVariables:t=>{const e=new bt;return e.calculate(t),e}}},xt={theme:"default",themeVariables:Ct.default.getThemeVariables(),themeCSS:void 0,maxTextSize:5e4,darkMode:!1,fontFamily:'"trebuchet ms", verdana, arial, sans-serif;',logLevel:5,securityLevel:"strict",startOnLoad:!0,arrowMarkerAbsolute:!1,secure:["secure","securityLevel","startOnLoad","maxTextSize"],deterministicIds:!1,deterministicIDSeed:void 0,flowchart:{titleTopMargin:25,diagramPadding:8,htmlLabels:!0,nodeSpacing:50,rankSpacing:50,curve:"basis",padding:15,useMaxWidth:!0,defaultRenderer:"dagre-wrapper",wrappingWidth:200},sequence:{hideUnusedParticipants:!1,activationWidth:10,diagramMarginX:50,diagramMarginY:10,actorMargin:50,width:150,height:65,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",mirrorActors:!0,forceMenus:!1,bottomMarginAdj:1,useMaxWidth:!0,rightAngles:!1,showSequenceNumbers:!1,actorFontSize:14,actorFontFamily:'"Open Sans", sans-serif',actorFontWeight:400,noteFontSize:14,noteFontFamily:'"trebuchet ms", verdana, arial, sans-serif',noteFontWeight:400,noteAlign:"center",messageFontSize:16,messageFontFamily:'"trebuchet ms", verdana, arial, sans-serif',messageFontWeight:400,wrap:!1,wrapPadding:10,labelBoxWidth:50,labelBoxHeight:20,messageFont:function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},noteFont:function(){return{fontFamily:this.noteFontFamily,fontSize:this.noteFontSize,fontWeight:this.noteFontWeight}},actorFont:function(){return{fontFamily:this.actorFontFamily,fontSize:this.actorFontSize,fontWeight:this.actorFontWeight}}},gantt:{titleTopMargin:25,barHeight:20,barGap:4,topPadding:50,rightPadding:75,leftPadding:75,gridLineStartPadding:35,fontSize:11,sectionFontSize:11,numberSectionStyles:4,displayMode:"",axisFormat:"%Y-%m-%d",tickInterval:void 0,useMaxWidth:!0,topAxis:!1,useWidth:void 0},journey:{diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,useMaxWidth:!0,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"]},timeline:{diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,useMaxWidth:!0,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"],disableMulticolor:!1},class:{titleTopMargin:25,arrowMarkerAbsolute:!1,dividerMargin:10,padding:5,textHeight:10,useMaxWidth:!0,defaultRenderer:"dagre-wrapper"},state:{titleTopMargin:25,dividerMargin:10,sizeUnit:5,padding:8,textHeight:10,titleShift:-15,noteMargin:10,forkWidth:70,forkHeight:7,miniPadding:2,fontSizeFactor:5.02,fontSize:24,labelHeight:16,edgeLengthFactor:"20",compositTitleSize:35,radius:5,useMaxWidth:!0,defaultRenderer:"dagre-wrapper"},er:{titleTopMargin:25,diagramPadding:20,layoutDirection:"TB",minEntityWidth:100,minEntityHeight:75,entityPadding:15,stroke:"gray",fill:"honeydew",fontSize:12,useMaxWidth:!0},pie:{useWidth:void 0,useMaxWidth:!0,textPosition:.75},quadrantChart:{chartWidth:500,chartHeight:500,titlePadding:10,titleFontSize:20,quadrantPadding:5,quadrantTextTopPadding:5,quadrantLabelFontSize:16,quadrantInternalBorderStrokeWidth:1,quadrantExternalBorderStrokeWidth:2,xAxisLabelPadding:5,xAxisLabelFontSize:16,xAxisPosition:"top",yAxisLabelPadding:5,yAxisLabelFontSize:16,yAxisPosition:"left",pointTextPadding:5,pointLabelFontSize:12,pointRadius:5,useMaxWidth:!0},requirement:{useWidth:void 0,useMaxWidth:!0,rect_fill:"#f9f9f9",text_color:"#333",rect_border_size:"0.5px",rect_border_color:"#bbb",rect_min_width:200,rect_min_height:200,fontSize:14,rect_padding:10,line_height:20},gitGraph:{titleTopMargin:25,diagramPadding:8,nodeLabel:{width:75,height:100,x:-25,y:0},mainBranchName:"main",mainBranchOrder:0,showCommitLabel:!0,showBranches:!0,rotateCommitLabel:!0},c4:{useWidth:void 0,diagramMarginX:50,diagramMarginY:10,c4ShapeMargin:50,c4ShapePadding:20,width:216,height:60,boxMargin:10,useMaxWidth:!0,c4ShapeInRow:4,nextLinePaddingX:0,c4BoundaryInRow:2,personFontSize:14,personFontFamily:'"Open Sans", sans-serif',personFontWeight:"normal",external_personFontSize:14,external_personFontFamily:'"Open Sans", sans-serif',external_personFontWeight:"normal",systemFontSize:14,systemFontFamily:'"Open Sans", sans-serif',systemFontWeight:"normal",external_systemFontSize:14,external_systemFontFamily:'"Open Sans", sans-serif',external_systemFontWeight:"normal",system_dbFontSize:14,system_dbFontFamily:'"Open Sans", sans-serif',system_dbFontWeight:"normal",external_system_dbFontSize:14,external_system_dbFontFamily:'"Open Sans", sans-serif',external_system_dbFontWeight:"normal",system_queueFontSize:14,system_queueFontFamily:'"Open Sans", sans-serif',system_queueFontWeight:"normal",external_system_queueFontSize:14,external_system_queueFontFamily:'"Open Sans", sans-serif',external_system_queueFontWeight:"normal",boundaryFontSize:14,boundaryFontFamily:'"Open Sans", sans-serif',boundaryFontWeight:"normal",messageFontSize:12,messageFontFamily:'"Open Sans", sans-serif',messageFontWeight:"normal",containerFontSize:14,containerFontFamily:'"Open Sans", sans-serif',containerFontWeight:"normal",external_containerFontSize:14,external_containerFontFamily:'"Open Sans", sans-serif',external_containerFontWeight:"normal",container_dbFontSize:14,container_dbFontFamily:'"Open Sans", sans-serif',container_dbFontWeight:"normal",external_container_dbFontSize:14,external_container_dbFontFamily:'"Open Sans", sans-serif',external_container_dbFontWeight:"normal",container_queueFontSize:14,container_queueFontFamily:'"Open Sans", sans-serif',container_queueFontWeight:"normal",external_container_queueFontSize:14,external_container_queueFontFamily:'"Open Sans", sans-serif',external_container_queueFontWeight:"normal",componentFontSize:14,componentFontFamily:'"Open Sans", sans-serif',componentFontWeight:"normal",external_componentFontSize:14,external_componentFontFamily:'"Open Sans", sans-serif',external_componentFontWeight:"normal",component_dbFontSize:14,component_dbFontFamily:'"Open Sans", sans-serif',component_dbFontWeight:"normal",external_component_dbFontSize:14,external_component_dbFontFamily:'"Open Sans", sans-serif',external_component_dbFontWeight:"normal",component_queueFontSize:14,component_queueFontFamily:'"Open Sans", sans-serif',component_queueFontWeight:"normal",external_component_queueFontSize:14,external_component_queueFontFamily:'"Open Sans", sans-serif',external_component_queueFontWeight:"normal",wrap:!0,wrapPadding:10,personFont:function(){return{fontFamily:this.personFontFamily,fontSize:this.personFontSize,fontWeight:this.personFontWeight}},external_personFont:function(){return{fontFamily:this.external_personFontFamily,fontSize:this.external_personFontSize,fontWeight:this.external_personFontWeight}},systemFont:function(){return{fontFamily:this.systemFontFamily,fontSize:this.systemFontSize,fontWeight:this.systemFontWeight}},external_systemFont:function(){return{fontFamily:this.external_systemFontFamily,fontSize:this.external_systemFontSize,fontWeight:this.external_systemFontWeight}},system_dbFont:function(){return{fontFamily:this.system_dbFontFamily,fontSize:this.system_dbFontSize,fontWeight:this.system_dbFontWeight}},external_system_dbFont:function(){return{fontFamily:this.external_system_dbFontFamily,fontSize:this.external_system_dbFontSize,fontWeight:this.external_system_dbFontWeight}},system_queueFont:function(){return{fontFamily:this.system_queueFontFamily,fontSize:this.system_queueFontSize,fontWeight:this.system_queueFontWeight}},external_system_queueFont:function(){return{fontFamily:this.external_system_queueFontFamily,fontSize:this.external_system_queueFontSize,fontWeight:this.external_system_queueFontWeight}},containerFont:function(){return{fontFamily:this.containerFontFamily,fontSize:this.containerFontSize,fontWeight:this.containerFontWeight}},external_containerFont:function(){return{fontFamily:this.external_containerFontFamily,fontSize:this.external_containerFontSize,fontWeight:this.external_containerFontWeight}},container_dbFont:function(){return{fontFamily:this.container_dbFontFamily,fontSize:this.container_dbFontSize,fontWeight:this.container_dbFontWeight}},external_container_dbFont:function(){return{fontFamily:this.external_container_dbFontFamily,fontSize:this.external_container_dbFontSize,fontWeight:this.external_container_dbFontWeight}},container_queueFont:function(){return{fontFamily:this.container_queueFontFamily,fontSize:this.container_queueFontSize,fontWeight:this.container_queueFontWeight}},external_container_queueFont:function(){return{fontFamily:this.external_container_queueFontFamily,fontSize:this.external_container_queueFontSize,fontWeight:this.external_container_queueFontWeight}},componentFont:function(){return{fontFamily:this.componentFontFamily,fontSize:this.componentFontSize,fontWeight:this.componentFontWeight}},external_componentFont:function(){return{fontFamily:this.external_componentFontFamily,fontSize:this.external_componentFontSize,fontWeight:this.external_componentFontWeight}},component_dbFont:function(){return{fontFamily:this.component_dbFontFamily,fontSize:this.component_dbFontSize,fontWeight:this.component_dbFontWeight}},external_component_dbFont:function(){return{fontFamily:this.external_component_dbFontFamily,fontSize:this.external_component_dbFontSize,fontWeight:this.external_component_dbFontWeight}},component_queueFont:function(){return{fontFamily:this.component_queueFontFamily,fontSize:this.component_queueFontSize,fontWeight:this.component_queueFontWeight}},external_component_queueFont:function(){return{fontFamily:this.external_component_queueFontFamily,fontSize:this.external_component_queueFontSize,fontWeight:this.external_component_queueFontWeight}},boundaryFont:function(){return{fontFamily:this.boundaryFontFamily,fontSize:this.boundaryFontSize,fontWeight:this.boundaryFontWeight}},messageFont:function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},person_bg_color:"#08427B",person_border_color:"#073B6F",external_person_bg_color:"#686868",external_person_border_color:"#8A8A8A",system_bg_color:"#1168BD",system_border_color:"#3C7FC0",system_db_bg_color:"#1168BD",system_db_border_color:"#3C7FC0",system_queue_bg_color:"#1168BD",system_queue_border_color:"#3C7FC0",external_system_bg_color:"#999999",external_system_border_color:"#8A8A8A",external_system_db_bg_color:"#999999",external_system_db_border_color:"#8A8A8A",external_system_queue_bg_color:"#999999",external_system_queue_border_color:"#8A8A8A",container_bg_color:"#438DD5",container_border_color:"#3C7FC0",container_db_bg_color:"#438DD5",container_db_border_color:"#3C7FC0",container_queue_bg_color:"#438DD5",container_queue_border_color:"#3C7FC0",external_container_bg_color:"#B3B3B3",external_container_border_color:"#A6A6A6",external_container_db_bg_color:"#B3B3B3",external_container_db_border_color:"#A6A6A6",external_container_queue_bg_color:"#B3B3B3",external_container_queue_border_color:"#A6A6A6",component_bg_color:"#85BBF0",component_border_color:"#78A8D8",component_db_bg_color:"#85BBF0",component_db_border_color:"#78A8D8",component_queue_bg_color:"#85BBF0",component_queue_border_color:"#78A8D8",external_component_bg_color:"#CCCCCC",external_component_border_color:"#BFBFBF",external_component_db_bg_color:"#CCCCCC",external_component_db_border_color:"#BFBFBF",external_component_queue_bg_color:"#CCCCCC",external_component_queue_border_color:"#BFBFBF"},mindmap:{useMaxWidth:!0,padding:10,maxNodeWidth:200},fontSize:16};xt.class&&(xt.class.arrowMarkerAbsolute=xt.arrowMarkerAbsolute),xt.gitGraph&&(xt.gitGraph.arrowMarkerAbsolute=xt.arrowMarkerAbsolute);const vt=(t,e="")=>Object.keys(t).reduce(((i,r)=>Array.isArray(t[r])?i:"object"==typeof t[r]&&null!==t[r]?[...i,e+r,...vt(t[r],"")]:[...i,e+r]),[]),kt=vt(xt,""),Tt=xt;function wt(t){return null==t}var St={isNothing:wt,isObject:function(t){return"object"==typeof t&&null!==t},toArray:function(t){return Array.isArray(t)?t:wt(t)?[]:[t]},repeat:function(t,e){var i,r="";for(i=0;i<e;i+=1)r+=t;return r},isNegativeZero:function(t){return 0===t&&Number.NEGATIVE_INFINITY===1/t},extend:function(t,e){var i,r,n,o;if(e)for(i=0,r=(o=Object.keys(e)).length;i<r;i+=1)t[n=o[i]]=e[n];return t}};function Bt(t,e){var i="",r=t.reason||"(unknown reason)";return t.mark?(t.mark.name&&(i+='in "'+t.mark.name+'" '),i+="("+(t.mark.line+1)+":"+(t.mark.column+1)+")",!e&&t.mark.snippet&&(i+="\n\n"+t.mark.snippet),r+" "+i):r}function Ft(t,e){Error.call(this),this.name="YAMLException",this.reason=t,this.mark=e,this.message=Bt(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack||""}Ft.prototype=Object.create(Error.prototype),Ft.prototype.constructor=Ft,Ft.prototype.toString=function(t){return this.name+": "+Bt(this,t)};var Lt=Ft;function Mt(t,e,i,r,n){var o="",a="",s=Math.floor(n/2)-1;return r-e>s&&(e=r-s+(o=" ... ").length),i-r>s&&(i=r+s-(a=" ...").length),{str:o+t.slice(e,i).replace(/\t/g,"→")+a,pos:r-e+o.length}}function At(t,e){return St.repeat(" ",e-t.length)+t}var Et=function(t,e){if(e=Object.create(e||null),!t.buffer)return null;e.maxLength||(e.maxLength=79),"number"!=typeof e.indent&&(e.indent=1),"number"!=typeof e.linesBefore&&(e.linesBefore=3),"number"!=typeof e.linesAfter&&(e.linesAfter=2);for(var i,r=/\r?\n|\r|\0/g,n=[0],o=[],a=-1;i=r.exec(t.buffer);)o.push(i.index),n.push(i.index+i[0].length),t.position<=i.index&&a<0&&(a=n.length-2);a<0&&(a=n.length-1);var s,l,h="",c=Math.min(t.line+e.linesAfter,o.length).toString().length,u=e.maxLength-(e.indent+c+3);for(s=1;s<=e.linesBefore&&!(a-s<0);s++)l=Mt(t.buffer,n[a-s],o[a-s],t.position-(n[a]-n[a-s]),u),h=St.repeat(" ",e.indent)+At((t.line-s+1).toString(),c)+" | "+l.str+"\n"+h;for(l=Mt(t.buffer,n[a],o[a],t.position,u),h+=St.repeat(" ",e.indent)+At((t.line+1).toString(),c)+" | "+l.str+"\n",h+=St.repeat("-",e.indent+c+3+l.pos)+"^\n",s=1;s<=e.linesAfter&&!(a+s>=o.length);s++)l=Mt(t.buffer,n[a+s],o[a+s],t.position-(n[a]-n[a+s]),u),h+=St.repeat(" ",e.indent)+At((t.line+s+1).toString(),c)+" | "+l.str+"\n";return h.replace(/\n$/,"")},Ot=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],It=["scalar","sequence","mapping"],Nt=function(t,e){var i,r;if(e=e||{},Object.keys(e).forEach((function(e){if(-1===Ot.indexOf(e))throw new Lt('Unknown option "'+e+'" is met in definition of "'+t+'" YAML type.')})),this.options=e,this.tag=t,this.kind=e.kind||null,this.resolve=e.resolve||function(){return!0},this.construct=e.construct||function(t){return t},this.instanceOf=e.instanceOf||null,this.predicate=e.predicate||null,this.represent=e.represent||null,this.representName=e.representName||null,this.defaultStyle=e.defaultStyle||null,this.multi=e.multi||!1,this.styleAliases=(i=e.styleAliases||null,r={},null!==i&&Object.keys(i).forEach((function(t){i[t].forEach((function(e){r[String(e)]=t}))})),r),-1===It.indexOf(this.kind))throw new Lt('Unknown kind "'+this.kind+'" is specified for "'+t+'" YAML type.')};function qt(t,e){var i=[];return t[e].forEach((function(t){var e=i.length;i.forEach((function(i,r){i.tag===t.tag&&i.kind===t.kind&&i.multi===t.multi&&(e=r)})),i[e]=t})),i}function Dt(t){return this.extend(t)}Dt.prototype.extend=function(t){var e=[],i=[];if(t instanceof Nt)i.push(t);else if(Array.isArray(t))i=i.concat(t);else{if(!t||!Array.isArray(t.implicit)&&!Array.isArray(t.explicit))throw new Lt("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");t.implicit&&(e=e.concat(t.implicit)),t.explicit&&(i=i.concat(t.explicit))}e.forEach((function(t){if(!(t instanceof Nt))throw new Lt("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(t.loadKind&&"scalar"!==t.loadKind)throw new Lt("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(t.multi)throw new Lt("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")})),i.forEach((function(t){if(!(t instanceof Nt))throw new Lt("Specified list of YAML types (or a single Type object) contains a non-Type object.")}));var r=Object.create(Dt.prototype);return r.implicit=(this.implicit||[]).concat(e),r.explicit=(this.explicit||[]).concat(i),r.compiledImplicit=qt(r,"implicit"),r.compiledExplicit=qt(r,"explicit"),r.compiledTypeMap=function(){var t,e,i={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}};function r(t){t.multi?(i.multi[t.kind].push(t),i.multi.fallback.push(t)):i[t.kind][t.tag]=i.fallback[t.tag]=t}for(t=0,e=arguments.length;t<e;t+=1)arguments[t].forEach(r);return i}(r.compiledImplicit,r.compiledExplicit),r};var $t=new Dt({explicit:[new Nt("tag:yaml.org,2002:str",{kind:"scalar",construct:function(t){return null!==t?t:""}}),new Nt("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(t){return null!==t?t:[]}}),new Nt("tag:yaml.org,2002:map",{kind:"mapping",construct:function(t){return null!==t?t:{}}})]}),Zt=new Nt("tag:yaml.org,2002:null",{kind:"scalar",resolve:function(t){if(null===t)return!0;var e=t.length;return 1===e&&"~"===t||4===e&&("null"===t||"Null"===t||"NULL"===t)},construct:function(){return null},predicate:function(t){return null===t},represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"},empty:function(){return""}},defaultStyle:"lowercase"}),zt=new Nt("tag:yaml.org,2002:bool",{kind:"scalar",resolve:function(t){if(null===t)return!1;var e=t.length;return 4===e&&("true"===t||"True"===t||"TRUE"===t)||5===e&&("false"===t||"False"===t||"FALSE"===t)},construct:function(t){return"true"===t||"True"===t||"TRUE"===t},predicate:function(t){return"[object Boolean]"===Object.prototype.toString.call(t)},represent:{lowercase:function(t){return t?"true":"false"},uppercase:function(t){return t?"TRUE":"FALSE"},camelcase:function(t){return t?"True":"False"}},defaultStyle:"lowercase"});function jt(t){return 48<=t&&t<=55}function Pt(t){return 48<=t&&t<=57}var Rt=new Nt("tag:yaml.org,2002:int",{kind:"scalar",resolve:function(t){if(null===t)return!1;var e,i,r=t.length,n=0,o=!1;if(!r)return!1;if("-"!==(e=t[n])&&"+"!==e||(e=t[++n]),"0"===e){if(n+1===r)return!0;if("b"===(e=t[++n])){for(n++;n<r;n++)if("_"!==(e=t[n])){if("0"!==e&&"1"!==e)return!1;o=!0}return o&&"_"!==e}if("x"===e){for(n++;n<r;n++)if("_"!==(e=t[n])){if(!(48<=(i=t.charCodeAt(n))&&i<=57||65<=i&&i<=70||97<=i&&i<=102))return!1;o=!0}return o&&"_"!==e}if("o"===e){for(n++;n<r;n++)if("_"!==(e=t[n])){if(!jt(t.charCodeAt(n)))return!1;o=!0}return o&&"_"!==e}}if("_"===e)return!1;for(;n<r;n++)if("_"!==(e=t[n])){if(!Pt(t.charCodeAt(n)))return!1;o=!0}return!(!o||"_"===e)},construct:function(t){var e,i=t,r=1;if(-1!==i.indexOf("_")&&(i=i.replace(/_/g,"")),"-"!==(e=i[0])&&"+"!==e||("-"===e&&(r=-1),e=(i=i.slice(1))[0]),"0"===i)return 0;if("0"===e){if("b"===i[1])return r*parseInt(i.slice(2),2);if("x"===i[1])return r*parseInt(i.slice(2),16);if("o"===i[1])return r*parseInt(i.slice(2),8)}return r*parseInt(i,10)},predicate:function(t){return"[object Number]"===Object.prototype.toString.call(t)&&t%1==0&&!St.isNegativeZero(t)},represent:{binary:function(t){return t>=0?"0b"+t.toString(2):"-0b"+t.toString(2).slice(1)},octal:function(t){return t>=0?"0o"+t.toString(8):"-0o"+t.toString(8).slice(1)},decimal:function(t){return t.toString(10)},hexadecimal:function(t){return t>=0?"0x"+t.toString(16).toUpperCase():"-0x"+t.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),Wt=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$"),Ut=/^[-+]?[0-9]+e/,Ht=new Nt("tag:yaml.org,2002:float",{kind:"scalar",resolve:function(t){return null!==t&&!(!Wt.test(t)||"_"===t[t.length-1])},construct:function(t){var e,i;return i="-"===(e=t.replace(/_/g,"").toLowerCase())[0]?-1:1,"+-".indexOf(e[0])>=0&&(e=e.slice(1)),".inf"===e?1===i?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:".nan"===e?NaN:i*parseFloat(e,10)},predicate:function(t){return"[object Number]"===Object.prototype.toString.call(t)&&(t%1!=0||St.isNegativeZero(t))},represent:function(t,e){var i;if(isNaN(t))switch(e){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===t)switch(e){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===t)switch(e){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(St.isNegativeZero(t))return"-0.0";return i=t.toString(10),Ut.test(i)?i.replace("e",".e"):i},defaultStyle:"lowercase"}),Yt=$t.extend({implicit:[Zt,zt,Rt,Ht]}),Vt=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),Gt=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$"),Xt=new Nt("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:function(t){return null!==t&&(null!==Vt.exec(t)||null!==Gt.exec(t))},construct:function(t){var e,i,r,n,o,a,s,l,h=0,c=null;if(null===(e=Vt.exec(t))&&(e=Gt.exec(t)),null===e)throw new Error("Date resolve error");if(i=+e[1],r=+e[2]-1,n=+e[3],!e[4])return new Date(Date.UTC(i,r,n));if(o=+e[4],a=+e[5],s=+e[6],e[7]){for(h=e[7].slice(0,3);h.length<3;)h+="0";h=+h}return e[9]&&(c=6e4*(60*+e[10]+ +(e[11]||0)),"-"===e[9]&&(c=-c)),l=new Date(Date.UTC(i,r,n,o,a,s,h)),c&&l.setTime(l.getTime()-c),l},instanceOf:Date,represent:function(t){return t.toISOString()}}),Jt=new Nt("tag:yaml.org,2002:merge",{kind:"scalar",resolve:function(t){return"<<"===t||null===t}}),Qt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r",Kt=new Nt("tag:yaml.org,2002:binary",{kind:"scalar",resolve:function(t){if(null===t)return!1;var e,i,r=0,n=t.length,o=Qt;for(i=0;i<n;i++)if(!((e=o.indexOf(t.charAt(i)))>64)){if(e<0)return!1;r+=6}return r%8==0},construct:function(t){var e,i,r=t.replace(/[\r\n=]/g,""),n=r.length,o=Qt,a=0,s=[];for(e=0;e<n;e++)e%4==0&&e&&(s.push(a>>16&255),s.push(a>>8&255),s.push(255&a)),a=a<<6|o.indexOf(r.charAt(e));return 0==(i=n%4*6)?(s.push(a>>16&255),s.push(a>>8&255),s.push(255&a)):18===i?(s.push(a>>10&255),s.push(a>>2&255)):12===i&&s.push(a>>4&255),new Uint8Array(s)},predicate:function(t){return"[object Uint8Array]"===Object.prototype.toString.call(t)},represent:function(t){var e,i,r="",n=0,o=t.length,a=Qt;for(e=0;e<o;e++)e%3==0&&e&&(r+=a[n>>18&63],r+=a[n>>12&63],r+=a[n>>6&63],r+=a[63&n]),n=(n<<8)+t[e];return 0==(i=o%3)?(r+=a[n>>18&63],r+=a[n>>12&63],r+=a[n>>6&63],r+=a[63&n]):2===i?(r+=a[n>>10&63],r+=a[n>>4&63],r+=a[n<<2&63],r+=a[64]):1===i&&(r+=a[n>>2&63],r+=a[n<<4&63],r+=a[64],r+=a[64]),r}}),te=Object.prototype.hasOwnProperty,ee=Object.prototype.toString,ie=new Nt("tag:yaml.org,2002:omap",{kind:"sequence",resolve:function(t){if(null===t)return!0;var e,i,r,n,o,a=[],s=t;for(e=0,i=s.length;e<i;e+=1){if(r=s[e],o=!1,"[object Object]"!==ee.call(r))return!1;for(n in r)if(te.call(r,n)){if(o)return!1;o=!0}if(!o)return!1;if(-1!==a.indexOf(n))return!1;a.push(n)}return!0},construct:function(t){return null!==t?t:[]}}),re=Object.prototype.toString,ne=new Nt("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:function(t){if(null===t)return!0;var e,i,r,n,o,a=t;for(o=new Array(a.length),e=0,i=a.length;e<i;e+=1){if(r=a[e],"[object Object]"!==re.call(r))return!1;if(1!==(n=Object.keys(r)).length)return!1;o[e]=[n[0],r[n[0]]]}return!0},construct:function(t){if(null===t)return[];var e,i,r,n,o,a=t;for(o=new Array(a.length),e=0,i=a.length;e<i;e+=1)r=a[e],n=Object.keys(r),o[e]=[n[0],r[n[0]]];return o}}),oe=Object.prototype.hasOwnProperty,ae=new Nt("tag:yaml.org,2002:set",{kind:"mapping",resolve:function(t){if(null===t)return!0;var e,i=t;for(e in i)if(oe.call(i,e)&&null!==i[e])return!1;return!0},construct:function(t){return null!==t?t:{}}}),se=Yt.extend({implicit:[Xt,Jt],explicit:[Kt,ie,ne,ae]}),le=Object.prototype.hasOwnProperty,he=1,ce=2,ue=3,de=4,fe=1,pe=2,ge=3,me=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,ye=/[\x85\u2028\u2029]/,_e=/[,\[\]\{\}]/,be=/^(?:!|!!|![a-z\-]+!)$/i,Ce=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function xe(t){return Object.prototype.toString.call(t)}function ve(t){return 10===t||13===t}function ke(t){return 9===t||32===t}function Te(t){return 9===t||32===t||10===t||13===t}function we(t){return 44===t||91===t||93===t||123===t||125===t}function Se(t){var e;return 48<=t&&t<=57?t-48:97<=(e=32|t)&&e<=102?e-97+10:-1}function Be(t){return 48===t?"\0":97===t?"":98===t?"\b":116===t||9===t?"\t":110===t?"\n":118===t?"\v":102===t?"\f":114===t?"\r":101===t?"":32===t?" ":34===t?'"':47===t?"/":92===t?"\\":78===t?"":95===t?" ":76===t?"\u2028":80===t?"\u2029":""}function Fe(t){return t<=65535?String.fromCharCode(t):String.fromCharCode(55296+(t-65536>>10),56320+(t-65536&1023))}for(var Le=new Array(256),Me=new Array(256),Ae=0;Ae<256;Ae++)Le[Ae]=Be(Ae)?1:0,Me[Ae]=Be(Ae);function Ee(t,e){this.input=t,this.filename=e.filename||null,this.schema=e.schema||se,this.onWarning=e.onWarning||null,this.legacy=e.legacy||!1,this.json=e.json||!1,this.listener=e.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=t.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}function Oe(t,e){var i={name:t.filename,buffer:t.input.slice(0,-1),position:t.position,line:t.line,column:t.position-t.lineStart};return i.snippet=Et(i),new Lt(e,i)}function Ie(t,e){throw Oe(t,e)}function Ne(t,e){t.onWarning&&t.onWarning.call(null,Oe(t,e))}var qe={YAML:function(t,e,i){var r,n,o;null!==t.version&&Ie(t,"duplication of %YAML directive"),1!==i.length&&Ie(t,"YAML directive accepts exactly one argument"),null===(r=/^([0-9]+)\.([0-9]+)$/.exec(i[0]))&&Ie(t,"ill-formed argument of the YAML directive"),n=parseInt(r[1],10),o=parseInt(r[2],10),1!==n&&Ie(t,"unacceptable YAML version of the document"),t.version=i[0],t.checkLineBreaks=o<2,1!==o&&2!==o&&Ne(t,"unsupported YAML version of the document")},TAG:function(t,e,i){var r,n;2!==i.length&&Ie(t,"TAG directive accepts exactly two arguments"),r=i[0],n=i[1],be.test(r)||Ie(t,"ill-formed tag handle (first argument) of the TAG directive"),le.call(t.tagMap,r)&&Ie(t,'there is a previously declared suffix for "'+r+'" tag handle'),Ce.test(n)||Ie(t,"ill-formed tag prefix (second argument) of the TAG directive");try{n=decodeURIComponent(n)}catch(e){Ie(t,"tag prefix is malformed: "+n)}t.tagMap[r]=n}};function De(t,e,i,r){var n,o,a,s;if(e<i){if(s=t.input.slice(e,i),r)for(n=0,o=s.length;n<o;n+=1)9===(a=s.charCodeAt(n))||32<=a&&a<=1114111||Ie(t,"expected valid JSON character");else me.test(s)&&Ie(t,"the stream contains non-printable characters");t.result+=s}}function $e(t,e,i,r){var n,o,a,s;for(St.isObject(i)||Ie(t,"cannot merge mappings; the provided source object is unacceptable"),a=0,s=(n=Object.keys(i)).length;a<s;a+=1)o=n[a],le.call(e,o)||(e[o]=i[o],r[o]=!0)}function Ze(t,e,i,r,n,o,a,s,l){var h,c;if(Array.isArray(n))for(h=0,c=(n=Array.prototype.slice.call(n)).length;h<c;h+=1)Array.isArray(n[h])&&Ie(t,"nested arrays are not supported inside keys"),"object"==typeof n&&"[object Object]"===xe(n[h])&&(n[h]="[object Object]");if("object"==typeof n&&"[object Object]"===xe(n)&&(n="[object Object]"),n=String(n),null===e&&(e={}),"tag:yaml.org,2002:merge"===r)if(Array.isArray(o))for(h=0,c=o.length;h<c;h+=1)$e(t,e,o[h],i);else $e(t,e,o,i);else t.json||le.call(i,n)||!le.call(e,n)||(t.line=a||t.line,t.lineStart=s||t.lineStart,t.position=l||t.position,Ie(t,"duplicated mapping key")),"__proto__"===n?Object.defineProperty(e,n,{configurable:!0,enumerable:!0,writable:!0,value:o}):e[n]=o,delete i[n];return e}function ze(t){var e;10===(e=t.input.charCodeAt(t.position))?t.position++:13===e?(t.position++,10===t.input.charCodeAt(t.position)&&t.position++):Ie(t,"a line break is expected"),t.line+=1,t.lineStart=t.position,t.firstTabInLine=-1}function je(t,e,i){for(var r=0,n=t.input.charCodeAt(t.position);0!==n;){for(;ke(n);)9===n&&-1===t.firstTabInLine&&(t.firstTabInLine=t.position),n=t.input.charCodeAt(++t.position);if(e&&35===n)do{n=t.input.charCodeAt(++t.position)}while(10!==n&&13!==n&&0!==n);if(!ve(n))break;for(ze(t),n=t.input.charCodeAt(t.position),r++,t.lineIndent=0;32===n;)t.lineIndent++,n=t.input.charCodeAt(++t.position)}return-1!==i&&0!==r&&t.lineIndent<i&&Ne(t,"deficient indentation"),r}function Pe(t){var e,i=t.position;return!(45!==(e=t.input.charCodeAt(i))&&46!==e||e!==t.input.charCodeAt(i+1)||e!==t.input.charCodeAt(i+2)||(i+=3,0!==(e=t.input.charCodeAt(i))&&!Te(e)))}function Re(t,e){1===e?t.result+=" ":e>1&&(t.result+=St.repeat("\n",e-1))}function We(t,e){var i,r,n=t.tag,o=t.anchor,a=[],s=!1;if(-1!==t.firstTabInLine)return!1;for(null!==t.anchor&&(t.anchorMap[t.anchor]=a),r=t.input.charCodeAt(t.position);0!==r&&(-1!==t.firstTabInLine&&(t.position=t.firstTabInLine,Ie(t,"tab characters must not be used in indentation")),45===r)&&Te(t.input.charCodeAt(t.position+1));)if(s=!0,t.position++,je(t,!0,-1)&&t.lineIndent<=e)a.push(null),r=t.input.charCodeAt(t.position);else if(i=t.line,Ye(t,e,ue,!1,!0),a.push(t.result),je(t,!0,-1),r=t.input.charCodeAt(t.position),(t.line===i||t.lineIndent>e)&&0!==r)Ie(t,"bad indentation of a sequence entry");else if(t.lineIndent<e)break;return!!s&&(t.tag=n,t.anchor=o,t.kind="sequence",t.result=a,!0)}function Ue(t){var e,i,r,n,o=!1,a=!1;if(33!==(n=t.input.charCodeAt(t.position)))return!1;if(null!==t.tag&&Ie(t,"duplication of a tag property"),60===(n=t.input.charCodeAt(++t.position))?(o=!0,n=t.input.charCodeAt(++t.position)):33===n?(a=!0,i="!!",n=t.input.charCodeAt(++t.position)):i="!",e=t.position,o){do{n=t.input.charCodeAt(++t.position)}while(0!==n&&62!==n);t.position<t.length?(r=t.input.slice(e,t.position),n=t.input.charCodeAt(++t.position)):Ie(t,"unexpected end of the stream within a verbatim tag")}else{for(;0!==n&&!Te(n);)33===n&&(a?Ie(t,"tag suffix cannot contain exclamation marks"):(i=t.input.slice(e-1,t.position+1),be.test(i)||Ie(t,"named tag handle cannot contain such characters"),a=!0,e=t.position+1)),n=t.input.charCodeAt(++t.position);r=t.input.slice(e,t.position),_e.test(r)&&Ie(t,"tag suffix cannot contain flow indicator characters")}r&&!Ce.test(r)&&Ie(t,"tag name cannot contain such characters: "+r);try{r=decodeURIComponent(r)}catch(e){Ie(t,"tag name is malformed: "+r)}return o?t.tag=r:le.call(t.tagMap,i)?t.tag=t.tagMap[i]+r:"!"===i?t.tag="!"+r:"!!"===i?t.tag="tag:yaml.org,2002:"+r:Ie(t,'undeclared tag handle "'+i+'"'),!0}function He(t){var e,i;if(38!==(i=t.input.charCodeAt(t.position)))return!1;for(null!==t.anchor&&Ie(t,"duplication of an anchor property"),i=t.input.charCodeAt(++t.position),e=t.position;0!==i&&!Te(i)&&!we(i);)i=t.input.charCodeAt(++t.position);return t.position===e&&Ie(t,"name of an anchor node must contain at least one character"),t.anchor=t.input.slice(e,t.position),!0}function Ye(t,e,i,r,n){var o,a,s,l,h,c,u,d,f,p=1,g=!1,m=!1;if(null!==t.listener&&t.listener("open",t),t.tag=null,t.anchor=null,t.kind=null,t.result=null,o=a=s=de===i||ue===i,r&&je(t,!0,-1)&&(g=!0,t.lineIndent>e?p=1:t.lineIndent===e?p=0:t.lineIndent<e&&(p=-1)),1===p)for(;Ue(t)||He(t);)je(t,!0,-1)?(g=!0,s=o,t.lineIndent>e?p=1:t.lineIndent===e?p=0:t.lineIndent<e&&(p=-1)):s=!1;if(s&&(s=g||n),1!==p&&de!==i||(d=he===i||ce===i?e:e+1,f=t.position-t.lineStart,1===p?s&&(We(t,f)||function(t,e,i){var r,n,o,a,s,l,h,c=t.tag,u=t.anchor,d={},f=Object.create(null),p=null,g=null,m=null,y=!1,_=!1;if(-1!==t.firstTabInLine)return!1;for(null!==t.anchor&&(t.anchorMap[t.anchor]=d),h=t.input.charCodeAt(t.position);0!==h;){if(y||-1===t.firstTabInLine||(t.position=t.firstTabInLine,Ie(t,"tab characters must not be used in indentation")),r=t.input.charCodeAt(t.position+1),o=t.line,63!==h&&58!==h||!Te(r)){if(a=t.line,s=t.lineStart,l=t.position,!Ye(t,i,ce,!1,!0))break;if(t.line===o){for(h=t.input.charCodeAt(t.position);ke(h);)h=t.input.charCodeAt(++t.position);if(58===h)Te(h=t.input.charCodeAt(++t.position))||Ie(t,"a whitespace character is expected after the key-value separator within a block mapping"),y&&(Ze(t,d,f,p,g,null,a,s,l),p=g=m=null),_=!0,y=!1,n=!1,p=t.tag,g=t.result;else{if(!_)return t.tag=c,t.anchor=u,!0;Ie(t,"can not read an implicit mapping pair; a colon is missed")}}else{if(!_)return t.tag=c,t.anchor=u,!0;Ie(t,"can not read a block mapping entry; a multiline key may not be an implicit key")}}else 63===h?(y&&(Ze(t,d,f,p,g,null,a,s,l),p=g=m=null),_=!0,y=!0,n=!0):y?(y=!1,n=!0):Ie(t,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),t.position+=1,h=r;if((t.line===o||t.lineIndent>e)&&(y&&(a=t.line,s=t.lineStart,l=t.position),Ye(t,e,de,!0,n)&&(y?g=t.result:m=t.result),y||(Ze(t,d,f,p,g,m,a,s,l),p=g=m=null),je(t,!0,-1),h=t.input.charCodeAt(t.position)),(t.line===o||t.lineIndent>e)&&0!==h)Ie(t,"bad indentation of a mapping entry");else if(t.lineIndent<e)break}return y&&Ze(t,d,f,p,g,null,a,s,l),_&&(t.tag=c,t.anchor=u,t.kind="mapping",t.result=d),_}(t,f,d))||function(t,e){var i,r,n,o,a,s,l,h,c,u,d,f,p=!0,g=t.tag,m=t.anchor,y=Object.create(null);if(91===(f=t.input.charCodeAt(t.position)))a=93,h=!1,o=[];else{if(123!==f)return!1;a=125,h=!0,o={}}for(null!==t.anchor&&(t.anchorMap[t.anchor]=o),f=t.input.charCodeAt(++t.position);0!==f;){if(je(t,!0,e),(f=t.input.charCodeAt(t.position))===a)return t.position++,t.tag=g,t.anchor=m,t.kind=h?"mapping":"sequence",t.result=o,!0;p?44===f&&Ie(t,"expected the node content, but found ','"):Ie(t,"missed comma between flow collection entries"),d=null,s=l=!1,63===f&&Te(t.input.charCodeAt(t.position+1))&&(s=l=!0,t.position++,je(t,!0,e)),i=t.line,r=t.lineStart,n=t.position,Ye(t,e,he,!1,!0),u=t.tag,c=t.result,je(t,!0,e),f=t.input.charCodeAt(t.position),!l&&t.line!==i||58!==f||(s=!0,f=t.input.charCodeAt(++t.position),je(t,!0,e),Ye(t,e,he,!1,!0),d=t.result),h?Ze(t,o,y,u,c,d,i,r,n):s?o.push(Ze(t,null,y,u,c,d,i,r,n)):o.push(c),je(t,!0,e),44===(f=t.input.charCodeAt(t.position))?(p=!0,f=t.input.charCodeAt(++t.position)):p=!1}Ie(t,"unexpected end of the stream within a flow collection")}(t,d)?m=!0:(a&&function(t,e){var i,r,n,o,a,s=fe,l=!1,h=!1,c=e,u=0,d=!1;if(124===(o=t.input.charCodeAt(t.position)))r=!1;else{if(62!==o)return!1;r=!0}for(t.kind="scalar",t.result="";0!==o;)if(43===(o=t.input.charCodeAt(++t.position))||45===o)fe===s?s=43===o?ge:pe:Ie(t,"repeat of a chomping mode identifier");else{if(!((n=48<=(a=o)&&a<=57?a-48:-1)>=0))break;0===n?Ie(t,"bad explicit indentation width of a block scalar; it cannot be less than one"):h?Ie(t,"repeat of an indentation width identifier"):(c=e+n-1,h=!0)}if(ke(o)){do{o=t.input.charCodeAt(++t.position)}while(ke(o));if(35===o)do{o=t.input.charCodeAt(++t.position)}while(!ve(o)&&0!==o)}for(;0!==o;){for(ze(t),t.lineIndent=0,o=t.input.charCodeAt(t.position);(!h||t.lineIndent<c)&&32===o;)t.lineIndent++,o=t.input.charCodeAt(++t.position);if(!h&&t.lineIndent>c&&(c=t.lineIndent),ve(o))u++;else{if(t.lineIndent<c){s===ge?t.result+=St.repeat("\n",l?1+u:u):s===fe&&l&&(t.result+="\n");break}for(r?ke(o)?(d=!0,t.result+=St.repeat("\n",l?1+u:u)):d?(d=!1,t.result+=St.repeat("\n",u+1)):0===u?l&&(t.result+=" "):t.result+=St.repeat("\n",u):t.result+=St.repeat("\n",l?1+u:u),l=!0,h=!0,u=0,i=t.position;!ve(o)&&0!==o;)o=t.input.charCodeAt(++t.position);De(t,i,t.position,!1)}}return!0}(t,d)||function(t,e){var i,r,n;if(39!==(i=t.input.charCodeAt(t.position)))return!1;for(t.kind="scalar",t.result="",t.position++,r=n=t.position;0!==(i=t.input.charCodeAt(t.position));)if(39===i){if(De(t,r,t.position,!0),39!==(i=t.input.charCodeAt(++t.position)))return!0;r=t.position,t.position++,n=t.position}else ve(i)?(De(t,r,n,!0),Re(t,je(t,!1,e)),r=n=t.position):t.position===t.lineStart&&Pe(t)?Ie(t,"unexpected end of the document within a single quoted scalar"):(t.position++,n=t.position);Ie(t,"unexpected end of the stream within a single quoted scalar")}(t,d)||function(t,e){var i,r,n,o,a,s,l;if(34!==(s=t.input.charCodeAt(t.position)))return!1;for(t.kind="scalar",t.result="",t.position++,i=r=t.position;0!==(s=t.input.charCodeAt(t.position));){if(34===s)return De(t,i,t.position,!0),t.position++,!0;if(92===s){if(De(t,i,t.position,!0),ve(s=t.input.charCodeAt(++t.position)))je(t,!1,e);else if(s<256&&Le[s])t.result+=Me[s],t.position++;else if((a=120===(l=s)?2:117===l?4:85===l?8:0)>0){for(n=a,o=0;n>0;n--)(a=Se(s=t.input.charCodeAt(++t.position)))>=0?o=(o<<4)+a:Ie(t,"expected hexadecimal character");t.result+=Fe(o),t.position++}else Ie(t,"unknown escape sequence");i=r=t.position}else ve(s)?(De(t,i,r,!0),Re(t,je(t,!1,e)),i=r=t.position):t.position===t.lineStart&&Pe(t)?Ie(t,"unexpected end of the document within a double quoted scalar"):(t.position++,r=t.position)}Ie(t,"unexpected end of the stream within a double quoted scalar")}(t,d)?m=!0:function(t){var e,i,r;if(42!==(r=t.input.charCodeAt(t.position)))return!1;for(r=t.input.charCodeAt(++t.position),e=t.position;0!==r&&!Te(r)&&!we(r);)r=t.input.charCodeAt(++t.position);return t.position===e&&Ie(t,"name of an alias node must contain at least one character"),i=t.input.slice(e,t.position),le.call(t.anchorMap,i)||Ie(t,'unidentified alias "'+i+'"'),t.result=t.anchorMap[i],je(t,!0,-1),!0}(t)?(m=!0,null===t.tag&&null===t.anchor||Ie(t,"alias node should not have any properties")):function(t,e,i){var r,n,o,a,s,l,h,c,u=t.kind,d=t.result;if(Te(c=t.input.charCodeAt(t.position))||we(c)||35===c||38===c||42===c||33===c||124===c||62===c||39===c||34===c||37===c||64===c||96===c)return!1;if((63===c||45===c)&&(Te(r=t.input.charCodeAt(t.position+1))||i&&we(r)))return!1;for(t.kind="scalar",t.result="",n=o=t.position,a=!1;0!==c;){if(58===c){if(Te(r=t.input.charCodeAt(t.position+1))||i&&we(r))break}else if(35===c){if(Te(t.input.charCodeAt(t.position-1)))break}else{if(t.position===t.lineStart&&Pe(t)||i&&we(c))break;if(ve(c)){if(s=t.line,l=t.lineStart,h=t.lineIndent,je(t,!1,-1),t.lineIndent>=e){a=!0,c=t.input.charCodeAt(t.position);continue}t.position=o,t.line=s,t.lineStart=l,t.lineIndent=h;break}}a&&(De(t,n,o,!1),Re(t,t.line-s),n=o=t.position,a=!1),ke(c)||(o=t.position+1),c=t.input.charCodeAt(++t.position)}return De(t,n,o,!1),!!t.result||(t.kind=u,t.result=d,!1)}(t,d,he===i)&&(m=!0,null===t.tag&&(t.tag="?")),null!==t.anchor&&(t.anchorMap[t.anchor]=t.result)):0===p&&(m=s&&We(t,f))),null===t.tag)null!==t.anchor&&(t.anchorMap[t.anchor]=t.result);else if("?"===t.tag){for(null!==t.result&&"scalar"!==t.kind&&Ie(t,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+t.kind+'"'),l=0,h=t.implicitTypes.length;l<h;l+=1)if((u=t.implicitTypes[l]).resolve(t.result)){t.result=u.construct(t.result),t.tag=u.tag,null!==t.anchor&&(t.anchorMap[t.anchor]=t.result);break}}else if("!"!==t.tag){if(le.call(t.typeMap[t.kind||"fallback"],t.tag))u=t.typeMap[t.kind||"fallback"][t.tag];else for(u=null,l=0,h=(c=t.typeMap.multi[t.kind||"fallback"]).length;l<h;l+=1)if(t.tag.slice(0,c[l].tag.length)===c[l].tag){u=c[l];break}u||Ie(t,"unknown tag !<"+t.tag+">"),null!==t.result&&u.kind!==t.kind&&Ie(t,"unacceptable node kind for !<"+t.tag+'> tag; it should be "'+u.kind+'", not "'+t.kind+'"'),u.resolve(t.result,t.tag)?(t.result=u.construct(t.result,t.tag),null!==t.anchor&&(t.anchorMap[t.anchor]=t.result)):Ie(t,"cannot resolve a node with !<"+t.tag+"> explicit tag")}return null!==t.listener&&t.listener("close",t),null!==t.tag||null!==t.anchor||m}function Ve(t){var e,i,r,n,o=t.position,a=!1;for(t.version=null,t.checkLineBreaks=t.legacy,t.tagMap=Object.create(null),t.anchorMap=Object.create(null);0!==(n=t.input.charCodeAt(t.position))&&(je(t,!0,-1),n=t.input.charCodeAt(t.position),!(t.lineIndent>0||37!==n));){for(a=!0,n=t.input.charCodeAt(++t.position),e=t.position;0!==n&&!Te(n);)n=t.input.charCodeAt(++t.position);for(r=[],(i=t.input.slice(e,t.position)).length<1&&Ie(t,"directive name must not be less than one character in length");0!==n;){for(;ke(n);)n=t.input.charCodeAt(++t.position);if(35===n){do{n=t.input.charCodeAt(++t.position)}while(0!==n&&!ve(n));break}if(ve(n))break;for(e=t.position;0!==n&&!Te(n);)n=t.input.charCodeAt(++t.position);r.push(t.input.slice(e,t.position))}0!==n&&ze(t),le.call(qe,i)?qe[i](t,i,r):Ne(t,'unknown document directive "'+i+'"')}je(t,!0,-1),0===t.lineIndent&&45===t.input.charCodeAt(t.position)&&45===t.input.charCodeAt(t.position+1)&&45===t.input.charCodeAt(t.position+2)?(t.position+=3,je(t,!0,-1)):a&&Ie(t,"directives end mark is expected"),Ye(t,t.lineIndent-1,de,!1,!0),je(t,!0,-1),t.checkLineBreaks&&ye.test(t.input.slice(o,t.position))&&Ne(t,"non-ASCII line breaks are interpreted as content"),t.documents.push(t.result),t.position===t.lineStart&&Pe(t)?46===t.input.charCodeAt(t.position)&&(t.position+=3,je(t,!0,-1)):t.position<t.length-1&&Ie(t,"end of the stream or a document separator is expected")}function Ge(t,e){e=e||{},0!==(t=String(t)).length&&(10!==t.charCodeAt(t.length-1)&&13!==t.charCodeAt(t.length-1)&&(t+="\n"),65279===t.charCodeAt(0)&&(t=t.slice(1)));var i=new Ee(t,e),r=t.indexOf("\0");for(-1!==r&&(i.position=r,Ie(i,"null byte is not allowed in input")),i.input+="\0";32===i.input.charCodeAt(i.position);)i.lineIndent+=1,i.position+=1;for(;i.position<i.length-1;)Ve(i);return i.documents}var Xe=$t,Je=function(t,e){var i=Ge(t,e);if(0!==i.length){if(1===i.length)return i[0];throw new Lt("expected a single document in the stream, but found more")}};const Qe=/^-{3}\s*[\n\r](.*?)[\n\r]-{3}\s*[\n\r]+/s,Ke=function(t,e,i){const{depth:r,clobber:n}=Object.assign({depth:2,clobber:!1},i);return Array.isArray(e)&&!Array.isArray(t)?(e.forEach((e=>Ke(t,e,i))),t):Array.isArray(e)&&Array.isArray(t)?(e.forEach((e=>{t.includes(e)||t.push(e)})),t):void 0===t||r<=0?null!=t&&"object"==typeof t&&"object"==typeof e?Object.assign(t,e):e:(void 0!==e&&"object"==typeof t&&"object"==typeof e&&Object.keys(e).forEach((i=>{"object"!=typeof e[i]||void 0!==t[i]&&"object"!=typeof t[i]?(n||"object"!=typeof t[i]&&"object"!=typeof e[i])&&(t[i]=e[i]):(void 0===t[i]&&(t[i]=Array.isArray(e[i])?[]:{}),t[i]=Ke(t[i],e[i],{depth:r-1,clobber:n}))})),t)},ti=Ke,ei=Object.freeze(Tt);let ii,ri=ti({},ei),ni=[],oi=ti({},ei);const ai=(t,e)=>{let i=ti({},t),r={};for(const t of e)ci(t),r=ti(r,t);if(i=ti(i,r),r.theme&&r.theme in Ct){const t=ti({},ii),e=ti(t.themeVariables||{},r.themeVariables);i.theme&&i.theme in Ct&&(i.themeVariables=Ct[i.theme].getThemeVariables(e))}return oi=i,gi(oi),oi},si=()=>ti({},ri),li=t=>(gi(t),ti(oi,t),hi()),hi=()=>ti({},oi),ci=t=>{["secure",...ri.secure??[]].forEach((e=>{void 0!==t[e]&&(rt.debug(`Denied attempt to modify a secure key ${e}`,t[e]),delete t[e])})),Object.keys(t).forEach((e=>{0===e.indexOf("__")&&delete t[e]})),Object.keys(t).forEach((e=>{"string"==typeof t[e]&&(t[e].includes("<")||t[e].includes(">")||t[e].includes("url(data:"))&&delete t[e],"object"==typeof t[e]&&ci(t[e])}))},ui=t=>{t.fontFamily&&(t.themeVariables&&t.themeVariables.fontFamily||(t.themeVariables={fontFamily:t.fontFamily})),ni.push(t),ai(ri,ni)},di=(t=ri)=>{ni=[],ai(t,ni)};var fi=(t=>(t.LAZY_LOAD_DEPRECATED="The configuration options lazyLoadedDiagrams and loadExternalDiagramsAtStartup are deprecated. Please use registerExternalDiagrams instead.",t))(fi||{});const pi={},gi=t=>{var e;t&&(t.lazyLoadedDiagrams||t.loadExternalDiagramsAtStartup)&&(pi[e="LAZY_LOAD_DEPRECATED"]||(rt.warn(fi[e]),pi[e]=!0))},mi=function(t,e,i,r){const n=function(t,e,i){let r=new Map;return i?(r.set("width","100%"),r.set("style",`max-width: ${e}px;`)):(r.set("height",t),r.set("width",e)),r}(e,i,r);!function(t,e){for(let i of e)t.attr(i[0],i[1])}(t,n)},yi=function(t,e,i,r){const n=e.node().getBBox(),o=n.width,a=n.height;rt.info(`SVG bounds: ${o}x${a}`,n);let s=0,l=0;rt.info(`Graph bounds: ${s}x${l}`,t),s=o+2*i,l=a+2*i,rt.info(`Calculated bounds: ${s}x${l}`),mi(e,l,s,r);const h=`${n.x-i} ${n.y-i} ${n.width+2*i} ${n.height+2*i}`;e.attr("viewBox",h)},_i={};let bi="",Ci="",xi="";const vi=t=>ht(t,hi()),ki=function(){bi="",xi="",Ci=""},Ti=function(t){bi=vi(t).replace(/^\s+/g,"")},wi=function(){return bi||Ci},Si=function(t){xi=vi(t).replace(/\n\s+/g,"\n")},Bi=function(){return xi},Fi=function(t){Ci=vi(t)},Li=function(){return Ci},Mi={getAccTitle:wi,setAccTitle:Ti,getDiagramTitle:Li,setDiagramTitle:Fi,getAccDescription:Bi,setAccDescription:Si,clear:ki},Ai=Object.freeze(Object.defineProperty({__proto__:null,clear:ki,default:Mi,getAccDescription:Bi,getAccTitle:wi,getDiagramTitle:Li,setAccDescription:Si,setAccTitle:Ti,setDiagramTitle:Fi},Symbol.toStringTag,{value:"Module"}));let Ei={};const Oi=function(t,e,i,r){rt.debug("parseDirective is being called",e,i,r);try{if(void 0!==e)switch(e=e.trim(),i){case"open_directive":Ei={};break;case"type_directive":if(!Ei)throw new Error("currentDirective is undefined");Ei.type=e.toLowerCase();break;case"arg_directive":if(!Ei)throw new Error("currentDirective is undefined");Ei.args=JSON.parse(e);break;case"close_directive":Ii(t,Ei,r),Ei=void 0}}catch(t){rt.error(`Error while rendering sequenceDiagram directive: ${e} jison context: ${i}`),rt.error(t.message)}},Ii=function(t,e,i){switch(rt.info(`Directive type=${e.type} with args:`,e.args),e.type){case"init":case"initialize":["config"].forEach((t=>{void 0!==e.args[t]&&("flowchart-v2"===i&&(i="flowchart"),e.args[i]=e.args[t],delete e.args[t])})),rt.info("sanitize in handleDirective",e.args),gr(e.args),rt.info("sanitize in handleDirective (done)",e.args),ui(e.args);break;case"wrap":case"nowrap":t&&t.setWrap&&t.setWrap("wrap"===e.type);break;case"themeCss":rt.warn("themeCss encountered");break;default:rt.warn(`Unhandled directive: source: '%%{${e.type}: ${JSON.stringify(e.args?e.args:{})}}%%`,e)}},Ni=rt,qi=nt,Di=hi,$i=t=>ht(t,Di()),Zi=yi,zi=(t,e,i,r)=>Oi(t,e,i,r),ji={},Pi=(t,e,i)=>{if(ji[t])throw new Error(`Diagram ${t} already registered.`);var r,n;ji[t]=e,i&&Xi(t,i),r=t,void 0!==(n=e.styles)&&(_i[r]=n),e.injectUtils&&e.injectUtils(Ni,qi,Di,$i,Zi,Ai,zi)},Ri=t=>{if(t in ji)return ji[t];throw new Error(`Diagram ${t} not found.`)};class Wi extends Error{constructor(t){super(t),this.name="UnknownDiagramError"}}const Ui=/%{2}{\s*(?:(\w+)\s*:|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,Hi=/\s*%%.*\n/gm,Yi={},Vi=function(t,e){t=t.replace(Qe,"").replace(Ui,"").replace(Hi,"\n");for(const[i,{detector:r}]of Object.entries(Yi))if(r(t,e))return i;throw new Wi(`No diagram type detected matching given configuration for text: ${t}`)},Gi=(...t)=>{for(const{id:e,detector:i,loader:r}of t)Xi(e,i,r)},Xi=(t,e,i)=>{Yi[t]?rt.error(`Detector with key ${t} already exists`):Yi[t]={detector:e,loader:i},rt.debug(`Detector with key ${t} added${i?" with loader":""}`)},Ji="​",Qi={curveBasis:a.$0Z,curveBasisClosed:a.Dts,curveBasisOpen:a.WQY,curveBumpX:a.qpX,curveBumpY:a.u93,curveBundle:a.tFB,curveCardinalClosed:a.OvA,curveCardinalOpen:a.dCK,curveCardinal:a.YY7,curveCatmullRomClosed:a.fGX,curveCatmullRomOpen:a.$m7,curveCatmullRom:a.zgE,curveLinear:a.c_6,curveLinearClosed:a.fxm,curveMonotoneX:a.FdL,curveMonotoneY:a.ak_,curveNatural:a.SxZ,curveStep:a.eA_,curveStepAfter:a.jsv,curveStepBefore:a.iJ},Ki=/%{2}{\s*(?:(\w+)\s*:|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,tr=/\s*(?:(\w+)(?=:):|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,er=function(t,e=null){try{const i=new RegExp(`[%]{2}(?![{]${tr.source})(?=[}][%]{2}).*\n`,"ig");let r;t=t.trim().replace(i,"").replace(/'/gm,'"'),rt.debug(`Detecting diagram directive${null!==e?" type:"+e:""} based on the text:${t}`);const n=[];for(;null!==(r=Ki.exec(t));)if(r.index===Ki.lastIndex&&Ki.lastIndex++,r&&!e||e&&r[1]&&r[1].match(e)||e&&r[2]&&r[2].match(e)){const t=r[1]?r[1]:r[2],e=r[3]?r[3].trim():r[4]?JSON.parse(r[4].trim()):null;n.push({type:t,args:e})}return 0===n.length&&n.push({type:t,args:null}),1===n.length?n[0]:n}catch(i){return rt.error(`ERROR: ${i.message} - Unable to parse directive\n      ${null!==e?" type:"+e:""} based on the text:${t}`),{type:null,args:null}}};function ir(t,e){if(!t)return e;const i=`curve${t.charAt(0).toUpperCase()+t.slice(1)}`;return Qi[i]||e}function rr(t,e){return t&&e?Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2)):0}function nr(t){let e="",i="";for(const r of t)void 0!==r&&(r.startsWith("color:")||r.startsWith("text-align:")?i=i+r+";":e=e+r+";");return{style:e,labelStyle:i}}let or=0;const ar=()=>(or++,"id-"+Math.random().toString(36).substr(2,12)+"-"+or),sr=t=>function(t){let e="";for(let i=0;i<t;i++)e+="0123456789abcdef".charAt(Math.floor(16*Math.random()));return e}(t.length),lr=function(t,e){const i=e.text.replace(pt.lineBreakRegex," "),[,r]=_r(e.fontSize),n=t.append("text");n.attr("x",e.x),n.attr("y",e.y),n.style("text-anchor",e.anchor),n.style("font-family",e.fontFamily),n.style("font-size",r),n.style("font-weight",e.fontWeight),n.attr("fill",e.fill),void 0!==e.class&&n.attr("class",e.class);const o=n.append("tspan");return o.attr("x",e.x+2*e.textMargin),o.attr("fill",e.fill),o.text(i),n},hr=(0,m.Z)(((t,e,i)=>{if(!t)return t;if(i=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",joinWith:"<br/>"},i),pt.lineBreakRegex.test(t))return t;const r=t.split(" "),n=[];let o="";return r.forEach(((t,a)=>{const s=dr(`${t} `,i),l=dr(o,i);if(s>e){const{hyphenatedStrings:r,remainingWord:a}=cr(t,e,"-",i);n.push(o,...r),o=a}else l+s>=e?(n.push(o),o=t):o=[o,t].filter(Boolean).join(" ");a+1===r.length&&n.push(o)})),n.filter((t=>""!==t)).join(i.joinWith)}),((t,e,i)=>`${t}${e}${i.fontSize}${i.fontWeight}${i.fontFamily}${i.joinWith}`)),cr=(0,m.Z)(((t,e,i="-",r)=>{r=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",margin:0},r);const n=[...t],o=[];let a="";return n.forEach(((t,s)=>{const l=`${a}${t}`;if(dr(l,r)>=e){const t=s+1,e=n.length===t,r=`${l}${i}`;o.push(e?l:r),a=""}else a=l})),{hyphenatedStrings:o,remainingWord:a}}),((t,e,i="-",r)=>`${t}${e}${i}${r.fontSize}${r.fontWeight}${r.fontFamily}`));function ur(t,e){return e=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",margin:15},e),fr(t,e).height}function dr(t,e){return e=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial"},e),fr(t,e).width}const fr=(0,m.Z)(((t,e)=>{e=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial"},e);const{fontSize:i,fontFamily:r,fontWeight:n}=e;if(!t)return{width:0,height:0};const[,o]=_r(i),s=["sans-serif",r],l=t.split(pt.lineBreakRegex),h=[],c=(0,a.Ys)("body");if(!c.remove)return{width:0,height:0,lineHeight:0};const u=c.append("svg");for(const t of s){let e=0;const i={width:0,height:0,lineHeight:0};for(const r of l){const a={x:0,y:0,fill:void 0,anchor:"start",style:"#666",width:100,height:100,textMargin:0,rx:0,ry:0,valign:void 0};a.text=r||Ji;const s=lr(u,a).style("font-size",o).style("font-weight",n).style("font-family",t),l=(s._groups||s)[0][0].getBBox();if(0===l.width&&0===l.height)throw new Error("svg element not in render tree");i.width=Math.round(Math.max(i.width,l.width)),e=Math.round(l.height),i.height+=e,i.lineHeight=Math.round(Math.max(i.lineHeight,e))}h.push(i)}return u.remove(),h[isNaN(h[1].height)||isNaN(h[1].width)||isNaN(h[1].lineHeight)||h[0].height>h[1].height&&h[0].width>h[1].width&&h[0].lineHeight>h[1].lineHeight?0:1]}),((t,e)=>`${t}${e.fontSize}${e.fontWeight}${e.fontFamily}`));let pr;const gr=t=>{if(rt.debug("directiveSanitizer called with",t),"object"==typeof t&&(t.length?t.forEach((t=>gr(t))):Object.keys(t).forEach((e=>{rt.debug("Checking key",e),e.startsWith("__")&&(rt.debug("sanitize deleting __ option",e),delete t[e]),e.includes("proto")&&(rt.debug("sanitize deleting proto option",e),delete t[e]),e.includes("constr")&&(rt.debug("sanitize deleting constr option",e),delete t[e]),e.includes("themeCSS")&&(rt.debug("sanitizing themeCss option"),t[e]=mr(t[e])),e.includes("fontFamily")&&(rt.debug("sanitizing fontFamily option"),t[e]=mr(t[e])),e.includes("altFontFamily")&&(rt.debug("sanitizing altFontFamily option"),t[e]=mr(t[e])),kt.includes(e)?"object"==typeof t[e]&&(rt.debug("sanitize deleting object",e),gr(t[e])):(rt.debug("sanitize deleting option",e),delete t[e])}))),t.themeVariables){const e=Object.keys(t.themeVariables);for(const i of e){const e=t.themeVariables[i];e&&e.match&&!e.match(/^[\d "#%(),.;A-Za-z]+$/)&&(t.themeVariables[i]="")}}rt.debug("After sanitization",t)},mr=t=>{let e=0,i=0;for(const r of t){if(e<i)return"{ /* ERROR: Unbalanced CSS */ }";"{"===r?e++:"}"===r&&i++}return e!==i?"{ /* ERROR: Unbalanced CSS */ }":t};function yr(t){return"str"in t}const _r=t=>{if("number"==typeof t)return[t,t+"px"];const e=parseInt(t,10);return Number.isNaN(e)?[void 0,void 0]:t===String(e)?[e,t+"px"]:[e,t]},br={assignWithDepth:ti,wrapLabel:hr,calculateTextHeight:ur,calculateTextWidth:dr,calculateTextDimensions:fr,detectInit:function(t,e){const i=er(t,/(?:init\b)|(?:initialize\b)/);let r={};if(Array.isArray(i)){const t=i.map((t=>t.args));gr(t),r=ti(r,[...t])}else r=i.args;if(r){let i=Vi(t,e);["config"].forEach((t=>{void 0!==r[t]&&("flowchart-v2"===i&&(i="flowchart"),r[i]=r[t],delete r[t])}))}return r},detectDirective:er,isSubstringInArray:function(t,e){for(const[i,r]of e.entries())if(r.match(t))return i;return-1},interpolateToCurve:ir,calcLabelPosition:function(t){return 1===t.length?t[0]:function(t){let e,i=0;t.forEach((t=>{i+=rr(t,e),e=t}));let r,n=i/2;return e=void 0,t.forEach((t=>{if(e&&!r){const i=rr(t,e);if(i<n)n-=i;else{const o=n/i;o<=0&&(r=e),o>=1&&(r={x:t.x,y:t.y}),o>0&&o<1&&(r={x:(1-o)*e.x+o*t.x,y:(1-o)*e.y+o*t.y})}}e=t})),r}(t)},calcCardinalityPosition:(t,e,i)=>{let r;rt.info(`our points ${JSON.stringify(e)}`),e[0]!==i&&(e=e.reverse());let n,o=25;r=void 0,e.forEach((t=>{if(r&&!n){const e=rr(t,r);if(e<o)o-=e;else{const i=o/e;i<=0&&(n=r),i>=1&&(n={x:t.x,y:t.y}),i>0&&i<1&&(n={x:(1-i)*r.x+i*t.x,y:(1-i)*r.y+i*t.y})}}r=t}));const a=t?10:5,s=Math.atan2(e[0].y-n.y,e[0].x-n.x),l={x:0,y:0};return l.x=Math.sin(s)*a+(e[0].x+n.x)/2,l.y=-Math.cos(s)*a+(e[0].y+n.y)/2,l},calcTerminalLabelPosition:function(t,e,i){let r,n=JSON.parse(JSON.stringify(i));rt.info("our points",n),"start_left"!==e&&"start_right"!==e&&(n=n.reverse()),n.forEach((t=>{r=t}));let o,a=25+t;r=void 0,n.forEach((t=>{if(r&&!o){const e=rr(t,r);if(e<a)a-=e;else{const i=a/e;i<=0&&(o=r),i>=1&&(o={x:t.x,y:t.y}),i>0&&i<1&&(o={x:(1-i)*r.x+i*t.x,y:(1-i)*r.y+i*t.y})}}r=t}));const s=10+.5*t,l=Math.atan2(n[0].y-o.y,n[0].x-o.x),h={x:0,y:0};return h.x=Math.sin(l)*s+(n[0].x+o.x)/2,h.y=-Math.cos(l)*s+(n[0].y+o.y)/2,"start_left"===e&&(h.x=Math.sin(l+Math.PI)*s+(n[0].x+o.x)/2,h.y=-Math.cos(l+Math.PI)*s+(n[0].y+o.y)/2),"end_right"===e&&(h.x=Math.sin(l-Math.PI)*s+(n[0].x+o.x)/2-5,h.y=-Math.cos(l-Math.PI)*s+(n[0].y+o.y)/2-5),"end_left"===e&&(h.x=Math.sin(l)*s+(n[0].x+o.x)/2-5,h.y=-Math.cos(l)*s+(n[0].y+o.y)/2-5),h},formatUrl:function(t,e){const i=t.trim();if(i)return"loose"!==e.securityLevel?(0,o.N)(i):i},getStylesFromArray:nr,generateId:ar,random:sr,runFunc:(t,...e)=>{const i=t.split("."),r=i.length-1,n=i[r];let o=window;for(let t=0;t<r;t++)if(o=o[i[t]],!o)return;o[n](...e)},entityDecode:function(t){return pr=pr||document.createElement("div"),t=escape(t).replace(/%26/g,"&").replace(/%23/g,"#").replace(/%3B/g,";"),pr.innerHTML=t,unescape(pr.textContent)},initIdGenerator:class{constructor(t,e){this.deterministic=t,this.seed=e,this.count=e?e.length:0}next(){return this.deterministic?this.count++:Date.now()}},directiveSanitizer:gr,sanitizeCss:mr,insertTitle:(t,e,i,r)=>{if(!r)return;const n=t.node().getBBox();t.append("text").text(r).attr("x",n.x+n.width/2).attr("y",-i).attr("class",e)},parseFontSize:_r},Cr="10.2.4",xr={id:"c4",detector:t=>/^\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(t),loader:async()=>{const{diagram:t}=await i.e(648).then(i.bind(i,9648));return{id:"c4",diagram:t}}},vr="flowchart",kr={id:vr,detector:(t,e)=>{var i,r;return"dagre-wrapper"!==(null==(i=null==e?void 0:e.flowchart)?void 0:i.defaultRenderer)&&"elk"!==(null==(r=null==e?void 0:e.flowchart)?void 0:r.defaultRenderer)&&/^\s*graph/.test(t)},loader:async()=>{const{diagram:t}=await Promise.all([i.e(451),i.e(105),i.e(264),i.e(759),i.e(796),i.e(177)]).then(i.bind(i,3177));return{id:vr,diagram:t}}},Tr="flowchart-v2",wr={id:Tr,detector:(t,e)=>{var i,r,n;return"dagre-d3"!==(null==(i=null==e?void 0:e.flowchart)?void 0:i.defaultRenderer)&&"elk"!==(null==(r=null==e?void 0:e.flowchart)?void 0:r.defaultRenderer)&&(!(!/^\s*graph/.test(t)||"dagre-wrapper"!==(null==(n=null==e?void 0:e.flowchart)?void 0:n.defaultRenderer))||/^\s*flowchart/.test(t))},loader:async()=>{const{diagram:t}=await Promise.all([i.e(451),i.e(105),i.e(264),i.e(759),i.e(796),i.e(209)]).then(i.bind(i,5209));return{id:Tr,diagram:t}}},Sr={id:"er",detector:t=>/^\s*erDiagram/.test(t),loader:async()=>{const{diagram:t}=await Promise.all([i.e(451),i.e(435)]).then(i.bind(i,7435));return{id:"er",diagram:t}}},Br="gitGraph",Fr={id:Br,detector:t=>/^\s*gitGraph/.test(t),loader:async()=>{const{diagram:t}=await i.e(978).then(i.bind(i,6978));return{id:Br,diagram:t}}},Lr="gantt",Mr={id:Lr,detector:t=>/^\s*gantt/.test(t),loader:async()=>{const{diagram:t}=await i.e(670).then(i.bind(i,670));return{id:Lr,diagram:t}}},Ar="info",Er={id:Ar,detector:t=>/^\s*info/.test(t),loader:async()=>{const{diagram:t}=await i.e(880).then(i.bind(i,5880));return{id:Ar,diagram:t}}},Or={id:"pie",detector:t=>/^\s*pie/.test(t),loader:async()=>{const{diagram:t}=await i.e(851).then(i.bind(i,9851));return{id:"pie",diagram:t}}},Ir="quadrantChart",Nr={id:Ir,detector:t=>/^\s*quadrantChart/.test(t),loader:async()=>{const{diagram:t}=await i.e(311).then(i.bind(i,3311));return{id:Ir,diagram:t}}},qr="requirement",Dr={id:qr,detector:t=>/^\s*requirement(Diagram)?/.test(t),loader:async()=>{const{diagram:t}=await Promise.all([i.e(451),i.e(422)]).then(i.bind(i,5422));return{id:qr,diagram:t}}},$r="sequence",Zr={id:$r,detector:t=>/^\s*sequenceDiagram/.test(t),loader:async()=>{const{diagram:t}=await i.e(770).then(i.bind(i,5935));return{id:$r,diagram:t}}},zr="class",jr={id:zr,detector:(t,e)=>{var i;return"dagre-wrapper"!==(null==(i=null==e?void 0:e.class)?void 0:i.defaultRenderer)&&/^\s*classDiagram/.test(t)},loader:async()=>{const{diagram:t}=await Promise.all([i.e(451),i.e(321),i.e(861)]).then(i.bind(i,2861));return{id:zr,diagram:t}}},Pr="classDiagram",Rr={id:Pr,detector:(t,e)=>{var i;return!(!/^\s*classDiagram/.test(t)||"dagre-wrapper"!==(null==(i=null==e?void 0:e.class)?void 0:i.defaultRenderer))||/^\s*classDiagram-v2/.test(t)},loader:async()=>{const{diagram:t}=await Promise.all([i.e(451),i.e(105),i.e(264),i.e(759),i.e(321),i.e(604)]).then(i.bind(i,5604));return{id:Pr,diagram:t}}},Wr="state",Ur={id:Wr,detector:(t,e)=>{var i;return"dagre-wrapper"!==(null==(i=null==e?void 0:e.state)?void 0:i.defaultRenderer)&&/^\s*stateDiagram/.test(t)},loader:async()=>{const{diagram:t}=await Promise.all([i.e(451),i.e(902),i.e(120)]).then(i.bind(i,120));return{id:Wr,diagram:t}}},Hr="stateDiagram",Yr={id:Hr,detector:(t,e)=>{var i;return!!/^\s*stateDiagram-v2/.test(t)||!(!/^\s*stateDiagram/.test(t)||"dagre-wrapper"!==(null==(i=null==e?void 0:e.state)?void 0:i.defaultRenderer))},loader:async()=>{const{diagram:t}=await Promise.all([i.e(451),i.e(105),i.e(264),i.e(759),i.e(902),i.e(980)]).then(i.bind(i,7980));return{id:Hr,diagram:t}}},Vr="journey",Gr={id:Vr,detector:t=>/^\s*journey/.test(t),loader:async()=>{const{diagram:t}=await i.e(246).then(i.bind(i,1246));return{id:Vr,diagram:t}}},Xr={setConf:function(){},draw:(t,e,i)=>{try{rt.debug("Renering svg for syntax error\n");const t=(0,a.Ys)("#"+e),r=t.append("g");r.append("path").attr("class","error-icon").attr("d","m411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z"),r.append("path").attr("class","error-icon").attr("d","m459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z"),r.append("path").attr("class","error-icon").attr("d","m340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z"),r.append("path").attr("class","error-icon").attr("d","m400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z"),r.append("path").attr("class","error-icon").attr("d","m496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z"),r.append("path").attr("class","error-icon").attr("d","m436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z"),r.append("text").attr("class","error-text").attr("x",1440).attr("y",250).attr("font-size","150px").style("text-anchor","middle").text("Syntax error in text"),r.append("text").attr("class","error-text").attr("x",1250).attr("y",400).attr("font-size","100px").style("text-anchor","middle").text("mermaid version "+i),t.attr("height",100),t.attr("width",500),t.attr("viewBox","768 0 912 512")}catch(t){rt.error("Error while rendering info diagram"),rt.error((r=t)instanceof Error?r.message:String(r))}var r}},Jr={db:{clear:()=>{}},styles:()=>"",renderer:Xr,parser:{parser:{yy:{}},parse:()=>{}},init:()=>{}},Qr="flowchart-elk",Kr={id:Qr,detector:(t,e)=>{var i;return!!(/^\s*flowchart-elk/.test(t)||/^\s*flowchart|graph/.test(t)&&"elk"===(null==(i=null==e?void 0:e.flowchart)?void 0:i.defaultRenderer))},loader:async()=>{const{diagram:t}=await Promise.all([i.e(105),i.e(264),i.e(796),i.e(522)]).then(i.bind(i,3522));return{id:Qr,diagram:t}}},tn="timeline",en={id:tn,detector:t=>/^\s*timeline/.test(t),loader:async()=>{const{diagram:t}=await i.e(684).then(i.bind(i,684));return{id:tn,diagram:t}}},rn="mindmap",nn={id:rn,detector:t=>/^\s*mindmap/.test(t),loader:async()=>{const{diagram:t}=await Promise.all([i.e(105),i.e(968)]).then(i.bind(i,9968));return{id:rn,diagram:t}}};let on=!1;const an=()=>{on||(on=!0,Pi("error",Jr,(t=>"error"===t.toLowerCase().trim())),Pi("---",{db:{clear:()=>{}},styles:{},renderer:{},parser:{parser:{yy:{}},parse:()=>{throw new Error("Diagrams beginning with --- are not valid. If you were trying to use a YAML front-matter, please ensure that you've correctly opened and closed the YAML front-matter with un-indented `---` blocks")}},init:()=>null},(t=>t.toLowerCase().trimStart().startsWith("---"))),Gi(xr,Rr,jr,Sr,Mr,Er,Or,Dr,Zr,Kr,wr,kr,nn,en,Fr,Yr,Ur,Gr,Nr))};class sn{constructor(t){var e,i;this.text=t,this.type="graph",this.text+="\n";const r=hi();try{this.type=Vi(t,r)}catch(t){this.type="error",this.detectError=t}const n=Ri(this.type);rt.debug("Type "+this.type),this.db=n.db,null==(i=(e=this.db).clear)||i.call(e),this.renderer=n.renderer,this.parser=n.parser;const o=this.parser.parse.bind(this.parser);this.parser.parse=t=>o((t=>t.trimStart().replace(/^\s*%%(?!{)[^\n]+\n?/gm,""))(function(t,e){var i,r;const n=t.match(Qe);if(n){const o=Je(n[1],{schema:Xe});return(null==o?void 0:o.title)&&(null==(i=e.setDiagramTitle)||i.call(e,o.title)),(null==o?void 0:o.displayMode)&&(null==(r=e.setDisplayMode)||r.call(e,o.displayMode)),t.slice(n[0].length)}return t}(t,this.db))),this.parser.parser.yy=this.db,n.init&&(n.init(r),rt.info("Initialized diagram "+this.type,r)),this.parse()}parse(){var t,e;if(this.detectError)throw this.detectError;null==(e=(t=this.db).clear)||e.call(t),this.parser.parse(this.text)}async render(t,e){await this.renderer.draw(this.text,t,e,this)}getParser(){return this.parser}getType(){return this.type}}const ln=async t=>{const e=Vi(t,hi());try{Ri(e)}catch(t){const i=Yi[e].loader;if(!i)throw new Wi(`Diagram ${e} not found.`);const{id:r,diagram:n}=await i();Pi(r,n)}return new sn(t)};let hn=[];const cn=t=>{hn.push(t)},un=["graph","flowchart","flowchart-v2","flowchart-elk","stateDiagram","stateDiagram-v2"],dn=["foreignobject"],fn=["dominant-baseline"],pn=function(t){return t.replace(/ﬂ°°/g,"&#").replace(/ﬂ°/g,"&").replace(/¶ß/g,";")},gn=(t,e,i=[])=>`\n.${t} ${e} { ${i.join(" !important; ")} !important; }`,mn=(t,e,i,r)=>{const n=((t,e,i={})=>{var r;let n="";if(void 0!==t.themeCSS&&(n+=`\n${t.themeCSS}`),void 0!==t.fontFamily&&(n+=`\n:root { --mermaid-font-family: ${t.fontFamily}}`),void 0!==t.altFontFamily&&(n+=`\n:root { --mermaid-alt-font-family: ${t.altFontFamily}}`),!(0,et.Z)(i)&&un.includes(e)){const e=t.htmlLabels||(null==(r=t.flowchart)?void 0:r.htmlLabels)?["> *","span"]:["rect","polygon","ellipse","circle","path"];for(const t in i){const r=i[t];(0,et.Z)(r.styles)||e.forEach((t=>{n+=gn(r.id,t,r.styles)})),(0,et.Z)(r.textStyles)||(n+=gn(r.id,"tspan",r.textStyles))}}return n})(t,e,i);return L(X(`${r}{${((t,e,i)=>{let r="";return t in _i&&_i[t]?r=_i[t](i):rt.warn(`No theme found for ${t}`),` & {\n    font-family: ${i.fontFamily};\n    font-size: ${i.fontSize};\n    fill: ${i.textColor}\n  }\n\n  /* Classes common for multiple diagrams */\n\n  & .error-icon {\n    fill: ${i.errorBkgColor};\n  }\n  & .error-text {\n    fill: ${i.errorTextColor};\n    stroke: ${i.errorTextColor};\n  }\n\n  & .edge-thickness-normal {\n    stroke-width: 2px;\n  }\n  & .edge-thickness-thick {\n    stroke-width: 3.5px\n  }\n  & .edge-pattern-solid {\n    stroke-dasharray: 0;\n  }\n\n  & .edge-pattern-dashed{\n    stroke-dasharray: 3;\n  }\n  .edge-pattern-dotted {\n    stroke-dasharray: 2;\n  }\n\n  & .marker {\n    fill: ${i.lineColor};\n    stroke: ${i.lineColor};\n  }\n  & .marker.cross {\n    stroke: ${i.lineColor};\n  }\n\n  & svg {\n    font-family: ${i.fontFamily};\n    font-size: ${i.fontSize};\n  }\n\n  ${r}\n\n  ${e}\n`})(e,n,t.themeVariables)}}`),M)},yn=(t,e,i,r,n)=>{const o=t.append("div");o.attr("id",i),r&&o.attr("style",r);const a=o.append("svg").attr("id",e).attr("width","100%").attr("xmlns","http://www.w3.org/2000/svg");return n&&a.attr("xmlns:xlink",n),a.append("g"),t};function _n(t,e){return t.append("iframe").attr("id",e).attr("style","width: 100%; height: 100%;").attr("sandbox","")}const bn=Object.freeze({render:async function(t,e,i){var r,n,o,l;an(),di();const h=br.detectInit(e);h&&(gr(h),ui(h));const c=hi();rt.debug(c),e.length>((null==c?void 0:c.maxTextSize)??5e4)&&(e="graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa"),e=(e=e.replace(/\r\n?/g,"\n")).replace(/<(\w+)([^>]*)>/g,((t,e,i)=>"<"+e+i.replace(/="([^"]*)"/g,"='$1'")+">"));const u="#"+t,d="i"+t,f="#"+d,p="d"+t,g="#"+p;let m=(0,a.Ys)("body");const y="sandbox"===c.securityLevel,_="loose"===c.securityLevel,b=c.fontFamily;if(void 0!==i){if(i&&(i.innerHTML=""),y){const t=_n((0,a.Ys)(i),d);m=(0,a.Ys)(t.nodes()[0].contentDocument.body),m.node().style.margin=0}else m=(0,a.Ys)(i);yn(m,t,p,`font-family: ${b}`,"http://www.w3.org/1999/xlink")}else{if(((t,e,i,r)=>{var n,o,a;null==(n=t.getElementById(e))||n.remove(),null==(o=t.getElementById(i))||o.remove(),null==(a=t.getElementById(r))||a.remove()})(document,t,p,d),y){const t=_n((0,a.Ys)("body"),d);m=(0,a.Ys)(t.nodes()[0].contentDocument.body),m.node().style.margin=0}else m=(0,a.Ys)("body");yn(m,t,p)}let C,x;e=function(t){let e=t;return e=e.replace(/style.*:\S*#.*;/g,(function(t){return t.substring(0,t.length-1)})),e=e.replace(/classDef.*:\S*#.*;/g,(function(t){return t.substring(0,t.length-1)})),e=e.replace(/#\w+;/g,(function(t){const e=t.substring(1,t.length-1);return/^\+?\d+$/.test(e)?"ﬂ°°"+e+"¶ß":"ﬂ°"+e+"¶ß"})),e}(e);try{C=await ln(e)}catch(t){C=new sn("error"),x=t}const v=m.select(g).node(),k=C.type,T=v.firstChild,w=T.firstChild,S=un.includes(k)?C.renderer.getClasses(e,C):{},B=mn(c,k,S,u),F=document.createElement("style");F.innerHTML=B,T.insertBefore(F,w);try{await C.renderer.draw(e,t,Cr,C)}catch(i){throw Xr.draw(e,t,Cr),i}!function(t,e,i,r){(function(t,e){t.attr("role","graphics-document document"),(0,et.Z)(e)||t.attr("aria-roledescription",e)})(e,t),function(t,e,i,r){if(void 0!==t.insert&&(e||i)){if(i){const e="chart-desc-"+r;t.attr("aria-describedby",e),t.insert("desc",":first-child").attr("id",e).text(i)}if(e){const i="chart-title-"+r;t.attr("aria-labelledby",i),t.insert("title",":first-child").attr("id",i).text(e)}}}(e,i,r,e.attr("id"))}(k,m.select(`${g} svg`),null==(n=(r=C.db).getAccTitle)?void 0:n.call(r),null==(l=(o=C.db).getAccDescription)?void 0:l.call(o)),m.select(`[id="${t}"]`).selectAll("foreignobject > *").attr("xmlns","http://www.w3.org/1999/xhtml");let L=m.select(g).node().innerHTML;if(rt.debug("config.arrowMarkerAbsolute",c.arrowMarkerAbsolute),L=((t="",e,i)=>{let r=t;return i||e||(r=r.replace(/marker-end="url\([\d+./:=?A-Za-z-]*?#/g,'marker-end="url(#')),r=pn(r),r=r.replace(/<br>/g,"<br/>"),r})(L,y,dt(c.arrowMarkerAbsolute)),y?L=((t="",e)=>`<iframe style="width:100%;height:${e?e.viewBox.baseVal.height+"px":"100%"};border:0;margin:0;" src="data:text/html;base64,${btoa('<body style="margin:0">'+t+"</body>")}" sandbox="allow-top-navigation-by-user-activation allow-popups">\n  The "iframe" tag is not supported by your browser.\n</iframe>`)(L,m.select(g+" svg").node()):_||(L=s.sanitize(L,{ADD_TAGS:dn,ADD_ATTR:fn})),hn.forEach((t=>{t()})),hn=[],x)throw x;const M=y?f:g,A=(0,a.Ys)(M).node();return A&&"remove"in A&&A.remove(),{svg:L,bindFunctions:C.db.bindFunctions}},parse:async function(t,e){an();try{(await ln(t)).parse()}catch(t){if(null==e?void 0:e.suppressErrors)return!1;throw t}return!0},parseDirective:Oi,getDiagramFromText:ln,initialize:function(t={}){var e;(null==t?void 0:t.fontFamily)&&!(null==(e=t.themeVariables)?void 0:e.fontFamily)&&(t.themeVariables||(t.themeVariables={}),t.themeVariables.fontFamily=t.fontFamily),ii=ti({},t),(null==t?void 0:t.theme)&&t.theme in Ct?t.themeVariables=Ct[t.theme].getThemeVariables(t.themeVariables):t&&(t.themeVariables=Ct.default.getThemeVariables(t.themeVariables));const i="object"==typeof t?(r=t,ri=ti({},ei),ri=ti(ri,r),r.theme&&Ct[r.theme]&&(ri.themeVariables=Ct[r.theme].getThemeVariables(r.themeVariables)),ai(ri,ni),ri):si();var r;nt(i.logLevel),an()},getConfig:hi,setConfig:li,getSiteConfig:si,updateSiteConfig:t=>(ri=ti(ri,t),ai(ri,ni),ri),reset:()=>{di()},globalReset:()=>{di(ei)},defaultConfig:ei});nt(hi().logLevel),di(hi());const Cn=(t,e,i)=>{rt.warn(t),yr(t)?(i&&i(t.str,t.hash),e.push({...t,message:t.str,error:t})):(i&&i(t),t instanceof Error&&e.push({str:t.message,message:t.message,hash:t.name,error:t}))},xn=async function(t={querySelector:".mermaid"}){try{await vn(t)}catch(e){if(yr(e)&&rt.error(e.str),Ln.parseError&&Ln.parseError(e),!t.suppressErrors)throw rt.error("Use the suppressErrors option to suppress these errors"),e}},vn=async function({postRenderCallback:t,querySelector:e,nodes:i}={querySelector:".mermaid"}){const n=bn.getConfig();let o;if(rt.debug((t?"":"No ")+"Callback function found"),i)o=i;else{if(!e)throw new Error("Nodes and querySelector are both undefined");o=document.querySelectorAll(e)}rt.debug(`Found ${o.length} diagrams`),void 0!==(null==n?void 0:n.startOnLoad)&&(rt.debug("Start On Load: "+(null==n?void 0:n.startOnLoad)),bn.updateSiteConfig({startOnLoad:null==n?void 0:n.startOnLoad}));const a=new br.initIdGenerator(n.deterministicIds,n.deterministicIDSeed);let s;const l=[];for(const e of Array.from(o)){if(rt.info("Rendering diagram: "+e.id),e.getAttribute("data-processed"))continue;e.setAttribute("data-processed","true");const i=`mermaid-${a.next()}`;s=e.innerHTML,s=(0,r.Z)(br.entityDecode(s)).trim().replace(/<br\s*\/?>/gi,"<br/>");const n=br.detectInit(s);n&&rt.debug("Detected early reinit: ",n);try{const{svg:r,bindFunctions:n}=await Fn(i,s,e);e.innerHTML=r,t&&await t(i),n&&n(e)}catch(t){Cn(t,l,Ln.parseError)}}if(l.length>0)throw l[0]},kn=function(t){bn.initialize(t)},Tn=function(){if(Ln.startOnLoad){const{startOnLoad:t}=bn.getConfig();t&&Ln.run().catch((t=>rt.error("Mermaid failed to initialize",t)))}};"undefined"!=typeof document&&window.addEventListener("load",Tn,!1);const wn=[];let Sn=!1;const Bn=async()=>{if(!Sn){for(Sn=!0;wn.length>0;){const t=wn.shift();if(t)try{await t()}catch(t){rt.error("Error executing queue",t)}}Sn=!1}},Fn=(t,e,i)=>new Promise(((r,n)=>{wn.push((()=>new Promise(((o,a)=>{bn.render(t,e,i).then((t=>{o(t),r(t)}),(t=>{var e;rt.error("Error parsing",t),null==(e=Ln.parseError)||e.call(Ln,t),a(t),n(t)}))})))),Bn().catch(n)})),Ln={startOnLoad:!0,mermaidAPI:bn,parse:async(t,e)=>new Promise(((i,r)=>{wn.push((()=>new Promise(((n,o)=>{bn.parse(t,e).then((t=>{n(t),i(t)}),(t=>{var e;rt.error("Error parsing",t),null==(e=Ln.parseError)||e.call(Ln,t),o(t),r(t)}))})))),Bn().catch(r)})),render:Fn,init:async function(t,e,i){rt.warn("mermaid.init is deprecated. Please use run instead."),t&&kn(t);const r={postRenderCallback:i,querySelector:".mermaid"};"string"==typeof e?r.querySelector=e:e&&(e instanceof HTMLElement?r.nodes=[e]:r.nodes=e),await xn(r)},run:xn,registerExternalDiagrams:async(t,{lazyLoad:e=!0}={})=>{Gi(...t),!1===e&&await(async()=>{rt.debug("Loading registered diagrams");const t=(await Promise.allSettled(Object.entries(Yi).map((async([t,{detector:e,loader:i}])=>{if(i)try{Ri(t)}catch(r){try{const{diagram:t,id:r}=await i();Pi(r,t,e)}catch(e){throw rt.error(`Failed to load external diagram with key ${t}. Removing from detectors.`),delete Yi[t],e}}})))).filter((t=>"rejected"===t.status));if(t.length>0){rt.error(`Failed to load ${t.length} external diagrams`);for(const e of t)rt.error(e);throw new Error(`Failed to load ${t.length} external diagrams`)}})()},initialize:kn,parseError:void 0,contentLoaded:Tn,setParseErrorHandler:function(t){Ln.parseError=t},detectType:Vi}},6637:function(t,e,i){"use strict";i.r(e),i.d(e,{default:function(){return r.K}});var r=i(5103);i(7484),i(7967),i(5740),i(7856)}}]);