<!DOCTYPE html>
<html
  lang="{{ .Site.Language.Lang }}"
  class="color-toggle-hidden"
  {{ if default false .Site.Params.geekdocDarkModeCode }}code-theme="dark"{{ end }}
>
  <head>
    {{ partial "head/meta" . }}
    <title>
      {{- if eq .Kind "home" -}}
        {{ .Site.Title }}
      {{- else -}}
        {{ printf "%s | %s" (partial "utils/title" .) .Site.Title }}
      {{- end -}}
    </title>

    {{ partial "head/favicons" . }}
    {{ partial "head/rel-me" . }}
    {{ partial "head/microformats" . }}
    {{ partial "head/others" . }}
    {{ partial "head/custom" . }}
  </head>

  <body itemscope itemtype="https://schema.org/WebPage">
    {{ partial "svg-icon-symbols" . }}


    <div
      class="wrapper {{ if default false .Site.Params.geekdocDarkModeDim }}dark-mode-dim{{ end }}"
    >
      <input type="checkbox" class="hidden" id="menu-control" />
      <input type="checkbox" class="hidden" id="menu-header-control" />
      {{ $navEnabled := default true .Page.Params.geekdocNav }}
      {{ partial "site-header" (dict "Root" . "MenuEnabled" $navEnabled) }}


      <main class="container flex flex-even">
        {{ if $navEnabled }}
          <aside class="gdoc-nav">
            {{ partial "menu" . }}
          </aside>
        {{ end }}


        <div class="gdoc-page">
          {{ template "main" . }}


          <div class="gdoc-page__footer flex flex-wrap justify-between">
            {{ partial "menu-nextprev" . }}
          </div>
        </div>
      </main>

      {{ partial "site-footer" . }}
    </div>

    {{ partial "foot" . }}
  </body>
</html>
