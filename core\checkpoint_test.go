/*
Copyright 2021 Mirantis

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package core

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPodSandboxCheckpoint(t *testing.T) {
	data := &CheckpointData{HostNetwork: true}
	checkpoint := NewPodSandboxCheckpoint("ns1", "sandbox1", data)
	version, name, namespace, _, hostNetwork := checkpoint.GetData()
	assert.Equal(t, schemaVersion, version)
	assert.Equal(t, "ns1", namespace)
	assert.Equal(t, "sandbox1", name)
	assert.Equal(t, true, hostNetwork)
}
