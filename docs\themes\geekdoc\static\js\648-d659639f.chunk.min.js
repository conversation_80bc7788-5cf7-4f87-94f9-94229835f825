"use strict";(self.webpackChunkgeekdoc=self.webpackChunkgeekdoc||[]).push([[648],{9648:function(t,e,n){n.d(e,{diagram:function(){return Q}});var i=n(5103),a=n(5740),s=n(8770),r=n(7967),l=(n(7484),n(7856),function(){var t=function(t,e,n,i){for(n=n||{},i=t.length;i--;n[t[i]]=e);return n},e=[1,6],n=[1,7],i=[1,8],a=[1,9],s=[1,16],r=[1,11],l=[1,12],o=[1,13],h=[1,14],d=[1,15],u=[1,27],p=[1,33],y=[1,34],f=[1,35],g=[1,36],b=[1,37],_=[1,72],x=[1,73],m=[1,74],E=[1,75],A=[1,76],S=[1,77],v=[1,78],C=[1,38],k=[1,39],O=[1,40],T=[1,41],w=[1,42],D=[1,43],R=[1,44],N=[1,45],P=[1,46],M=[1,47],j=[1,48],B=[1,49],Y=[1,50],L=[1,51],I=[1,52],U=[1,53],F=[1,54],X=[1,55],z=[1,56],Q=[1,57],W=[1,59],$=[1,60],q=[1,61],V=[1,62],G=[1,63],H=[1,64],K=[1,65],J=[1,66],Z=[1,67],tt=[1,68],et=[1,69],nt=[24,52],it=[24,44,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84],at=[15,24,44,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84],st=[1,94],rt=[1,95],lt=[1,96],ot=[1,97],ct=[15,24,52],ht=[7,8,9,10,18,22,25,26,27,28],dt=[15,24,43,52],ut=[15,24,43,52,86,87,89,90],pt=[15,43],yt=[44,46,47,48,49,50,51,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84],ft={trace:function(){},yy:{},symbols_:{error:2,start:3,mermaidDoc:4,direction:5,directive:6,direction_tb:7,direction_bt:8,direction_rl:9,direction_lr:10,graphConfig:11,openDirective:12,typeDirective:13,closeDirective:14,NEWLINE:15,":":16,argDirective:17,open_directive:18,type_directive:19,arg_directive:20,close_directive:21,C4_CONTEXT:22,statements:23,EOF:24,C4_CONTAINER:25,C4_COMPONENT:26,C4_DYNAMIC:27,C4_DEPLOYMENT:28,otherStatements:29,diagramStatements:30,otherStatement:31,title:32,accDescription:33,acc_title:34,acc_title_value:35,acc_descr:36,acc_descr_value:37,acc_descr_multiline_value:38,boundaryStatement:39,boundaryStartStatement:40,boundaryStopStatement:41,boundaryStart:42,LBRACE:43,ENTERPRISE_BOUNDARY:44,attributes:45,SYSTEM_BOUNDARY:46,BOUNDARY:47,CONTAINER_BOUNDARY:48,NODE:49,NODE_L:50,NODE_R:51,RBRACE:52,diagramStatement:53,PERSON:54,PERSON_EXT:55,SYSTEM:56,SYSTEM_DB:57,SYSTEM_QUEUE:58,SYSTEM_EXT:59,SYSTEM_EXT_DB:60,SYSTEM_EXT_QUEUE:61,CONTAINER:62,CONTAINER_DB:63,CONTAINER_QUEUE:64,CONTAINER_EXT:65,CONTAINER_EXT_DB:66,CONTAINER_EXT_QUEUE:67,COMPONENT:68,COMPONENT_DB:69,COMPONENT_QUEUE:70,COMPONENT_EXT:71,COMPONENT_EXT_DB:72,COMPONENT_EXT_QUEUE:73,REL:74,BIREL:75,REL_U:76,REL_D:77,REL_L:78,REL_R:79,REL_B:80,REL_INDEX:81,UPDATE_EL_STYLE:82,UPDATE_REL_STYLE:83,UPDATE_LAYOUT_CONFIG:84,attribute:85,STR:86,STR_KEY:87,STR_VALUE:88,ATTRIBUTE:89,ATTRIBUTE_EMPTY:90,$accept:0,$end:1},terminals_:{2:"error",7:"direction_tb",8:"direction_bt",9:"direction_rl",10:"direction_lr",15:"NEWLINE",16:":",18:"open_directive",19:"type_directive",20:"arg_directive",21:"close_directive",22:"C4_CONTEXT",24:"EOF",25:"C4_CONTAINER",26:"C4_COMPONENT",27:"C4_DYNAMIC",28:"C4_DEPLOYMENT",32:"title",33:"accDescription",34:"acc_title",35:"acc_title_value",36:"acc_descr",37:"acc_descr_value",38:"acc_descr_multiline_value",43:"LBRACE",44:"ENTERPRISE_BOUNDARY",46:"SYSTEM_BOUNDARY",47:"BOUNDARY",48:"CONTAINER_BOUNDARY",49:"NODE",50:"NODE_L",51:"NODE_R",52:"RBRACE",54:"PERSON",55:"PERSON_EXT",56:"SYSTEM",57:"SYSTEM_DB",58:"SYSTEM_QUEUE",59:"SYSTEM_EXT",60:"SYSTEM_EXT_DB",61:"SYSTEM_EXT_QUEUE",62:"CONTAINER",63:"CONTAINER_DB",64:"CONTAINER_QUEUE",65:"CONTAINER_EXT",66:"CONTAINER_EXT_DB",67:"CONTAINER_EXT_QUEUE",68:"COMPONENT",69:"COMPONENT_DB",70:"COMPONENT_QUEUE",71:"COMPONENT_EXT",72:"COMPONENT_EXT_DB",73:"COMPONENT_EXT_QUEUE",74:"REL",75:"BIREL",76:"REL_U",77:"REL_D",78:"REL_L",79:"REL_R",80:"REL_B",81:"REL_INDEX",82:"UPDATE_EL_STYLE",83:"UPDATE_REL_STYLE",84:"UPDATE_LAYOUT_CONFIG",86:"STR",87:"STR_KEY",88:"STR_VALUE",89:"ATTRIBUTE",90:"ATTRIBUTE_EMPTY"},productions_:[0,[3,1],[3,1],[3,2],[5,1],[5,1],[5,1],[5,1],[4,1],[6,4],[6,6],[12,1],[13,1],[17,1],[14,1],[11,4],[11,4],[11,4],[11,4],[11,4],[23,1],[23,1],[23,2],[29,1],[29,2],[29,3],[31,1],[31,1],[31,2],[31,2],[31,1],[39,3],[40,3],[40,3],[40,4],[42,2],[42,2],[42,2],[42,2],[42,2],[42,2],[42,2],[41,1],[30,1],[30,2],[30,3],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,1],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[53,2],[45,1],[45,2],[85,1],[85,2],[85,1],[85,1]],performAction:function(t,e,n,i,a,s,r){var l=s.length-1;switch(a){case 4:i.setDirection("TB");break;case 5:i.setDirection("BT");break;case 6:i.setDirection("RL");break;case 7:i.setDirection("LR");break;case 11:i.parseDirective("%%{","open_directive");break;case 12:break;case 13:s[l]=s[l].trim().replace(/'/g,'"'),i.parseDirective(s[l],"arg_directive");break;case 14:i.parseDirective("}%%","close_directive","c4Context");break;case 15:case 16:case 17:case 18:case 19:i.setC4Type(s[l-3]);break;case 26:i.setTitle(s[l].substring(6)),this.$=s[l].substring(6);break;case 27:i.setAccDescription(s[l].substring(15)),this.$=s[l].substring(15);break;case 28:this.$=s[l].trim(),i.setTitle(this.$);break;case 29:case 30:this.$=s[l].trim(),i.setAccDescription(this.$);break;case 35:case 36:s[l].splice(2,0,"ENTERPRISE"),i.addPersonOrSystemBoundary(...s[l]),this.$=s[l];break;case 37:i.addPersonOrSystemBoundary(...s[l]),this.$=s[l];break;case 38:s[l].splice(2,0,"CONTAINER"),i.addContainerBoundary(...s[l]),this.$=s[l];break;case 39:i.addDeploymentNode("node",...s[l]),this.$=s[l];break;case 40:i.addDeploymentNode("nodeL",...s[l]),this.$=s[l];break;case 41:i.addDeploymentNode("nodeR",...s[l]),this.$=s[l];break;case 42:i.popBoundaryParseStack();break;case 46:i.addPersonOrSystem("person",...s[l]),this.$=s[l];break;case 47:i.addPersonOrSystem("external_person",...s[l]),this.$=s[l];break;case 48:i.addPersonOrSystem("system",...s[l]),this.$=s[l];break;case 49:i.addPersonOrSystem("system_db",...s[l]),this.$=s[l];break;case 50:i.addPersonOrSystem("system_queue",...s[l]),this.$=s[l];break;case 51:i.addPersonOrSystem("external_system",...s[l]),this.$=s[l];break;case 52:i.addPersonOrSystem("external_system_db",...s[l]),this.$=s[l];break;case 53:i.addPersonOrSystem("external_system_queue",...s[l]),this.$=s[l];break;case 54:i.addContainer("container",...s[l]),this.$=s[l];break;case 55:i.addContainer("container_db",...s[l]),this.$=s[l];break;case 56:i.addContainer("container_queue",...s[l]),this.$=s[l];break;case 57:i.addContainer("external_container",...s[l]),this.$=s[l];break;case 58:i.addContainer("external_container_db",...s[l]),this.$=s[l];break;case 59:i.addContainer("external_container_queue",...s[l]),this.$=s[l];break;case 60:i.addComponent("component",...s[l]),this.$=s[l];break;case 61:i.addComponent("component_db",...s[l]),this.$=s[l];break;case 62:i.addComponent("component_queue",...s[l]),this.$=s[l];break;case 63:i.addComponent("external_component",...s[l]),this.$=s[l];break;case 64:i.addComponent("external_component_db",...s[l]),this.$=s[l];break;case 65:i.addComponent("external_component_queue",...s[l]),this.$=s[l];break;case 67:i.addRel("rel",...s[l]),this.$=s[l];break;case 68:i.addRel("birel",...s[l]),this.$=s[l];break;case 69:i.addRel("rel_u",...s[l]),this.$=s[l];break;case 70:i.addRel("rel_d",...s[l]),this.$=s[l];break;case 71:i.addRel("rel_l",...s[l]),this.$=s[l];break;case 72:i.addRel("rel_r",...s[l]),this.$=s[l];break;case 73:i.addRel("rel_b",...s[l]),this.$=s[l];break;case 74:s[l].splice(0,1),i.addRel("rel",...s[l]),this.$=s[l];break;case 75:i.updateElStyle("update_el_style",...s[l]),this.$=s[l];break;case 76:i.updateRelStyle("update_rel_style",...s[l]),this.$=s[l];break;case 77:i.updateLayoutConfig("update_layout_config",...s[l]),this.$=s[l];break;case 78:this.$=[s[l]];break;case 79:s[l].unshift(s[l-1]),this.$=s[l];break;case 80:case 82:this.$=s[l].trim();break;case 81:let t={};t[s[l-1].trim()]=s[l].trim(),this.$=t;break;case 83:this.$=""}},table:[{3:1,4:2,5:3,6:4,7:e,8:n,9:i,10:a,11:5,12:10,18:s,22:r,25:l,26:o,27:h,28:d},{1:[3]},{1:[2,1]},{1:[2,2]},{3:17,4:2,5:3,6:4,7:e,8:n,9:i,10:a,11:5,12:10,18:s,22:r,25:l,26:o,27:h,28:d},{1:[2,8]},{1:[2,4]},{1:[2,5]},{1:[2,6]},{1:[2,7]},{13:18,19:[1,19]},{15:[1,20]},{15:[1,21]},{15:[1,22]},{15:[1,23]},{15:[1,24]},{19:[2,11]},{1:[2,3]},{14:25,16:[1,26],21:u},t([16,21],[2,12]),{23:28,29:29,30:30,31:31,32:p,33:y,34:f,36:g,38:b,39:58,40:70,42:71,44:_,46:x,47:m,48:E,49:A,50:S,51:v,53:32,54:C,55:k,56:O,57:T,58:w,59:D,60:R,61:N,62:P,63:M,64:j,65:B,66:Y,67:L,68:I,69:U,70:F,71:X,72:z,73:Q,74:W,75:$,76:q,77:V,78:G,79:H,80:K,81:J,82:Z,83:tt,84:et},{23:79,29:29,30:30,31:31,32:p,33:y,34:f,36:g,38:b,39:58,40:70,42:71,44:_,46:x,47:m,48:E,49:A,50:S,51:v,53:32,54:C,55:k,56:O,57:T,58:w,59:D,60:R,61:N,62:P,63:M,64:j,65:B,66:Y,67:L,68:I,69:U,70:F,71:X,72:z,73:Q,74:W,75:$,76:q,77:V,78:G,79:H,80:K,81:J,82:Z,83:tt,84:et},{23:80,29:29,30:30,31:31,32:p,33:y,34:f,36:g,38:b,39:58,40:70,42:71,44:_,46:x,47:m,48:E,49:A,50:S,51:v,53:32,54:C,55:k,56:O,57:T,58:w,59:D,60:R,61:N,62:P,63:M,64:j,65:B,66:Y,67:L,68:I,69:U,70:F,71:X,72:z,73:Q,74:W,75:$,76:q,77:V,78:G,79:H,80:K,81:J,82:Z,83:tt,84:et},{23:81,29:29,30:30,31:31,32:p,33:y,34:f,36:g,38:b,39:58,40:70,42:71,44:_,46:x,47:m,48:E,49:A,50:S,51:v,53:32,54:C,55:k,56:O,57:T,58:w,59:D,60:R,61:N,62:P,63:M,64:j,65:B,66:Y,67:L,68:I,69:U,70:F,71:X,72:z,73:Q,74:W,75:$,76:q,77:V,78:G,79:H,80:K,81:J,82:Z,83:tt,84:et},{23:82,29:29,30:30,31:31,32:p,33:y,34:f,36:g,38:b,39:58,40:70,42:71,44:_,46:x,47:m,48:E,49:A,50:S,51:v,53:32,54:C,55:k,56:O,57:T,58:w,59:D,60:R,61:N,62:P,63:M,64:j,65:B,66:Y,67:L,68:I,69:U,70:F,71:X,72:z,73:Q,74:W,75:$,76:q,77:V,78:G,79:H,80:K,81:J,82:Z,83:tt,84:et},{15:[1,83]},{17:84,20:[1,85]},{15:[2,14]},{24:[1,86]},t(nt,[2,20],{53:32,39:58,40:70,42:71,30:87,44:_,46:x,47:m,48:E,49:A,50:S,51:v,54:C,55:k,56:O,57:T,58:w,59:D,60:R,61:N,62:P,63:M,64:j,65:B,66:Y,67:L,68:I,69:U,70:F,71:X,72:z,73:Q,74:W,75:$,76:q,77:V,78:G,79:H,80:K,81:J,82:Z,83:tt,84:et}),t(nt,[2,21]),t(it,[2,23],{15:[1,88]}),t(nt,[2,43],{15:[1,89]}),t(at,[2,26]),t(at,[2,27]),{35:[1,90]},{37:[1,91]},t(at,[2,30]),{45:92,85:93,86:st,87:rt,89:lt,90:ot},{45:98,85:93,86:st,87:rt,89:lt,90:ot},{45:99,85:93,86:st,87:rt,89:lt,90:ot},{45:100,85:93,86:st,87:rt,89:lt,90:ot},{45:101,85:93,86:st,87:rt,89:lt,90:ot},{45:102,85:93,86:st,87:rt,89:lt,90:ot},{45:103,85:93,86:st,87:rt,89:lt,90:ot},{45:104,85:93,86:st,87:rt,89:lt,90:ot},{45:105,85:93,86:st,87:rt,89:lt,90:ot},{45:106,85:93,86:st,87:rt,89:lt,90:ot},{45:107,85:93,86:st,87:rt,89:lt,90:ot},{45:108,85:93,86:st,87:rt,89:lt,90:ot},{45:109,85:93,86:st,87:rt,89:lt,90:ot},{45:110,85:93,86:st,87:rt,89:lt,90:ot},{45:111,85:93,86:st,87:rt,89:lt,90:ot},{45:112,85:93,86:st,87:rt,89:lt,90:ot},{45:113,85:93,86:st,87:rt,89:lt,90:ot},{45:114,85:93,86:st,87:rt,89:lt,90:ot},{45:115,85:93,86:st,87:rt,89:lt,90:ot},{45:116,85:93,86:st,87:rt,89:lt,90:ot},t(ct,[2,66]),{45:117,85:93,86:st,87:rt,89:lt,90:ot},{45:118,85:93,86:st,87:rt,89:lt,90:ot},{45:119,85:93,86:st,87:rt,89:lt,90:ot},{45:120,85:93,86:st,87:rt,89:lt,90:ot},{45:121,85:93,86:st,87:rt,89:lt,90:ot},{45:122,85:93,86:st,87:rt,89:lt,90:ot},{45:123,85:93,86:st,87:rt,89:lt,90:ot},{45:124,85:93,86:st,87:rt,89:lt,90:ot},{45:125,85:93,86:st,87:rt,89:lt,90:ot},{45:126,85:93,86:st,87:rt,89:lt,90:ot},{45:127,85:93,86:st,87:rt,89:lt,90:ot},{30:128,39:58,40:70,42:71,44:_,46:x,47:m,48:E,49:A,50:S,51:v,53:32,54:C,55:k,56:O,57:T,58:w,59:D,60:R,61:N,62:P,63:M,64:j,65:B,66:Y,67:L,68:I,69:U,70:F,71:X,72:z,73:Q,74:W,75:$,76:q,77:V,78:G,79:H,80:K,81:J,82:Z,83:tt,84:et},{15:[1,130],43:[1,129]},{45:131,85:93,86:st,87:rt,89:lt,90:ot},{45:132,85:93,86:st,87:rt,89:lt,90:ot},{45:133,85:93,86:st,87:rt,89:lt,90:ot},{45:134,85:93,86:st,87:rt,89:lt,90:ot},{45:135,85:93,86:st,87:rt,89:lt,90:ot},{45:136,85:93,86:st,87:rt,89:lt,90:ot},{45:137,85:93,86:st,87:rt,89:lt,90:ot},{24:[1,138]},{24:[1,139]},{24:[1,140]},{24:[1,141]},t(ht,[2,9]),{14:142,21:u},{21:[2,13]},{1:[2,15]},t(nt,[2,22]),t(it,[2,24],{31:31,29:143,32:p,33:y,34:f,36:g,38:b}),t(nt,[2,44],{29:29,30:30,31:31,53:32,39:58,40:70,42:71,23:144,32:p,33:y,34:f,36:g,38:b,44:_,46:x,47:m,48:E,49:A,50:S,51:v,54:C,55:k,56:O,57:T,58:w,59:D,60:R,61:N,62:P,63:M,64:j,65:B,66:Y,67:L,68:I,69:U,70:F,71:X,72:z,73:Q,74:W,75:$,76:q,77:V,78:G,79:H,80:K,81:J,82:Z,83:tt,84:et}),t(at,[2,28]),t(at,[2,29]),t(ct,[2,46]),t(dt,[2,78],{85:93,45:145,86:st,87:rt,89:lt,90:ot}),t(ut,[2,80]),{88:[1,146]},t(ut,[2,82]),t(ut,[2,83]),t(ct,[2,47]),t(ct,[2,48]),t(ct,[2,49]),t(ct,[2,50]),t(ct,[2,51]),t(ct,[2,52]),t(ct,[2,53]),t(ct,[2,54]),t(ct,[2,55]),t(ct,[2,56]),t(ct,[2,57]),t(ct,[2,58]),t(ct,[2,59]),t(ct,[2,60]),t(ct,[2,61]),t(ct,[2,62]),t(ct,[2,63]),t(ct,[2,64]),t(ct,[2,65]),t(ct,[2,67]),t(ct,[2,68]),t(ct,[2,69]),t(ct,[2,70]),t(ct,[2,71]),t(ct,[2,72]),t(ct,[2,73]),t(ct,[2,74]),t(ct,[2,75]),t(ct,[2,76]),t(ct,[2,77]),{41:147,52:[1,148]},{15:[1,149]},{43:[1,150]},t(pt,[2,35]),t(pt,[2,36]),t(pt,[2,37]),t(pt,[2,38]),t(pt,[2,39]),t(pt,[2,40]),t(pt,[2,41]),{1:[2,16]},{1:[2,17]},{1:[2,18]},{1:[2,19]},{15:[1,151]},t(it,[2,25]),t(nt,[2,45]),t(dt,[2,79]),t(ut,[2,81]),t(ct,[2,31]),t(ct,[2,42]),t(yt,[2,32]),t(yt,[2,33],{15:[1,152]}),t(ht,[2,10]),t(yt,[2,34])],defaultActions:{2:[2,1],3:[2,2],5:[2,8],6:[2,4],7:[2,5],8:[2,6],9:[2,7],16:[2,11],17:[2,3],27:[2,14],85:[2,13],86:[2,15],138:[2,16],139:[2,17],140:[2,18],141:[2,19]},parseError:function(t,e){if(!e.recoverable){var n=new Error(t);throw n.hash=e,n}this.trace(t)},parse:function(t){var e=[0],n=[],i=[null],a=[],s=this.table,r="",l=0,o=0,c=a.slice.call(arguments,1),h=Object.create(this.lexer),d={yy:{}};for(var u in this.yy)Object.prototype.hasOwnProperty.call(this.yy,u)&&(d.yy[u]=this.yy[u]);h.setInput(t,d.yy),d.yy.lexer=h,d.yy.parser=this,void 0===h.yylloc&&(h.yylloc={});var p=h.yylloc;a.push(p);var y=h.options&&h.options.ranges;"function"==typeof d.yy.parseError?this.parseError=d.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;for(var f,g,b,_,x,m,E,A,S,v={};;){if(g=e[e.length-1],this.defaultActions[g]?b=this.defaultActions[g]:(null==f&&(S=void 0,"number"!=typeof(S=n.pop()||h.lex()||1)&&(S instanceof Array&&(S=(n=S).pop()),S=this.symbols_[S]||S),f=S),b=s[g]&&s[g][f]),void 0===b||!b.length||!b[0]){var C;for(x in A=[],s[g])this.terminals_[x]&&x>2&&A.push("'"+this.terminals_[x]+"'");C=h.showPosition?"Parse error on line "+(l+1)+":\n"+h.showPosition()+"\nExpecting "+A.join(", ")+", got '"+(this.terminals_[f]||f)+"'":"Parse error on line "+(l+1)+": Unexpected "+(1==f?"end of input":"'"+(this.terminals_[f]||f)+"'"),this.parseError(C,{text:h.match,token:this.terminals_[f]||f,line:h.yylineno,loc:p,expected:A})}if(b[0]instanceof Array&&b.length>1)throw new Error("Parse Error: multiple actions possible at state: "+g+", token: "+f);switch(b[0]){case 1:e.push(f),i.push(h.yytext),a.push(h.yylloc),e.push(b[1]),f=null,o=h.yyleng,r=h.yytext,l=h.yylineno,p=h.yylloc;break;case 2:if(m=this.productions_[b[1]][1],v.$=i[i.length-m],v._$={first_line:a[a.length-(m||1)].first_line,last_line:a[a.length-1].last_line,first_column:a[a.length-(m||1)].first_column,last_column:a[a.length-1].last_column},y&&(v._$.range=[a[a.length-(m||1)].range[0],a[a.length-1].range[1]]),void 0!==(_=this.performAction.apply(v,[r,o,l,d.yy,b[1],i,a].concat(c))))return _;m&&(e=e.slice(0,-1*m*2),i=i.slice(0,-1*m),a=a.slice(0,-1*m)),e.push(this.productions_[b[1]][0]),i.push(v.$),a.push(v._$),E=s[e[e.length-2]][e[e.length-1]],e.push(E);break;case 3:return!0}}return!0}},gt={EOF:1,parseError:function(t,e){if(!this.yy.parser)throw new Error(t);this.yy.parser.parseError(t,e)},setInput:function(t,e){return this.yy=e||this.yy||{},this._input=t,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t},unput:function(t){var e=t.length,n=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),n.length-1&&(this.yylineno-=n.length-1);var a=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:n?(n.length===i.length?this.yylloc.first_column:0)+i[i.length-n.length].length-n[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[a[0],a[0]+this.yyleng-e]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},less:function(t){this.unput(this.match.slice(t))},pastInput:function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var t=this.pastInput(),e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},test_match:function(t,e){var n,i,a;if(this.options.backtrack_lexer&&(a={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(a.yylloc.range=this.yylloc.range.slice(0))),(i=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=i.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],n=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),n)return n;if(this._backtrack){for(var s in a)this[s]=a[s];return!1}return!1},next:function(){if(this.done)return this.EOF;var t,e,n,i;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var a=this._currentRules(),s=0;s<a.length;s++)if((n=this._input.match(this.rules[a[s]]))&&(!e||n[0].length>e[0].length)){if(e=n,i=s,this.options.backtrack_lexer){if(!1!==(t=this.test_match(n,a[s])))return t;if(this._backtrack){e=!1;continue}return!1}if(!this.options.flex)break}return e?!1!==(t=this.test_match(e,a[i]))&&t:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){return this.next()||this.lex()},begin:function(t){this.conditionStack.push(t)},popState:function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"},pushState:function(t){this.begin(t)},stateStackSize:function(){return this.conditionStack.length},options:{},performAction:function(t,e,n,i){switch(n){case 0:return this.begin("open_directive"),18;case 1:return 7;case 2:return 8;case 3:return 9;case 4:return 10;case 5:return this.begin("type_directive"),19;case 6:return this.popState(),this.begin("arg_directive"),16;case 7:return this.popState(),this.popState(),21;case 8:return 20;case 9:return 32;case 10:return 33;case 11:return this.begin("acc_title"),34;case 12:return this.popState(),"acc_title_value";case 13:return this.begin("acc_descr"),36;case 14:return this.popState(),"acc_descr_value";case 15:this.begin("acc_descr_multiline");break;case 16:case 78:this.popState();break;case 17:return"acc_descr_multiline_value";case 18:case 21:case 75:break;case 19:c;break;case 20:return 15;case 22:return 22;case 23:return 25;case 24:return 26;case 25:return 27;case 26:return 28;case 27:return this.begin("person_ext"),55;case 28:return this.begin("person"),54;case 29:return this.begin("system_ext_queue"),61;case 30:return this.begin("system_ext_db"),60;case 31:return this.begin("system_ext"),59;case 32:return this.begin("system_queue"),58;case 33:return this.begin("system_db"),57;case 34:return this.begin("system"),56;case 35:return this.begin("boundary"),47;case 36:return this.begin("enterprise_boundary"),44;case 37:return this.begin("system_boundary"),46;case 38:return this.begin("container_ext_queue"),67;case 39:return this.begin("container_ext_db"),66;case 40:return this.begin("container_ext"),65;case 41:return this.begin("container_queue"),64;case 42:return this.begin("container_db"),63;case 43:return this.begin("container"),62;case 44:return this.begin("container_boundary"),48;case 45:return this.begin("component_ext_queue"),73;case 46:return this.begin("component_ext_db"),72;case 47:return this.begin("component_ext"),71;case 48:return this.begin("component_queue"),70;case 49:return this.begin("component_db"),69;case 50:return this.begin("component"),68;case 51:case 52:return this.begin("node"),49;case 53:return this.begin("node_l"),50;case 54:return this.begin("node_r"),51;case 55:return this.begin("rel"),74;case 56:return this.begin("birel"),75;case 57:case 58:return this.begin("rel_u"),76;case 59:case 60:return this.begin("rel_d"),77;case 61:case 62:return this.begin("rel_l"),78;case 63:case 64:return this.begin("rel_r"),79;case 65:return this.begin("rel_b"),80;case 66:return this.begin("rel_index"),81;case 67:return this.begin("update_el_style"),82;case 68:return this.begin("update_rel_style"),83;case 69:return this.begin("update_layout_config"),84;case 70:return"EOF_IN_STRUCT";case 71:return this.begin("attribute"),"ATTRIBUTE_EMPTY";case 72:this.begin("attribute");break;case 73:case 84:this.popState(),this.popState();break;case 74:case 76:return 90;case 77:this.begin("string");break;case 79:case 85:return"STR";case 80:this.begin("string_kv");break;case 81:return this.begin("string_kv_key"),"STR_KEY";case 82:this.popState(),this.begin("string_kv_value");break;case 83:return"STR_VALUE";case 86:return"LBRACE";case 87:return"RBRACE";case 88:return"SPACE";case 89:return"EOL";case 90:return 24}},rules:[/^(?:%%\{)/,/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:((?:(?!\}%%)[^:.])*))/,/^(?::)/,/^(?:\}%%)/,/^(?:((?:(?!\}%%).|\n)*))/,/^(?:title\s[^#\n;]+)/,/^(?:accDescription\s[^#\n;]+)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:C4Context\b)/,/^(?:C4Container\b)/,/^(?:C4Component\b)/,/^(?:C4Dynamic\b)/,/^(?:C4Deployment\b)/,/^(?:Person_Ext\b)/,/^(?:Person\b)/,/^(?:SystemQueue_Ext\b)/,/^(?:SystemDb_Ext\b)/,/^(?:System_Ext\b)/,/^(?:SystemQueue\b)/,/^(?:SystemDb\b)/,/^(?:System\b)/,/^(?:Boundary\b)/,/^(?:Enterprise_Boundary\b)/,/^(?:System_Boundary\b)/,/^(?:ContainerQueue_Ext\b)/,/^(?:ContainerDb_Ext\b)/,/^(?:Container_Ext\b)/,/^(?:ContainerQueue\b)/,/^(?:ContainerDb\b)/,/^(?:Container\b)/,/^(?:Container_Boundary\b)/,/^(?:ComponentQueue_Ext\b)/,/^(?:ComponentDb_Ext\b)/,/^(?:Component_Ext\b)/,/^(?:ComponentQueue\b)/,/^(?:ComponentDb\b)/,/^(?:Component\b)/,/^(?:Deployment_Node\b)/,/^(?:Node\b)/,/^(?:Node_L\b)/,/^(?:Node_R\b)/,/^(?:Rel\b)/,/^(?:BiRel\b)/,/^(?:Rel_Up\b)/,/^(?:Rel_U\b)/,/^(?:Rel_Down\b)/,/^(?:Rel_D\b)/,/^(?:Rel_Left\b)/,/^(?:Rel_L\b)/,/^(?:Rel_Right\b)/,/^(?:Rel_R\b)/,/^(?:Rel_Back\b)/,/^(?:RelIndex\b)/,/^(?:UpdateElementStyle\b)/,/^(?:UpdateRelStyle\b)/,/^(?:UpdateLayoutConfig\b)/,/^(?:$)/,/^(?:[(][ ]*[,])/,/^(?:[(])/,/^(?:[)])/,/^(?:,,)/,/^(?:,)/,/^(?:[ ]*["]["])/,/^(?:[ ]*["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:[ ]*[\$])/,/^(?:[^=]*)/,/^(?:[=][ ]*["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:[^,]+)/,/^(?:\{)/,/^(?:\})/,/^(?:[\s]+)/,/^(?:[\n\r]+)/,/^(?:$)/],conditions:{acc_descr_multiline:{rules:[16,17],inclusive:!1},acc_descr:{rules:[14],inclusive:!1},acc_title:{rules:[12],inclusive:!1},close_directive:{rules:[],inclusive:!1},arg_directive:{rules:[7,8],inclusive:!1},type_directive:{rules:[6,7],inclusive:!1},open_directive:{rules:[5],inclusive:!1},string_kv_value:{rules:[83,84],inclusive:!1},string_kv_key:{rules:[82],inclusive:!1},string_kv:{rules:[81],inclusive:!1},string:{rules:[78,79],inclusive:!1},attribute:{rules:[73,74,75,76,77,80,85],inclusive:!1},update_layout_config:{rules:[70,71,72,73],inclusive:!1},update_rel_style:{rules:[70,71,72,73],inclusive:!1},update_el_style:{rules:[70,71,72,73],inclusive:!1},rel_b:{rules:[70,71,72,73],inclusive:!1},rel_r:{rules:[70,71,72,73],inclusive:!1},rel_l:{rules:[70,71,72,73],inclusive:!1},rel_d:{rules:[70,71,72,73],inclusive:!1},rel_u:{rules:[70,71,72,73],inclusive:!1},rel_bi:{rules:[],inclusive:!1},rel:{rules:[70,71,72,73],inclusive:!1},node_r:{rules:[70,71,72,73],inclusive:!1},node_l:{rules:[70,71,72,73],inclusive:!1},node:{rules:[70,71,72,73],inclusive:!1},index:{rules:[],inclusive:!1},rel_index:{rules:[70,71,72,73],inclusive:!1},component_ext_queue:{rules:[],inclusive:!1},component_ext_db:{rules:[70,71,72,73],inclusive:!1},component_ext:{rules:[70,71,72,73],inclusive:!1},component_queue:{rules:[70,71,72,73],inclusive:!1},component_db:{rules:[70,71,72,73],inclusive:!1},component:{rules:[70,71,72,73],inclusive:!1},container_boundary:{rules:[70,71,72,73],inclusive:!1},container_ext_queue:{rules:[],inclusive:!1},container_ext_db:{rules:[70,71,72,73],inclusive:!1},container_ext:{rules:[70,71,72,73],inclusive:!1},container_queue:{rules:[70,71,72,73],inclusive:!1},container_db:{rules:[70,71,72,73],inclusive:!1},container:{rules:[70,71,72,73],inclusive:!1},birel:{rules:[70,71,72,73],inclusive:!1},system_boundary:{rules:[70,71,72,73],inclusive:!1},enterprise_boundary:{rules:[70,71,72,73],inclusive:!1},boundary:{rules:[70,71,72,73],inclusive:!1},system_ext_queue:{rules:[70,71,72,73],inclusive:!1},system_ext_db:{rules:[70,71,72,73],inclusive:!1},system_ext:{rules:[70,71,72,73],inclusive:!1},system_queue:{rules:[70,71,72,73],inclusive:!1},system_db:{rules:[70,71,72,73],inclusive:!1},system:{rules:[70,71,72,73],inclusive:!1},person_ext:{rules:[70,71,72,73],inclusive:!1},person:{rules:[70,71,72,73],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,9,10,11,13,15,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,86,87,88,89,90],inclusive:!0}}};function bt(){this.yy={}}return ft.lexer=gt,bt.prototype=ft,ft.Parser=bt,new bt}());l.parser=l;const o=l;let h=[],d=[""],u="global",p="",y=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}],f=[],g="",b=!1,_=4,x=2;var m;const E=function(t){return null==t?h:h.filter((e=>e.parentBoundary===t))},A=function(){return b},S={addPersonOrSystem:function(t,e,n,i,a,s,r){if(null===e||null===n)return;let l={};const o=h.find((t=>t.alias===e));if(o&&e===o.alias?l=o:(l.alias=e,h.push(l)),l.label=null==n?{text:""}:{text:n},null==i)l.descr={text:""};else if("object"==typeof i){let[t,e]=Object.entries(i)[0];l[t]={text:e}}else l.descr={text:i};if("object"==typeof a){let[t,e]=Object.entries(a)[0];l[t]=e}else l.sprite=a;if("object"==typeof s){let[t,e]=Object.entries(s)[0];l[t]=e}else l.tags=s;if("object"==typeof r){let[t,e]=Object.entries(r)[0];l[t]=e}else l.link=r;l.typeC4Shape={text:t},l.parentBoundary=u,l.wrap=A()},addPersonOrSystemBoundary:function(t,e,n,i,a){if(null===t||null===e)return;let s={};const r=y.find((e=>e.alias===t));if(r&&t===r.alias?s=r:(s.alias=t,y.push(s)),s.label=null==e?{text:""}:{text:e},null==n)s.type={text:"system"};else if("object"==typeof n){let[t,e]=Object.entries(n)[0];s[t]={text:e}}else s.type={text:n};if("object"==typeof i){let[t,e]=Object.entries(i)[0];s[t]=e}else s.tags=i;if("object"==typeof a){let[t,e]=Object.entries(a)[0];s[t]=e}else s.link=a;s.parentBoundary=u,s.wrap=A(),p=u,u=t,d.push(p)},addContainer:function(t,e,n,i,a,s,r,l){if(null===e||null===n)return;let o={};const c=h.find((t=>t.alias===e));if(c&&e===c.alias?o=c:(o.alias=e,h.push(o)),o.label=null==n?{text:""}:{text:n},null==i)o.techn={text:""};else if("object"==typeof i){let[t,e]=Object.entries(i)[0];o[t]={text:e}}else o.techn={text:i};if(null==a)o.descr={text:""};else if("object"==typeof a){let[t,e]=Object.entries(a)[0];o[t]={text:e}}else o.descr={text:a};if("object"==typeof s){let[t,e]=Object.entries(s)[0];o[t]=e}else o.sprite=s;if("object"==typeof r){let[t,e]=Object.entries(r)[0];o[t]=e}else o.tags=r;if("object"==typeof l){let[t,e]=Object.entries(l)[0];o[t]=e}else o.link=l;o.wrap=A(),o.typeC4Shape={text:t},o.parentBoundary=u},addContainerBoundary:function(t,e,n,i,a){if(null===t||null===e)return;let s={};const r=y.find((e=>e.alias===t));if(r&&t===r.alias?s=r:(s.alias=t,y.push(s)),s.label=null==e?{text:""}:{text:e},null==n)s.type={text:"container"};else if("object"==typeof n){let[t,e]=Object.entries(n)[0];s[t]={text:e}}else s.type={text:n};if("object"==typeof i){let[t,e]=Object.entries(i)[0];s[t]=e}else s.tags=i;if("object"==typeof a){let[t,e]=Object.entries(a)[0];s[t]=e}else s.link=a;s.parentBoundary=u,s.wrap=A(),p=u,u=t,d.push(p)},addComponent:function(t,e,n,i,a,s,r,l){if(null===e||null===n)return;let o={};const c=h.find((t=>t.alias===e));if(c&&e===c.alias?o=c:(o.alias=e,h.push(o)),o.label=null==n?{text:""}:{text:n},null==i)o.techn={text:""};else if("object"==typeof i){let[t,e]=Object.entries(i)[0];o[t]={text:e}}else o.techn={text:i};if(null==a)o.descr={text:""};else if("object"==typeof a){let[t,e]=Object.entries(a)[0];o[t]={text:e}}else o.descr={text:a};if("object"==typeof s){let[t,e]=Object.entries(s)[0];o[t]=e}else o.sprite=s;if("object"==typeof r){let[t,e]=Object.entries(r)[0];o[t]=e}else o.tags=r;if("object"==typeof l){let[t,e]=Object.entries(l)[0];o[t]=e}else o.link=l;o.wrap=A(),o.typeC4Shape={text:t},o.parentBoundary=u},addDeploymentNode:function(t,e,n,i,a,s,r,l){if(null===e||null===n)return;let o={};const c=y.find((t=>t.alias===e));if(c&&e===c.alias?o=c:(o.alias=e,y.push(o)),o.label=null==n?{text:""}:{text:n},null==i)o.type={text:"node"};else if("object"==typeof i){let[t,e]=Object.entries(i)[0];o[t]={text:e}}else o.type={text:i};if(null==a)o.descr={text:""};else if("object"==typeof a){let[t,e]=Object.entries(a)[0];o[t]={text:e}}else o.descr={text:a};if("object"==typeof r){let[t,e]=Object.entries(r)[0];o[t]=e}else o.tags=r;if("object"==typeof l){let[t,e]=Object.entries(l)[0];o[t]=e}else o.link=l;o.nodeType=t,o.parentBoundary=u,o.wrap=A(),p=u,u=e,d.push(p)},popBoundaryParseStack:function(){u=p,d.pop(),p=d.pop(),d.push(p)},addRel:function(t,e,n,i,a,s,r,l,o){if(null==t||null==e||null==n||null==i)return;let c={};const h=f.find((t=>t.from===e&&t.to===n));if(h?c=h:f.push(c),c.type=t,c.from=e,c.to=n,c.label={text:i},null==a)c.techn={text:""};else if("object"==typeof a){let[t,e]=Object.entries(a)[0];c[t]={text:e}}else c.techn={text:a};if(null==s)c.descr={text:""};else if("object"==typeof s){let[t,e]=Object.entries(s)[0];c[t]={text:e}}else c.descr={text:s};if("object"==typeof r){let[t,e]=Object.entries(r)[0];c[t]=e}else c.sprite=r;if("object"==typeof l){let[t,e]=Object.entries(l)[0];c[t]=e}else c.tags=l;if("object"==typeof o){let[t,e]=Object.entries(o)[0];c[t]=e}else c.link=o;c.wrap=A()},updateElStyle:function(t,e,n,i,a,s,r,l,o,c,d){let u=h.find((t=>t.alias===e));if(void 0!==u||(u=y.find((t=>t.alias===e)),void 0!==u)){if(null!=n)if("object"==typeof n){let[t,e]=Object.entries(n)[0];u[t]=e}else u.bgColor=n;if(null!=i)if("object"==typeof i){let[t,e]=Object.entries(i)[0];u[t]=e}else u.fontColor=i;if(null!=a)if("object"==typeof a){let[t,e]=Object.entries(a)[0];u[t]=e}else u.borderColor=a;if(null!=s)if("object"==typeof s){let[t,e]=Object.entries(s)[0];u[t]=e}else u.shadowing=s;if(null!=r)if("object"==typeof r){let[t,e]=Object.entries(r)[0];u[t]=e}else u.shape=r;if(null!=l)if("object"==typeof l){let[t,e]=Object.entries(l)[0];u[t]=e}else u.sprite=l;if(null!=o)if("object"==typeof o){let[t,e]=Object.entries(o)[0];u[t]=e}else u.techn=o;if(null!=c)if("object"==typeof c){let[t,e]=Object.entries(c)[0];u[t]=e}else u.legendText=c;if(null!=d)if("object"==typeof d){let[t,e]=Object.entries(d)[0];u[t]=e}else u.legendSprite=d}},updateRelStyle:function(t,e,n,i,a,s,r){const l=f.find((t=>t.from===e&&t.to===n));if(void 0!==l){if(null!=i)if("object"==typeof i){let[t,e]=Object.entries(i)[0];l[t]=e}else l.textColor=i;if(null!=a)if("object"==typeof a){let[t,e]=Object.entries(a)[0];l[t]=e}else l.lineColor=a;if(null!=s)if("object"==typeof s){let[t,e]=Object.entries(s)[0];l[t]=parseInt(e)}else l.offsetX=parseInt(s);if(null!=r)if("object"==typeof r){let[t,e]=Object.entries(r)[0];l[t]=parseInt(e)}else l.offsetY=parseInt(r)}},updateLayoutConfig:function(t,e,n){let i=_,a=x;if("object"==typeof e){const t=Object.values(e)[0];i=parseInt(t)}else i=parseInt(e);if("object"==typeof n){const t=Object.values(n)[0];a=parseInt(t)}else a=parseInt(n);i>=1&&(_=i),a>=1&&(x=a)},autoWrap:A,setWrap:function(t){b=t},getC4ShapeArray:E,getC4Shape:function(t){return h.find((e=>e.alias===t))},getC4ShapeKeys:function(t){return Object.keys(E(t))},getBoundarys:function(t){return null==t?y:y.filter((e=>e.parentBoundary===t))},getCurrentBoundaryParse:function(){return u},getParentBoundaryParse:function(){return p},getRels:function(){return f},getTitle:function(){return g},getC4Type:function(){return m},getC4ShapeInRow:function(){return _},getC4BoundaryInRow:function(){return x},setAccTitle:i.s,getAccTitle:i.g,getAccDescription:i.a,setAccDescription:i.b,parseDirective:function(t,e,n){i.m.parseDirective(this,t,e,n)},getConfig:()=>(0,i.c)().c4,clear:function(){h=[],y=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}],p="",u="global",d=[""],f=[],d=[""],g="",b=!1,_=4,x=2},LINETYPE:{SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25},ARROWTYPE:{FILLED:0,OPEN:1},PLACEMENT:{LEFTOF:0,RIGHTOF:1,OVER:2},setTitle:function(t){let e=(0,i.d)(t,(0,i.c)());g=e},setC4Type:function(t){let e=(0,i.d)(t,(0,i.c)());m=e}},v=function(t,e){return(0,s.d)(t,e)},C=function(){function t(t,e,n,i,s,r,l){a(e.append("text").attr("x",n+s/2).attr("y",i+r/2+5).style("text-anchor","middle").text(t),l)}function e(t,e,n,s,r,l,o,c){const{fontSize:h,fontFamily:d,fontWeight:u}=c,p=t.split(i.e.lineBreakRegex);for(let t=0;t<p.length;t++){const i=t*h-h*(p.length-1)/2,l=e.append("text").attr("x",n+r/2).attr("y",s).style("text-anchor","middle").attr("dominant-baseline","middle").style("font-size",h).style("font-weight",u).style("font-family",d);l.append("tspan").attr("dy",i).text(p[t]).attr("alignment-baseline","mathematical"),a(l,o)}}function n(t,n,i,s,r,l,o,c){const h=n.append("switch"),d=h.append("foreignObject").attr("x",i).attr("y",s).attr("width",r).attr("height",l).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");d.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t),e(t,h,i,s,r,0,o,c),a(d,o)}function a(t,e){for(const n in e)e.hasOwnProperty(n)&&t.attr(n,e[n])}return function(i){return"fo"===i.textPlacement?n:"old"===i.textPlacement?t:e}}(),k=function(t,e,n){var i;let a=e.bgColor?e.bgColor:n[e.typeC4Shape.text+"_bg_color"],l=e.borderColor?e.borderColor:n[e.typeC4Shape.text+"_border_color"],o=e.fontColor?e.fontColor:"#FFFFFF",c="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";switch(e.typeC4Shape.text){case"person":c="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";break;case"external_person":c="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII="}const h=t.append("g");h.attr("class","person-man");const d=(0,s.g)();switch(e.typeC4Shape.text){case"person":case"external_person":case"system":case"external_system":case"container":case"external_container":case"component":case"external_component":d.x=e.x,d.y=e.y,d.fill=a,d.width=e.width,d.height=e.height,d.stroke=l,d.rx=2.5,d.ry=2.5,d.attrs={"stroke-width":.5},v(h,d);break;case"system_db":case"external_system_db":case"container_db":case"external_container_db":case"component_db":case"external_component_db":h.append("path").attr("fill",a).attr("stroke-width","0.5").attr("stroke",l).attr("d","Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("half",e.width/2).replaceAll("height",e.height)),h.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",l).attr("d","Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("half",e.width/2));break;case"system_queue":case"external_system_queue":case"container_queue":case"external_container_queue":case"component_queue":case"external_component_queue":h.append("path").attr("fill",a).attr("stroke-width","0.5").attr("stroke",l).attr("d","Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("width",e.width).replaceAll("half",e.height/2)),h.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",l).attr("d","Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half".replaceAll("startx",e.x+e.width).replaceAll("starty",e.y).replaceAll("half",e.height/2))}let u=(p=n,y=e.typeC4Shape.text,{fontFamily:p[y+"FontFamily"],fontSize:p[y+"FontSize"],fontWeight:p[y+"FontWeight"]});var p,y;switch(h.append("text").attr("fill",o).attr("font-family",u.fontFamily).attr("font-size",u.fontSize-2).attr("font-style","italic").attr("lengthAdjust","spacing").attr("textLength",e.typeC4Shape.width).attr("x",e.x+e.width/2-e.typeC4Shape.width/2).attr("y",e.y+e.typeC4Shape.Y).text("<<"+e.typeC4Shape.text+">>"),e.typeC4Shape.text){case"person":case"external_person":!function(t,e,n,i,a,s){const l=t.append("image");l.attr("width",e),l.attr("height",n),l.attr("x",i),l.attr("y",a);let o=s.startsWith("data:image/png;base64")?s:(0,r.N)(s);l.attr("xlink:href",o)}(h,48,48,e.x+e.width/2-24,e.y+e.image.Y,c)}let f=n[e.typeC4Shape.text+"Font"]();return f.fontWeight="bold",f.fontSize=f.fontSize+2,f.fontColor=o,C(n)(e.label.text,h,e.x,e.y+e.label.Y,e.width,e.height,{fill:o},f),f=n[e.typeC4Shape.text+"Font"](),f.fontColor=o,e.techn&&""!==(null==(i=e.techn)?void 0:i.text)?C(n)(e.techn.text,h,e.x,e.y+e.techn.Y,e.width,e.height,{fill:o,"font-style":"italic"},f):e.type&&""!==e.type.text&&C(n)(e.type.text,h,e.x,e.y+e.type.Y,e.width,e.height,{fill:o,"font-style":"italic"},f),e.descr&&""!==e.descr.text&&(f=n.personFont(),f.fontColor=o,C(n)(e.descr.text,h,e.x,e.y+e.descr.Y,e.width,e.height,{fill:o},f)),e.height};let O=0,T=0,w=4,D=2;l.yy=S;let R={};class N{constructor(t){this.name="",this.data={},this.data.startx=void 0,this.data.stopx=void 0,this.data.starty=void 0,this.data.stopy=void 0,this.data.widthLimit=void 0,this.nextData={},this.nextData.startx=void 0,this.nextData.stopx=void 0,this.nextData.starty=void 0,this.nextData.stopy=void 0,this.nextData.cnt=0,P(t.db.getConfig())}setData(t,e,n,i){this.nextData.startx=this.data.startx=t,this.nextData.stopx=this.data.stopx=e,this.nextData.starty=this.data.starty=n,this.nextData.stopy=this.data.stopy=i}updateVal(t,e,n,i){void 0===t[e]?t[e]=n:t[e]=i(n,t[e])}insert(t){this.nextData.cnt=this.nextData.cnt+1;let e=this.nextData.startx===this.nextData.stopx?this.nextData.stopx+t.margin:this.nextData.stopx+2*t.margin,n=e+t.width,i=this.nextData.starty+2*t.margin,a=i+t.height;(e>=this.data.widthLimit||n>=this.data.widthLimit||this.nextData.cnt>w)&&(e=this.nextData.startx+t.margin+R.nextLinePaddingX,i=this.nextData.stopy+2*t.margin,this.nextData.stopx=n=e+t.width,this.nextData.starty=this.nextData.stopy,this.nextData.stopy=a=i+t.height,this.nextData.cnt=1),t.x=e,t.y=i,this.updateVal(this.data,"startx",e,Math.min),this.updateVal(this.data,"starty",i,Math.min),this.updateVal(this.data,"stopx",n,Math.max),this.updateVal(this.data,"stopy",a,Math.max),this.updateVal(this.nextData,"startx",e,Math.min),this.updateVal(this.nextData,"starty",i,Math.min),this.updateVal(this.nextData,"stopx",n,Math.max),this.updateVal(this.nextData,"stopy",a,Math.max)}init(t){this.name="",this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,widthLimit:void 0},this.nextData={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,cnt:0},P(t.db.getConfig())}bumpLastMargin(t){this.data.stopx+=t,this.data.stopy+=t}}const P=function(t){(0,i.f)(R,t),t.fontFamily&&(R.personFontFamily=R.systemFontFamily=R.messageFontFamily=t.fontFamily),t.fontSize&&(R.personFontSize=R.systemFontSize=R.messageFontSize=t.fontSize),t.fontWeight&&(R.personFontWeight=R.systemFontWeight=R.messageFontWeight=t.fontWeight)},M=(t,e)=>({fontFamily:t[e+"FontFamily"],fontSize:t[e+"FontSize"],fontWeight:t[e+"FontWeight"]}),j=t=>({fontFamily:t.boundaryFontFamily,fontSize:t.boundaryFontSize,fontWeight:t.boundaryFontWeight});function B(t,e,n,a,s){if(!e[t].width)if(n)e[t].text=(0,i.w)(e[t].text,s,a),e[t].textLines=e[t].text.split(i.e.lineBreakRegex).length,e[t].width=s,e[t].height=(0,i.j)(e[t].text,a);else{let n=e[t].text.split(i.e.lineBreakRegex);e[t].textLines=n.length;let s=0;e[t].height=0,e[t].width=0;for(const r of n)e[t].width=Math.max((0,i.h)(r,a),e[t].width),s=(0,i.j)(r,a),e[t].height=e[t].height+s}}const Y=function(t,e,n){e.x=n.data.startx,e.y=n.data.starty,e.width=n.data.stopx-n.data.startx,e.height=n.data.stopy-n.data.starty,e.label.y=R.c4ShapeMargin-35;let a=e.wrap&&R.wrap,s=j(R);s.fontSize=s.fontSize+2,s.fontWeight="bold",B("label",e,a,s,(0,i.h)(e.label.text,s)),function(t,e,n){const i=t.append("g");let a=e.bgColor?e.bgColor:"none",s=e.borderColor?e.borderColor:"#444444",r=e.fontColor?e.fontColor:"black",l={"stroke-width":1,"stroke-dasharray":"7.0,7.0"};e.nodeType&&(l={"stroke-width":1});let o={x:e.x,y:e.y,fill:a,stroke:s,width:e.width,height:e.height,rx:2.5,ry:2.5,attrs:l};v(i,o);let c=n.boundaryFont();c.fontWeight="bold",c.fontSize=c.fontSize+2,c.fontColor=r,C(n)(e.label.text,i,e.x,e.y+e.label.Y,e.width,e.height,{fill:"#444444"},c),e.type&&""!==e.type.text&&(c=n.boundaryFont(),c.fontColor=r,C(n)(e.type.text,i,e.x,e.y+e.type.Y,e.width,e.height,{fill:"#444444"},c)),e.descr&&""!==e.descr.text&&(c=n.boundaryFont(),c.fontSize=c.fontSize-2,c.fontColor=r,C(n)(e.descr.text,i,e.x,e.y+e.descr.Y,e.width,e.height,{fill:"#444444"},c))}(t,e,R)},L=function(t,e,n,a){let s=0;for(const r of a){s=0;const a=n[r];let l=M(R,a.typeC4Shape.text);switch(l.fontSize=l.fontSize-2,a.typeC4Shape.width=(0,i.h)("«"+a.typeC4Shape.text+"»",l),a.typeC4Shape.height=l.fontSize+2,a.typeC4Shape.Y=R.c4ShapePadding,s=a.typeC4Shape.Y+a.typeC4Shape.height-4,a.image={width:0,height:0,Y:0},a.typeC4Shape.text){case"person":case"external_person":a.image.width=48,a.image.height=48,a.image.Y=s,s=a.image.Y+a.image.height}a.sprite&&(a.image.width=48,a.image.height=48,a.image.Y=s,s=a.image.Y+a.image.height);let o=a.wrap&&R.wrap,c=R.width-2*R.c4ShapePadding,h=M(R,a.typeC4Shape.text);h.fontSize=h.fontSize+2,h.fontWeight="bold",B("label",a,o,h,c),a.label.Y=s+8,s=a.label.Y+a.label.height,a.type&&""!==a.type.text?(a.type.text="["+a.type.text+"]",B("type",a,o,M(R,a.typeC4Shape.text),c),a.type.Y=s+5,s=a.type.Y+a.type.height):a.techn&&""!==a.techn.text&&(a.techn.text="["+a.techn.text+"]",B("techn",a,o,M(R,a.techn.text),c),a.techn.Y=s+5,s=a.techn.Y+a.techn.height);let d=s,u=a.label.width;a.descr&&""!==a.descr.text&&(B("descr",a,o,M(R,a.typeC4Shape.text),c),a.descr.Y=s+20,s=a.descr.Y+a.descr.height,u=Math.max(a.label.width,a.descr.width),d=s-5*a.descr.textLines),u+=R.c4ShapePadding,a.width=Math.max(a.width||R.width,u,R.width),a.height=Math.max(a.height||R.height,d,R.height),a.margin=a.margin||R.c4ShapeMargin,t.insert(a),k(e,a,R)}t.bumpLastMargin(R.c4ShapeMargin)};class I{constructor(t,e){this.x=t,this.y=e}}let U=function(t,e){let n=t.x,i=t.y,a=e.x,s=e.y,r=n+t.width/2,l=i+t.height/2,o=Math.abs(n-a),c=Math.abs(i-s),h=c/o,d=t.height/t.width,u=null;return i==s&&n<a?u=new I(n+t.width,l):i==s&&n>a?u=new I(n,l):n==a&&i<s?u=new I(r,i+t.height):n==a&&i>s&&(u=new I(r,i)),n>a&&i<s?u=d>=h?new I(n,l+h*t.width/2):new I(r-o/c*t.height/2,i+t.height):n<a&&i<s?u=d>=h?new I(n+t.width,l+h*t.width/2):new I(r+o/c*t.height/2,i+t.height):n<a&&i>s?u=d>=h?new I(n+t.width,l-h*t.width/2):new I(r+t.height/2*o/c,i):n>a&&i>s&&(u=d>=h?new I(n,l-t.width/2*h):new I(r-t.height/2*o/c,i)),u},F=function(t,e){let n={x:0,y:0};n.x=e.x+e.width/2,n.y=e.y+e.height/2;let i=U(t,n);return n.x=t.x+t.width/2,n.y=t.y+t.height/2,{startPoint:i,endPoint:U(e,n)}};function X(t,e,n,i,a){let s=new N(a);s.data.widthLimit=n.data.widthLimit/Math.min(D,i.length);for(let[r,l]of i.entries()){let i=0;l.image={width:0,height:0,Y:0},l.sprite&&(l.image.width=48,l.image.height=48,l.image.Y=i,i=l.image.Y+l.image.height);let o=l.wrap&&R.wrap,c=j(R);if(c.fontSize=c.fontSize+2,c.fontWeight="bold",B("label",l,o,c,s.data.widthLimit),l.label.Y=i+8,i=l.label.Y+l.label.height,l.type&&""!==l.type.text&&(l.type.text="["+l.type.text+"]",B("type",l,o,j(R),s.data.widthLimit),l.type.Y=i+5,i=l.type.Y+l.type.height),l.descr&&""!==l.descr.text){let t=j(R);t.fontSize=t.fontSize-2,B("descr",l,o,t,s.data.widthLimit),l.descr.Y=i+20,i=l.descr.Y+l.descr.height}if(0==r||r%D==0){let t=n.data.startx+R.diagramMarginX,e=n.data.stopy+R.diagramMarginY+i;s.setData(t,t,e,e)}else{let t=s.data.stopx!==s.data.startx?s.data.stopx+R.diagramMarginX:s.data.startx,e=s.data.starty;s.setData(t,t,e,e)}s.name=l.alias;let h=a.db.getC4ShapeArray(l.alias),d=a.db.getC4ShapeKeys(l.alias);d.length>0&&L(s,t,h,d),e=l.alias;let u=a.db.getBoundarys(e);u.length>0&&X(t,e,s,u,a),"global"!==l.alias&&Y(t,l,s),n.data.stopy=Math.max(s.data.stopy+R.c4ShapeMargin,n.data.stopy),n.data.stopx=Math.max(s.data.stopx+R.c4ShapeMargin,n.data.stopx),O=Math.max(O,n.data.stopx),T=Math.max(T,n.data.stopy)}}const z={drawPersonOrSystemArray:L,drawBoundary:Y,setConf:P,draw:function(t,e,n,s){R=(0,i.c)().c4;const r=(0,i.c)().securityLevel;let l;"sandbox"===r&&(l=(0,a.Ys)("#i"+e));const o="sandbox"===r?(0,a.Ys)(l.nodes()[0].contentDocument.body):(0,a.Ys)("body");let c=s.db;s.db.setWrap(R.wrap),w=c.getC4ShapeInRow(),D=c.getC4BoundaryInRow(),i.l.debug(`C:${JSON.stringify(R,null,2)}`);const h="sandbox"===r?o.select(`[id="${e}"]`):(0,a.Ys)(`[id="${e}"]`);h.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z"),function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")}(h),function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")}(h);let d=new N(s);d.setData(R.diagramMarginX,R.diagramMarginX,R.diagramMarginY,R.diagramMarginY),d.data.widthLimit=screen.availWidth,O=R.diagramMarginX,T=R.diagramMarginY;const u=s.db.getTitle();X(h,"",d,s.db.getBoundarys(""),s),function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z")}(h),function(t){t.append("defs").append("marker").attr("id","arrowend").attr("refX",1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 10 0 L 0 5 L 10 10 z")}(h),function(t){const e=t.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",16).attr("refY",4);e.append("path").attr("fill","black").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 9,2 V 6 L16,4 Z"),e.append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 0,1 L 6,7 M 6,1 L 0,7")}(h),function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",18).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")}(h),function(t,e,n,a){let s=0;for(let t of e){s+=1;let e=t.wrap&&R.wrap,l={fontFamily:(r=R).messageFontFamily,fontSize:r.messageFontSize,fontWeight:r.messageFontWeight};"C4Dynamic"===a.db.getC4Type()&&(t.label.text=s+": "+t.label.text);let o=(0,i.h)(t.label.text,l);B("label",t,e,l,o),t.techn&&""!==t.techn.text&&(o=(0,i.h)(t.techn.text,l),B("techn",t,e,l,o)),t.descr&&""!==t.descr.text&&(o=(0,i.h)(t.descr.text,l),B("descr",t,e,l,o));let c=n(t.from),h=n(t.to),d=F(c,h);t.startPoint=d.startPoint,t.endPoint=d.endPoint}var r;((t,e,n)=>{const i=t.append("g");let a=0;for(let t of e){let e=t.textColor?t.textColor:"#444444",s=t.lineColor?t.lineColor:"#444444",r=t.offsetX?parseInt(t.offsetX):0,l=t.offsetY?parseInt(t.offsetY):0,o="";if(0===a){let e=i.append("line");e.attr("x1",t.startPoint.x),e.attr("y1",t.startPoint.y),e.attr("x2",t.endPoint.x),e.attr("y2",t.endPoint.y),e.attr("stroke-width","1"),e.attr("stroke",s),e.style("fill","none"),"rel_b"!==t.type&&e.attr("marker-end","url("+o+"#arrowhead)"),"birel"!==t.type&&"rel_b"!==t.type||e.attr("marker-start","url("+o+"#arrowend)"),a=-1}else{let e=i.append("path");e.attr("fill","none").attr("stroke-width","1").attr("stroke",s).attr("d","Mstartx,starty Qcontrolx,controly stopx,stopy ".replaceAll("startx",t.startPoint.x).replaceAll("starty",t.startPoint.y).replaceAll("controlx",t.startPoint.x+(t.endPoint.x-t.startPoint.x)/2-(t.endPoint.x-t.startPoint.x)/4).replaceAll("controly",t.startPoint.y+(t.endPoint.y-t.startPoint.y)/2).replaceAll("stopx",t.endPoint.x).replaceAll("stopy",t.endPoint.y)),"rel_b"!==t.type&&e.attr("marker-end","url("+o+"#arrowhead)"),"birel"!==t.type&&"rel_b"!==t.type||e.attr("marker-start","url("+o+"#arrowend)")}let c=n.messageFont();C(n)(t.label.text,i,Math.min(t.startPoint.x,t.endPoint.x)+Math.abs(t.endPoint.x-t.startPoint.x)/2+r,Math.min(t.startPoint.y,t.endPoint.y)+Math.abs(t.endPoint.y-t.startPoint.y)/2+l,t.label.width,t.label.height,{fill:e},c),t.techn&&""!==t.techn.text&&(c=n.messageFont(),C(n)("["+t.techn.text+"]",i,Math.min(t.startPoint.x,t.endPoint.x)+Math.abs(t.endPoint.x-t.startPoint.x)/2+r,Math.min(t.startPoint.y,t.endPoint.y)+Math.abs(t.endPoint.y-t.startPoint.y)/2+n.messageFontSize+5+l,Math.max(t.label.width,t.techn.width),t.techn.height,{fill:e,"font-style":"italic"},c))}})(t,e,R)}(h,s.db.getRels(),s.db.getC4Shape,s),d.data.stopx=O,d.data.stopy=T;const p=d.data;let y=p.stopy-p.starty+2*R.diagramMarginY;const f=p.stopx-p.startx+2*R.diagramMarginX;u&&h.append("text").text(u).attr("x",(p.stopx-p.startx)/2-4*R.diagramMarginX).attr("y",p.starty+R.diagramMarginY),(0,i.i)(h,y,f,R.useMaxWidth);const g=u?60:0;h.attr("viewBox",p.startx-R.diagramMarginX+" -"+(R.diagramMarginY+g)+" "+f+" "+(y+g)),i.l.debug("models:",p)}},Q={parser:o,db:S,renderer:z,styles:t=>`.person {\n    stroke: ${t.personBorder};\n    fill: ${t.personBkg};\n  }\n`,init:t=>{z.setConf(t.c4)}}},8770:function(t,e,n){n.d(e,{a:function(){return s},b:function(){return o},c:function(){return l},d:function(){return a},e:function(){return h},f:function(){return r},g:function(){return c}});var i=n(7967);const a=function(t,e){const n=t.append("rect");if(n.attr("x",e.x),n.attr("y",e.y),n.attr("fill",e.fill),n.attr("stroke",e.stroke),n.attr("width",e.width),n.attr("height",e.height),n.attr("rx",e.rx),n.attr("ry",e.ry),"undefined"!==e.attrs&&null!==e.attrs)for(let t in e.attrs)n.attr(t,e.attrs[t]);return"undefined"!==e.class&&n.attr("class",e.class),n},s=function(t,e){a(t,{x:e.startx,y:e.starty,width:e.stopx-e.startx,height:e.stopy-e.starty,fill:e.fill,stroke:e.stroke,class:"rect"}).lower()},r=function(t,e){const n=e.text.replace(/<br\s*\/?>/gi," "),i=t.append("text");i.attr("x",e.x),i.attr("y",e.y),i.attr("class","legend"),i.style("text-anchor",e.anchor),void 0!==e.class&&i.attr("class",e.class);const a=i.append("tspan");return a.attr("x",e.x+2*e.textMargin),a.text(n),i},l=function(t,e,n,a){const s=t.append("image");s.attr("x",e),s.attr("y",n);var r=(0,i.N)(a);s.attr("xlink:href",r)},o=function(t,e,n,a){const s=t.append("use");s.attr("x",e),s.attr("y",n);const r=(0,i.N)(a);s.attr("xlink:href","#"+r)},c=function(){return{x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0}},h=function(){return{x:0,y:0,width:100,height:100,fill:void 0,anchor:void 0,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}}}}]);