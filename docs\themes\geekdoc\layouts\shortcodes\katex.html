<!-- prettier-ignore-start -->
{{ if not (.Page.Scratch.Get "katex") }}
  <!-- Include katex only first time -->
  <link
    rel="stylesheet"
    href="{{ index (index .Site.Data.assets "katex.css") "src" | relURL }}"
  />
  <script defer src="{{ index (index .Site.Data.assets "katex.js") "src" | relURL }}"></script>
  {{ .Page.Scratch.Set "katex" true }}
{{ end }}
<!-- prettier-ignore-end -->

<span class="gdoc-katex {{ with .Get "class" }}{{ . }}{{ end }}">
  {{ cond (in .Params "display") "\\[" "\\(" -}}
  {{- trim .Inner "\n" -}}
  {{- cond (in .Params "display") "\\]" "\\)" -}}
</span>
{{- /* Drop trailing newlines */ -}}
