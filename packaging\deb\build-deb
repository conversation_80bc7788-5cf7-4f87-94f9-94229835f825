#!/usr/bin/env bash
set -x
set -e

# untar sources
mkdir -p /root/build-deb/app
tar -C /root/build-deb -xzf /sources/app.tgz

# link them to their canonical path
mkdir -p /go/src/github.com/Mirantis
ln -snf /root/build-deb/app /go/src/github.com/Mirantis/cri-dockerd

EPOCH="${EPOCH:-}"
EPOCH_SEP=""
if [[ ! -z "$EPOCH" ]]; then
    EPOCH_SEP=":"
fi

if [[ -z "$DEB_VERSION" ]]; then
    echo "DEB_VERSION is required to build deb packages"
    exit 1
fi

echo VERSION AAA $VERSION

VERSION=${VERSION:-$( cat app/VERSION )}

echo VERSION bbb $VERSION

debSource="$(awk -F ': ' '$1 == "Source" { print $2; exit }' debian/control)"
debMaintainer="$(awk -F ': ' '$1 == "Maintainer" { print $2; exit }' debian/control)"
debDate="$(date --rfc-2822)"

cat > "debian/changelog" <<-EOF
$debSource (${EPOCH}${EPOCH_SEP}${DEB_VERSION}-0~${DISTRO}-${SUITE}) $SUITE; urgency=low
  * Version: $VERSION
 -- $debMaintainer  $debDate
EOF
# The space above at the start of the line for the debMaintainer is very important

# Give the script a git commit because it wants it
export GITCOMMIT=${GITCOMMIT-$(cd engine; $GIT_COMMAND rev-parse --short HEAD)}

echo VERSION BBB $VERSION
dpkg-buildpackage -uc -us -I.git
destination="/build"
mkdir -p "$destination"
mv -v /root/*docker* "$destination"
