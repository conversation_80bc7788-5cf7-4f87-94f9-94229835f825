{{- $format := default "html" (.Get "format") }}
{{- $tocLevels := default (default 6 .Site.Params.geekdocToC) .Page.Params.geekdocToC }}

{{- if and $tocLevels .Page.TableOfContents -}}
  {{- if not (eq ($format | lower) "raw") -}}
    <div class="gdoc-toc gdoc-toc__level--{{ $tocLevels }}">
      {{ .Page.TableOfContents }}
      <hr />
    </div>
  {{- else -}}
    {{ .Page.TableOfContents }}
  {{- end -}}
{{- end -}}
