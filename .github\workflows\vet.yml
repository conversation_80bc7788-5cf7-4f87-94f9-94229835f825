name: Vet Go Code

on:
  workflow_call:

jobs:
  vet:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Load environment
        uses: c-py/action-dotenv-to-setenv@v4
        with:
          env-file: .github/.env

      - name: Setup Go ${{ env.GO_VERSION }}
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Vet Go Code
        working-directory: .
        run: go vet
