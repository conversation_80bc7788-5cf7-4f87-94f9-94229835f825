The docs are generated using [<PERSON>](https://gohugo.io/) and the [Geekdocs](https://themes.gohugo.io/hugo-geekdoc/) theme.

## How to generate the documentation

The docs can be ran locally with hot-reloading to make editing easier. To do so,
run the following command in the project's root directory:

```bash
make docs
```

This will launch the development server that is included with <PERSON>. You can then
access the docs at http://localhost:1313/
