load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(
    licenses = ["notice"],  # Apache 2.0
)

go_library(
    name = "go_default_library",
    srcs = [
        "checker.go",
        "cost.go",
        "env.go",
        "errors.go",
        "format.go",
        "mapping.go",
        "options.go",
        "printer.go",
        "scopes.go",
        "standard.go",
        "types.go",
    ],
    importpath = "github.com/google/cel-go/checker",
    visibility = ["//visibility:public"],
    deps = [
        "//checker/decls:go_default_library",
        "//common:go_default_library",
        "//common/ast:go_default_library",
        "//common/containers:go_default_library",
        "//common/debug:go_default_library",
        "//common/decls:go_default_library",
        "//common/operators:go_default_library",
        "//common/overloads:go_default_library",
        "//common/stdlib:go_default_library",
        "//common/types:go_default_library",
        "//common/types/pb:go_default_library",
        "//common/types/ref:go_default_library",
        "//parser:go_default_library",
        "@org_golang_google_genproto_googleapis_api//expr/v1alpha1:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
        "@org_golang_google_protobuf//types/known/emptypb:go_default_library",
        "@org_golang_google_protobuf//types/known/structpb:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    size = "small",
    srcs = [
        "checker_test.go",
        "cost_test.go",
        "env_test.go",
        "format_test.go",
    ],
    embed = [
        ":go_default_library",
    ],
    deps = [
        "//common/types:go_default_library",
        "//parser:go_default_library",
        "//test:go_default_library",
        "//test/proto2pb:go_default_library",
        "//test/proto3pb:go_default_library",
        "@com_github_antlr_antlr4_runtime_go_antlr_v4//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)
