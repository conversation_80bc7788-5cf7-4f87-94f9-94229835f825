load("@io_bazel_rules_go//go:def.bzl", "go_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],  # Apache 2.0
)

go_library(
    name = "go_default_library",
    srcs = [
        "decls.go",
    ],
    importpath = "github.com/google/cel-go/checker/decls",
    deps = [
        "@org_golang_google_genproto_googleapis_api//expr/v1alpha1:go_default_library",
        "@org_golang_google_protobuf//types/known/emptypb:go_default_library",
        "@org_golang_google_protobuf//types/known/structpb:go_default_library",
    ],
)
