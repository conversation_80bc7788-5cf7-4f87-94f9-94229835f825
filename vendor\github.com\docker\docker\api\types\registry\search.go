package registry

import (
	"context"

	"github.com/docker/docker/api/types/filters"
)

// SearchOptions holds parameters to search images with.
type SearchOptions struct {
	RegistryAuth string

	// PrivilegeFunc is a [types.RequestPrivilegeFunc] the client can
	// supply to retry operations after getting an authorization error.
	//
	// It must return the registry authentication header value in base64
	// format, or an error if the privilege request fails.
	PrivilegeFunc func(context.Context) (string, error)
	Filters       filters.Args
	Limit         int
}

// SearchResult describes a search result returned from a registry
type SearchResult struct {
	// StarCount indicates the number of stars this repository has
	StarCount int `json:"star_count"`
	// IsOfficial is true if the result is from an official repository.
	IsOfficial bool `json:"is_official"`
	// Name is the name of the repository
	Name string `json:"name"`
	// IsAutomated indicates whether the result is automated.
	//
	// Deprecated: the "is_automated" field is deprecated and will always be "false".
	IsAutomated bool `json:"is_automated"`
	// Description is a textual description of the repository
	Description string `json:"description"`
}

// SearchResults lists a collection search results returned from a registry
type SearchResults struct {
	// Query contains the query string that generated the search results
	Query string `json:"query"`
	// NumResults indicates the number of results the query returned
	NumResults int `json:"num_results"`
	// Results is a slice containing the actual results for the search
	Results []SearchResult `json:"results"`
}
