"use strict";(self.webpackChunkgeekdoc=self.webpackChunkgeekdoc||[]).push([[980],{7980:function(e,t,s){s.d(t,{diagram:function(){return N}});var o=s(2902),r=s(5625),a=s(5740),i=s(5103),n=s(2759);s(7484),s(7967),s(7856),s(9451),s(9368);const d="rect",c="rectWithTitle",l="statediagram",p=`${l}-state`,g="transition",b=`${g} note-edge`,h=`${l}-note`,u=`${l}-cluster`,y=`${l}-cluster-alt`,f="parent",w="note",x="----",$=`${x}${w}`,m=`${x}${f}`,T="fill:none",k="fill: #333",S="text",D="normal";let A={},v=0;function B(e="",t=0,s="",o=x){return`state-${e}${null!==s&&s.length>0?`${o}${s}`:""}-${t}`}const E=(e,t,s,r,a,n)=>{const l=s.id,g=null==(x=r[l])?"":x.classes?x.classes.join(" "):"";var x;if("root"!==l){let t=d;!0===s.start&&(t="start"),!1===s.start&&(t="end"),s.type!==o.D&&(t=s.type),A[l]||(A[l]={id:l,shape:t,description:i.e.sanitizeText(l,(0,i.c)()),classes:`${g} ${p}`});const r=A[l];s.description&&(Array.isArray(r.description)?(r.shape=c,r.description.push(s.description)):r.description.length>0?(r.shape=c,r.description===l?r.description=[s.description]:r.description=[r.description,s.description]):(r.shape=d,r.description=s.description),r.description=i.e.sanitizeTextOrArray(r.description,(0,i.c)())),1===r.description.length&&r.shape===c&&(r.shape=d),!r.type&&s.doc&&(i.l.info("Setting cluster for ",l,R(s)),r.type="group",r.dir=R(s),r.shape=s.type===o.a?"divider":"roundedWithTitle",r.classes=r.classes+" "+u+" "+(n?y:""));const a={labelStyle:"",shape:r.shape,labelText:r.description,classes:r.classes,style:"",id:l,dir:r.dir,domId:B(l,v),type:r.type,padding:15,centerLabel:!0};if(s.note){const t={labelStyle:"",shape:"note",labelText:s.note.text,classes:h,style:"",id:l+$+"-"+v,domId:B(l,v,w),type:r.type,padding:15},o={labelStyle:"",shape:"noteGroup",labelText:s.note.text,classes:r.classes,style:"",id:l+m,domId:B(l,v,f),type:"group",padding:0};v++;const i=l+m;e.setNode(i,o),e.setNode(t.id,t),e.setNode(l,a),e.setParent(l,i),e.setParent(t.id,i);let n=l,d=t.id;"left of"===s.note.position&&(n=t.id,d=l),e.setEdge(n,d,{arrowhead:"none",arrowType:"",style:T,labelStyle:"",classes:b,arrowheadStyle:k,labelpos:"c",labelType:S,thickness:D})}else e.setNode(l,a)}t&&"root"!==t.id&&(i.l.trace("Setting node ",l," to be child of its parent ",t.id),e.setParent(l,t.id)),s.doc&&(i.l.trace("Adding nodes children "),C(e,s,s.doc,r,a,!n))},C=(e,t,s,r,a,n)=>{i.l.trace("items",s),s.forEach((s=>{switch(s.stmt){case o.b:case o.D:E(e,t,s,r,a,n);break;case o.S:{E(e,t,s.state1,r,a,n),E(e,t,s.state2,r,a,n);const o={id:"edge"+v,arrowhead:"normal",arrowTypeEnd:"arrow_barb",style:T,labelStyle:"",label:i.e.sanitizeText(s.description,(0,i.c)()),arrowheadStyle:k,labelpos:"c",labelType:S,thickness:D,classes:g};e.setEdge(s.state1.id,s.state2.id,o,v),v++}}}))},R=(e,t=o.c)=>{let s=t;if(e.doc)for(let t=0;t<e.doc.length;t++){const o=e.doc[t];"dir"===o.stmt&&(s=o.value)}return s},V={setConf:function(e){const t=Object.keys(e);for(const s of t)e[s]},getClasses:function(e,t){i.l.trace("Extracting classes"),t.db.clear();try{return t.parser.parse(e),t.db.extract(t.db.getRootDocV2()),t.db.getClasses()}catch(e){return e}},draw:async function(e,t,s,o){i.l.info("Drawing state diagram (v2)",t),A={},o.db.getDirection();const{securityLevel:c,state:p}=(0,i.c)(),g=p.nodeSpacing||50,b=p.rankSpacing||50;i.l.info(o.db.getRootDocV2()),o.db.extract(o.db.getRootDocV2()),i.l.info(o.db.getRootDocV2());const h=o.db.getStates(),u=new r.k({multigraph:!0,compound:!0}).setGraph({rankdir:R(o.db.getRootDocV2()),nodesep:g,ranksep:b,marginx:8,marginy:8}).setDefaultEdgeLabel((function(){return{}}));let y;E(u,void 0,o.db.getRootDocV2(),h,o.db,!0),"sandbox"===c&&(y=(0,a.Ys)("#i"+t));const f="sandbox"===c?(0,a.Ys)(y.nodes()[0].contentDocument.body):(0,a.Ys)("body"),w=f.select(`[id="${t}"]`),x=f.select("#"+t+" g");await(0,n.r)(x,u,["barb"],l,t),i.u.insertTitle(w,"statediagramTitleText",p.titleTopMargin,o.db.getDiagramTitle());const $=w.node().getBBox(),m=$.width+16,T=$.height+16;w.attr("class",l);const k=w.node().getBBox();(0,i.i)(w,T,m,p.useMaxWidth);const S=`${k.x-8} ${k.y-8} ${m} ${T}`;i.l.debug(`viewBox ${S}`),w.attr("viewBox",S);const D=document.querySelectorAll('[id="'+t+'"] .edgeLabel .label');for(const e of D){const t=e.getBBox(),s=document.createElementNS("http://www.w3.org/2000/svg",d);s.setAttribute("rx",0),s.setAttribute("ry",0),s.setAttribute("width",t.width),s.setAttribute("height",t.height),e.insertBefore(s,e.firstChild)}}},N={parser:o.p,db:o.d,renderer:V,styles:o.s,init:e=>{e.state||(e.state={}),e.state.arrowMarkerAbsolute=e.arrowMarkerAbsolute,o.d.clear()}}}}]);