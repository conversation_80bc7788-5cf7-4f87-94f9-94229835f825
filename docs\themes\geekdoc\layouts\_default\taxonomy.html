{{ define "main" }}
  {{ range .Paginator.Pages }}
    <article class="gdoc-post">
      <header class="gdoc-post__header">
        <h1 class="gdoc-post__title">
          <a href="{{ .RelPermalink }}">{{ partial "utils/title" . }}</a>
        </h1>
      </header>

      <section class="gdoc-markdown">
        {{ .Summary }}
      </section>

      <div class="gdoc-post__readmore">
        {{ if .Truncated }}
          <a
            class="flex-inline align-center fake-link"
            title="{{ i18n "posts_read_more" }}"
            href="{{ .RelPermalink }}"
          >
            {{ i18n "posts_read_more" }}
            <i class="gdoc-icon">gdoc_arrow_right_alt</i>
          </a>
        {{ end }}
      </div>

      <footer class="gdoc-post__footer">
        <div class="flex flex-wrap align-center gdoc-post__meta">
          {{ partial "posts/metadata.html" . }}
        </div>
      </footer>
    </article>
  {{ end }}
  {{ partial "pagination.html" . }}
{{ end }}

{{ define "post-tag" }}
  <span class="gdoc-post__tag">
    <span class="gdoc-button">
      <a
        class="gdoc-button__link"
        href="{{ .page.RelPermalink }}"
        title="{{ i18n "posts_tagged_with" .name }}"
      >
        {{ .name }}
      </a>
    </span>
  </span>
{{ end }}
