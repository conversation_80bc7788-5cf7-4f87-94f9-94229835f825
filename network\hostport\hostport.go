/*
Copyright 2021 Mirantis

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package hostport

import (
	"fmt"
	"net"
	"strconv"
	"strings"

	"github.com/Mirantis/cri-dockerd/config"

	"github.com/sirupsen/logrus"

	utiliptables "k8s.io/kubernetes/pkg/util/iptables"
)

const (
	// the hostport chain
	kubeHostportsChain utiliptables.Chain = "KUBE-HOSTPORTS"
	// prefix for hostport chains
	kubeHostportChainPrefix string = "KUBE-HP-"
	// kubeMarkMasqChain is the mark-for-masquerade chain
	kubeMarkMasqChain string = "KUBE-MARK-MASQ"
)

// PortMapping represents a network port in a container
type PortMapping struct {
	HostPort      int32
	ContainerPort int32
	Protocol      config.Protocol
	HostIP        string
}

// PodPortMapping represents a pod's network state and associated container port mappings
type PodPortMapping struct {
	Namespace    string
	Name         string
	PortMappings []*PortMapping
	HostNetwork  bool
	IP           net.IP
}

// ipFamily refers to a specific family if not empty, i.e. "4" or "6".
type ipFamily string

// Constants for valid IPFamily:
const (
	IPv4 ipFamily = "4"
	IPv6 ipFamily = "6"
)

type hostport struct {
	ipFamily ipFamily
	ip       string
	port     int32
	protocol string
}

type hostportOpener func(*hostport) (closeable, error)

type closeable interface {
	Close() error
}

func openLocalPort(hp *hostport) (closeable, error) {
	// For ports on node IPs, open the actual port and hold it, even though we
	// use iptables to redirect traffic.
	// This ensures a) that it's safe to use that port and b) that (a) stays
	// true.  The risk is that some process on the node (e.g. sshd or kubelet)
	// is using a port and we give that same port out to a Service.  That would
	// be bad because iptables would silently claim the traffic but the process
	// would never know.
	// NOTE: We should not need to have a real listen()ing socket - bind()
	// should be enough, but I can't figure out a way to e2e test without
	// it.  Tools like 'ss' and 'netstat' do not show sockets that are
	// bind()ed but not listen()ed, and at least the default debian netcat
	// has no way to avoid about 10 seconds of retries.
	var socket closeable
	// open the socket on the HostIP and HostPort specified
	address := net.JoinHostPort(hp.ip, strconv.Itoa(int(hp.port)))
	switch hp.protocol {
	case "tcp":
		network := "tcp" + string(hp.ipFamily)
		listener, err := net.Listen(network, address)
		if err != nil {
			return nil, err
		}
		socket = listener
	case "udp":
		network := "udp" + string(hp.ipFamily)
		addr, err := net.ResolveUDPAddr(network, address)
		if err != nil {
			return nil, err
		}
		conn, err := net.ListenUDP(network, addr)
		if err != nil {
			return nil, err
		}
		socket = conn
	default:
		return nil, fmt.Errorf("unknown protocol %q", hp.protocol)
	}
	return socket, nil
}

// portMappingToHostport creates hostport structure based on input portmapping
func portMappingToHostport(portMapping *PortMapping, family ipFamily) hostport {
	return hostport{
		ipFamily: family,
		ip:       portMapping.HostIP,
		port:     portMapping.HostPort,
		protocol: strings.ToLower(string(portMapping.Protocol)),
	}
}

// ensureKubeHostportChains ensures the KUBE-HOSTPORTS chain is setup correctly
func ensureKubeHostportChains(iptables utiliptables.Interface, natInterfaceName string) error {
	logrus.Debug("Ensuring kubelet hostport chains")
	// Ensure kubeHostportChain
	if _, err := iptables.EnsureChain(utiliptables.TableNAT, kubeHostportsChain); err != nil {
		return fmt.Errorf(
			"failed to ensure that %s chain %s exists: %v",
			utiliptables.TableNAT,
			kubeHostportsChain,
			err,
		)
	}
	tableChainsNeedJumpServices := []struct {
		table utiliptables.Table
		chain utiliptables.Chain
	}{
		{utiliptables.TableNAT, utiliptables.ChainOutput},
		{utiliptables.TableNAT, utiliptables.ChainPrerouting},
	}
	args := []string{
		"-m", "comment", "--comment", "kube hostport portals",
		"-m", "addrtype", "--dst-type", "LOCAL",
		"-j", string(kubeHostportsChain),
	}
	for _, tc := range tableChainsNeedJumpServices {
		// KUBE-HOSTPORTS chain needs to be appended to the system chains.
		// This ensures KUBE-SERVICES chain gets processed first.
		// Since rules in KUBE-HOSTPORTS chain matches broader cases, allow the more specific rules to be processed first.
		if _, err := iptables.EnsureRule(utiliptables.Append, tc.table, tc.chain, args...); err != nil {
			return fmt.Errorf(
				"failed to ensure that %s chain %s jumps to %s: %v",
				tc.table,
				tc.chain,
				kubeHostportsChain,
				err,
			)
		}
	}
	if natInterfaceName != "" && natInterfaceName != "lo" {
		// Need to SNAT traffic from localhost
		localhost := "*********/8"
		if iptables.IsIPv6() {
			localhost = "::1/128"
		}
		args = []string{
			"-m",
			"comment",
			"--comment",
			"SNAT for localhost access to hostports",
			"-o",
			natInterfaceName,
			"-s",
			localhost,
			"-j",
			"MASQUERADE",
		}
		if _, err := iptables.EnsureRule(utiliptables.Append, utiliptables.TableNAT, utiliptables.ChainPostrouting, args...); err != nil {
			return fmt.Errorf(
				"failed to ensure that %s chain %s jumps to MASQUERADE: %v",
				utiliptables.TableNAT,
				utiliptables.ChainPostrouting,
				err,
			)
		}
	}
	return nil
}
