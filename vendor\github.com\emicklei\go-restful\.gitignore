# Compiled Object files, Static and Dynamic libs (Shared Objects)
*.o
*.a
*.so

# Folders
_obj
_test

# Architecture specific extensions/prefixes
*.[568vq]
[568vq].out

*.cgo1.go
*.cgo2.c
_cgo_defun.c
_cgo_gotypes.go
_cgo_export.*

_testmain.go

*.exe

restful.html

*.out

tmp.prof

go-restful.test

examples/restful-basic-authentication

examples/restful-encoding-filter

examples/restful-filters

examples/restful-hello-world

examples/restful-resource-functions

examples/restful-serve-static

examples/restful-user-service

*.DS_Store
examples/restful-user-resource

examples/restful-multi-containers

examples/restful-form-handling

examples/restful-CORS-filter

examples/restful-options-filter

examples/restful-curly-router

examples/restful-cpuprofiler-service

examples/restful-pre-post-filters

curly.prof

examples/restful-NCSA-logging

examples/restful-html-template

s.html
restful-path-tail
.idea
