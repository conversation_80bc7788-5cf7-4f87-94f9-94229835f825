"use strict";(self.webpackChunkgeekdoc=self.webpackChunkgeekdoc||[]).push([[770],{5935:function(t,e,a){a.d(e,{diagram:function(){return ut}});var i=a(5103),n=a(5740),r=a(8770),s=a(7967),o=(a(7484),a(7856),function(){var t=function(t,e,a,i){for(a=a||{},i=t.length;i--;a[t[i]]=e);return a},e=[1,2],a=[1,3],i=[1,5],n=[1,7],r=[2,5],s=[1,15],o=[1,17],c=[1,19],l=[1,21],h=[1,22],d=[1,23],p=[1,29],u=[1,30],g=[1,31],x=[1,32],y=[1,33],m=[1,34],f=[1,35],b=[1,36],T=[1,37],E=[1,38],_=[1,39],v=[1,40],w=[1,41],P=[1,43],k=[1,44],L=[1,46],I=[1,47],N=[1,48],M=[1,49],A=[1,50],S=[1,51],O=[1,54],D=[1,4,5,19,21,23,26,28,34,35,36,38,40,41,42,43,44,46,48,49,51,52,53,54,55,57,58,63,64,65,66,74,84],R=[4,5,21,55,57],Y=[4,5,19,21,23,26,28,34,35,36,38,40,41,42,43,44,46,48,49,51,55,57,58,63,64,65,66,74,84],$=[4,5,19,21,23,26,28,34,35,36,38,40,41,42,43,44,46,48,49,51,54,55,57,58,63,64,65,66,74,84],C=[4,5,19,21,23,26,28,34,35,36,38,40,41,42,43,44,46,48,49,51,53,55,57,58,63,64,65,66,74,84],B=[4,5,19,21,23,26,28,34,35,36,38,40,41,42,43,44,46,48,49,51,52,55,57,58,63,64,65,66,74,84],V=[72,73,74],F=[1,128],W=[1,4,5,7,19,21,23,26,28,34,35,36,38,40,41,42,43,44,46,48,49,51,52,53,54,55,57,58,63,64,65,66,74,84],q={trace:function(){},yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,directive:6,SD:7,document:8,line:9,statement:10,box_section:11,box_line:12,participant_statement:13,openDirective:14,typeDirective:15,closeDirective:16,":":17,argDirective:18,box:19,restOfLine:20,end:21,signal:22,autonumber:23,NUM:24,off:25,activate:26,actor:27,deactivate:28,note_statement:29,links_statement:30,link_statement:31,properties_statement:32,details_statement:33,title:34,legacy_title:35,acc_title:36,acc_title_value:37,acc_descr:38,acc_descr_value:39,acc_descr_multiline_value:40,loop:41,rect:42,opt:43,alt:44,else_sections:45,par:46,par_sections:47,par_over:48,critical:49,option_sections:50,break:51,option:52,and:53,else:54,participant:55,AS:56,participant_actor:57,note:58,placement:59,text2:60,over:61,actor_pair:62,links:63,link:64,properties:65,details:66,spaceList:67,",":68,left_of:69,right_of:70,signaltype:71,"+":72,"-":73,ACTOR:74,SOLID_OPEN_ARROW:75,DOTTED_OPEN_ARROW:76,SOLID_ARROW:77,DOTTED_ARROW:78,SOLID_CROSS:79,DOTTED_CROSS:80,SOLID_POINT:81,DOTTED_POINT:82,TXT:83,open_directive:84,type_directive:85,arg_directive:86,close_directive:87,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",7:"SD",17:":",19:"box",20:"restOfLine",21:"end",23:"autonumber",24:"NUM",25:"off",26:"activate",28:"deactivate",34:"title",35:"legacy_title",36:"acc_title",37:"acc_title_value",38:"acc_descr",39:"acc_descr_value",40:"acc_descr_multiline_value",41:"loop",42:"rect",43:"opt",44:"alt",46:"par",48:"par_over",49:"critical",51:"break",52:"option",53:"and",54:"else",55:"participant",56:"AS",57:"participant_actor",58:"note",61:"over",63:"links",64:"link",65:"properties",66:"details",68:",",69:"left_of",70:"right_of",72:"+",73:"-",74:"ACTOR",75:"SOLID_OPEN_ARROW",76:"DOTTED_OPEN_ARROW",77:"SOLID_ARROW",78:"DOTTED_ARROW",79:"SOLID_CROSS",80:"DOTTED_CROSS",81:"SOLID_POINT",82:"DOTTED_POINT",83:"TXT",84:"open_directive",85:"type_directive",86:"arg_directive",87:"close_directive"},productions_:[0,[3,2],[3,2],[3,2],[3,2],[8,0],[8,2],[9,2],[9,1],[9,1],[11,0],[11,2],[12,2],[12,1],[12,1],[6,4],[6,6],[10,1],[10,4],[10,2],[10,4],[10,3],[10,3],[10,2],[10,3],[10,3],[10,2],[10,2],[10,2],[10,2],[10,2],[10,1],[10,1],[10,2],[10,2],[10,1],[10,4],[10,4],[10,4],[10,4],[10,4],[10,4],[10,4],[10,4],[10,1],[50,1],[50,4],[47,1],[47,4],[45,1],[45,4],[13,5],[13,3],[13,5],[13,3],[29,4],[29,4],[30,3],[31,3],[32,3],[33,3],[67,2],[67,1],[62,3],[62,1],[59,1],[59,1],[22,5],[22,5],[22,4],[27,1],[71,1],[71,1],[71,1],[71,1],[71,1],[71,1],[71,1],[71,1],[60,1],[14,1],[15,1],[18,1],[16,1]],performAction:function(t,e,a,i,n,r,s){var o=r.length-1;switch(n){case 4:return i.apply(r[o]),r[o];case 5:case 10:case 9:case 14:this.$=[];break;case 6:case 11:r[o-1].push(r[o]),this.$=r[o-1];break;case 7:case 8:case 12:case 13:case 64:this.$=r[o];break;case 18:r[o-1].unshift({type:"boxStart",boxData:i.parseBoxData(r[o-2])}),r[o-1].push({type:"boxEnd",boxText:r[o-2]}),this.$=r[o-1];break;case 20:this.$={type:"sequenceIndex",sequenceIndex:Number(r[o-2]),sequenceIndexStep:Number(r[o-1]),sequenceVisible:!0,signalType:i.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceIndex:Number(r[o-1]),sequenceIndexStep:1,sequenceVisible:!0,signalType:i.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"sequenceIndex",sequenceVisible:!1,signalType:i.LINETYPE.AUTONUMBER};break;case 23:this.$={type:"sequenceIndex",sequenceVisible:!0,signalType:i.LINETYPE.AUTONUMBER};break;case 24:this.$={type:"activeStart",signalType:i.LINETYPE.ACTIVE_START,actor:r[o-1]};break;case 25:this.$={type:"activeEnd",signalType:i.LINETYPE.ACTIVE_END,actor:r[o-1]};break;case 31:i.setDiagramTitle(r[o].substring(6)),this.$=r[o].substring(6);break;case 32:i.setDiagramTitle(r[o].substring(7)),this.$=r[o].substring(7);break;case 33:this.$=r[o].trim(),i.setAccTitle(this.$);break;case 34:case 35:this.$=r[o].trim(),i.setAccDescription(this.$);break;case 36:r[o-1].unshift({type:"loopStart",loopText:i.parseMessage(r[o-2]),signalType:i.LINETYPE.LOOP_START}),r[o-1].push({type:"loopEnd",loopText:r[o-2],signalType:i.LINETYPE.LOOP_END}),this.$=r[o-1];break;case 37:r[o-1].unshift({type:"rectStart",color:i.parseMessage(r[o-2]),signalType:i.LINETYPE.RECT_START}),r[o-1].push({type:"rectEnd",color:i.parseMessage(r[o-2]),signalType:i.LINETYPE.RECT_END}),this.$=r[o-1];break;case 38:r[o-1].unshift({type:"optStart",optText:i.parseMessage(r[o-2]),signalType:i.LINETYPE.OPT_START}),r[o-1].push({type:"optEnd",optText:i.parseMessage(r[o-2]),signalType:i.LINETYPE.OPT_END}),this.$=r[o-1];break;case 39:r[o-1].unshift({type:"altStart",altText:i.parseMessage(r[o-2]),signalType:i.LINETYPE.ALT_START}),r[o-1].push({type:"altEnd",signalType:i.LINETYPE.ALT_END}),this.$=r[o-1];break;case 40:r[o-1].unshift({type:"parStart",parText:i.parseMessage(r[o-2]),signalType:i.LINETYPE.PAR_START}),r[o-1].push({type:"parEnd",signalType:i.LINETYPE.PAR_END}),this.$=r[o-1];break;case 41:r[o-1].unshift({type:"parStart",parText:i.parseMessage(r[o-2]),signalType:i.LINETYPE.PAR_OVER_START}),r[o-1].push({type:"parEnd",signalType:i.LINETYPE.PAR_END}),this.$=r[o-1];break;case 42:r[o-1].unshift({type:"criticalStart",criticalText:i.parseMessage(r[o-2]),signalType:i.LINETYPE.CRITICAL_START}),r[o-1].push({type:"criticalEnd",signalType:i.LINETYPE.CRITICAL_END}),this.$=r[o-1];break;case 43:r[o-1].unshift({type:"breakStart",breakText:i.parseMessage(r[o-2]),signalType:i.LINETYPE.BREAK_START}),r[o-1].push({type:"breakEnd",optText:i.parseMessage(r[o-2]),signalType:i.LINETYPE.BREAK_END}),this.$=r[o-1];break;case 46:this.$=r[o-3].concat([{type:"option",optionText:i.parseMessage(r[o-1]),signalType:i.LINETYPE.CRITICAL_OPTION},r[o]]);break;case 48:this.$=r[o-3].concat([{type:"and",parText:i.parseMessage(r[o-1]),signalType:i.LINETYPE.PAR_AND},r[o]]);break;case 50:this.$=r[o-3].concat([{type:"else",altText:i.parseMessage(r[o-1]),signalType:i.LINETYPE.ALT_ELSE},r[o]]);break;case 51:r[o-3].type="addParticipant",r[o-3].description=i.parseMessage(r[o-1]),this.$=r[o-3];break;case 52:r[o-1].type="addParticipant",this.$=r[o-1];break;case 53:r[o-3].type="addActor",r[o-3].description=i.parseMessage(r[o-1]),this.$=r[o-3];break;case 54:r[o-1].type="addActor",this.$=r[o-1];break;case 55:this.$=[r[o-1],{type:"addNote",placement:r[o-2],actor:r[o-1].actor,text:r[o]}];break;case 56:r[o-2]=[].concat(r[o-1],r[o-1]).slice(0,2),r[o-2][0]=r[o-2][0].actor,r[o-2][1]=r[o-2][1].actor,this.$=[r[o-1],{type:"addNote",placement:i.PLACEMENT.OVER,actor:r[o-2].slice(0,2),text:r[o]}];break;case 57:this.$=[r[o-1],{type:"addLinks",actor:r[o-1].actor,text:r[o]}];break;case 58:this.$=[r[o-1],{type:"addALink",actor:r[o-1].actor,text:r[o]}];break;case 59:this.$=[r[o-1],{type:"addProperties",actor:r[o-1].actor,text:r[o]}];break;case 60:this.$=[r[o-1],{type:"addDetails",actor:r[o-1].actor,text:r[o]}];break;case 63:this.$=[r[o-2],r[o]];break;case 65:this.$=i.PLACEMENT.LEFTOF;break;case 66:this.$=i.PLACEMENT.RIGHTOF;break;case 67:this.$=[r[o-4],r[o-1],{type:"addMessage",from:r[o-4].actor,to:r[o-1].actor,signalType:r[o-3],msg:r[o]},{type:"activeStart",signalType:i.LINETYPE.ACTIVE_START,actor:r[o-1]}];break;case 68:this.$=[r[o-4],r[o-1],{type:"addMessage",from:r[o-4].actor,to:r[o-1].actor,signalType:r[o-3],msg:r[o]},{type:"activeEnd",signalType:i.LINETYPE.ACTIVE_END,actor:r[o-4]}];break;case 69:this.$=[r[o-3],r[o-1],{type:"addMessage",from:r[o-3].actor,to:r[o-1].actor,signalType:r[o-2],msg:r[o]}];break;case 70:this.$={type:"addParticipant",actor:r[o]};break;case 71:this.$=i.LINETYPE.SOLID_OPEN;break;case 72:this.$=i.LINETYPE.DOTTED_OPEN;break;case 73:this.$=i.LINETYPE.SOLID;break;case 74:this.$=i.LINETYPE.DOTTED;break;case 75:this.$=i.LINETYPE.SOLID_CROSS;break;case 76:this.$=i.LINETYPE.DOTTED_CROSS;break;case 77:this.$=i.LINETYPE.SOLID_POINT;break;case 78:this.$=i.LINETYPE.DOTTED_POINT;break;case 79:this.$=i.parseMessage(r[o].trim().substring(1));break;case 80:i.parseDirective("%%{","open_directive");break;case 81:i.parseDirective(r[o],"type_directive");break;case 82:r[o]=r[o].trim().replace(/'/g,'"'),i.parseDirective(r[o],"arg_directive");break;case 83:i.parseDirective("}%%","close_directive","sequence")}},table:[{3:1,4:e,5:a,6:4,7:i,14:6,84:n},{1:[3]},{3:8,4:e,5:a,6:4,7:i,14:6,84:n},{3:9,4:e,5:a,6:4,7:i,14:6,84:n},{3:10,4:e,5:a,6:4,7:i,14:6,84:n},t([1,4,5,19,23,26,28,34,35,36,38,40,41,42,43,44,46,48,49,51,55,57,58,63,64,65,66,74,84],r,{8:11}),{15:12,85:[1,13]},{85:[2,80]},{1:[2,1]},{1:[2,2]},{1:[2,3]},{1:[2,4],4:s,5:o,6:42,9:14,10:16,13:18,14:6,19:c,22:20,23:l,26:h,27:45,28:d,29:24,30:25,31:26,32:27,33:28,34:p,35:u,36:g,38:x,40:y,41:m,42:f,43:b,44:T,46:E,48:_,49:v,51:w,55:P,57:k,58:L,63:I,64:N,65:M,66:A,74:S,84:n},{16:52,17:[1,53],87:O},t([17,87],[2,81]),t(D,[2,6]),{6:42,10:55,13:18,14:6,19:c,22:20,23:l,26:h,27:45,28:d,29:24,30:25,31:26,32:27,33:28,34:p,35:u,36:g,38:x,40:y,41:m,42:f,43:b,44:T,46:E,48:_,49:v,51:w,55:P,57:k,58:L,63:I,64:N,65:M,66:A,74:S,84:n},t(D,[2,8]),t(D,[2,9]),t(D,[2,17]),{20:[1,56]},{5:[1,57]},{5:[1,60],24:[1,58],25:[1,59]},{27:61,74:S},{27:62,74:S},{5:[1,63]},{5:[1,64]},{5:[1,65]},{5:[1,66]},{5:[1,67]},t(D,[2,31]),t(D,[2,32]),{37:[1,68]},{39:[1,69]},t(D,[2,35]),{20:[1,70]},{20:[1,71]},{20:[1,72]},{20:[1,73]},{20:[1,74]},{20:[1,75]},{20:[1,76]},{20:[1,77]},t(D,[2,44]),{27:78,74:S},{27:79,74:S},{71:80,75:[1,81],76:[1,82],77:[1,83],78:[1,84],79:[1,85],80:[1,86],81:[1,87],82:[1,88]},{59:89,61:[1,90],69:[1,91],70:[1,92]},{27:93,74:S},{27:94,74:S},{27:95,74:S},{27:96,74:S},t([5,56,68,75,76,77,78,79,80,81,82,83],[2,70]),{5:[1,97]},{18:98,86:[1,99]},{5:[2,83]},t(D,[2,7]),t(R,[2,10],{11:100}),t(D,[2,19]),{5:[1,102],24:[1,101]},{5:[1,103]},t(D,[2,23]),{5:[1,104]},{5:[1,105]},t(D,[2,26]),t(D,[2,27]),t(D,[2,28]),t(D,[2,29]),t(D,[2,30]),t(D,[2,33]),t(D,[2,34]),t(Y,r,{8:106}),t(Y,r,{8:107}),t(Y,r,{8:108}),t($,r,{45:109,8:110}),t(C,r,{47:111,8:112}),t(C,r,{8:112,47:113}),t(B,r,{50:114,8:115}),t(Y,r,{8:116}),{5:[1,118],56:[1,117]},{5:[1,120],56:[1,119]},{27:123,72:[1,121],73:[1,122],74:S},t(V,[2,71]),t(V,[2,72]),t(V,[2,73]),t(V,[2,74]),t(V,[2,75]),t(V,[2,76]),t(V,[2,77]),t(V,[2,78]),{27:124,74:S},{27:126,62:125,74:S},{74:[2,65]},{74:[2,66]},{60:127,83:F},{60:129,83:F},{60:130,83:F},{60:131,83:F},t(W,[2,15]),{16:132,87:O},{87:[2,82]},{4:[1,135],5:[1,137],12:134,13:136,21:[1,133],55:P,57:k},{5:[1,138]},t(D,[2,21]),t(D,[2,22]),t(D,[2,24]),t(D,[2,25]),{4:s,5:o,6:42,9:14,10:16,13:18,14:6,19:c,21:[1,139],22:20,23:l,26:h,27:45,28:d,29:24,30:25,31:26,32:27,33:28,34:p,35:u,36:g,38:x,40:y,41:m,42:f,43:b,44:T,46:E,48:_,49:v,51:w,55:P,57:k,58:L,63:I,64:N,65:M,66:A,74:S,84:n},{4:s,5:o,6:42,9:14,10:16,13:18,14:6,19:c,21:[1,140],22:20,23:l,26:h,27:45,28:d,29:24,30:25,31:26,32:27,33:28,34:p,35:u,36:g,38:x,40:y,41:m,42:f,43:b,44:T,46:E,48:_,49:v,51:w,55:P,57:k,58:L,63:I,64:N,65:M,66:A,74:S,84:n},{4:s,5:o,6:42,9:14,10:16,13:18,14:6,19:c,21:[1,141],22:20,23:l,26:h,27:45,28:d,29:24,30:25,31:26,32:27,33:28,34:p,35:u,36:g,38:x,40:y,41:m,42:f,43:b,44:T,46:E,48:_,49:v,51:w,55:P,57:k,58:L,63:I,64:N,65:M,66:A,74:S,84:n},{21:[1,142]},{4:s,5:o,6:42,9:14,10:16,13:18,14:6,19:c,21:[2,49],22:20,23:l,26:h,27:45,28:d,29:24,30:25,31:26,32:27,33:28,34:p,35:u,36:g,38:x,40:y,41:m,42:f,43:b,44:T,46:E,48:_,49:v,51:w,54:[1,143],55:P,57:k,58:L,63:I,64:N,65:M,66:A,74:S,84:n},{21:[1,144]},{4:s,5:o,6:42,9:14,10:16,13:18,14:6,19:c,21:[2,47],22:20,23:l,26:h,27:45,28:d,29:24,30:25,31:26,32:27,33:28,34:p,35:u,36:g,38:x,40:y,41:m,42:f,43:b,44:T,46:E,48:_,49:v,51:w,53:[1,145],55:P,57:k,58:L,63:I,64:N,65:M,66:A,74:S,84:n},{21:[1,146]},{21:[1,147]},{4:s,5:o,6:42,9:14,10:16,13:18,14:6,19:c,21:[2,45],22:20,23:l,26:h,27:45,28:d,29:24,30:25,31:26,32:27,33:28,34:p,35:u,36:g,38:x,40:y,41:m,42:f,43:b,44:T,46:E,48:_,49:v,51:w,52:[1,148],55:P,57:k,58:L,63:I,64:N,65:M,66:A,74:S,84:n},{4:s,5:o,6:42,9:14,10:16,13:18,14:6,19:c,21:[1,149],22:20,23:l,26:h,27:45,28:d,29:24,30:25,31:26,32:27,33:28,34:p,35:u,36:g,38:x,40:y,41:m,42:f,43:b,44:T,46:E,48:_,49:v,51:w,55:P,57:k,58:L,63:I,64:N,65:M,66:A,74:S,84:n},{20:[1,150]},t(D,[2,52]),{20:[1,151]},t(D,[2,54]),{27:152,74:S},{27:153,74:S},{60:154,83:F},{60:155,83:F},{60:156,83:F},{68:[1,157],83:[2,64]},{5:[2,57]},{5:[2,79]},{5:[2,58]},{5:[2,59]},{5:[2,60]},{5:[1,158]},t(D,[2,18]),t(R,[2,11]),{13:159,55:P,57:k},t(R,[2,13]),t(R,[2,14]),t(D,[2,20]),t(D,[2,36]),t(D,[2,37]),t(D,[2,38]),t(D,[2,39]),{20:[1,160]},t(D,[2,40]),{20:[1,161]},t(D,[2,41]),t(D,[2,42]),{20:[1,162]},t(D,[2,43]),{5:[1,163]},{5:[1,164]},{60:165,83:F},{60:166,83:F},{5:[2,69]},{5:[2,55]},{5:[2,56]},{27:167,74:S},t(W,[2,16]),t(R,[2,12]),t($,r,{8:110,45:168}),t(C,r,{8:112,47:169}),t(B,r,{8:115,50:170}),t(D,[2,51]),t(D,[2,53]),{5:[2,67]},{5:[2,68]},{83:[2,63]},{21:[2,50]},{21:[2,48]},{21:[2,46]}],defaultActions:{7:[2,80],8:[2,1],9:[2,2],10:[2,3],54:[2,83],91:[2,65],92:[2,66],99:[2,82],127:[2,57],128:[2,79],129:[2,58],130:[2,59],131:[2,60],154:[2,69],155:[2,55],156:[2,56],165:[2,67],166:[2,68],167:[2,63],168:[2,50],169:[2,48],170:[2,46]},parseError:function(t,e){if(!e.recoverable){var a=new Error(t);throw a.hash=e,a}this.trace(t)},parse:function(t){var e=[0],a=[],i=[null],n=[],r=this.table,s="",o=0,c=0,l=n.slice.call(arguments,1),h=Object.create(this.lexer),d={yy:{}};for(var p in this.yy)Object.prototype.hasOwnProperty.call(this.yy,p)&&(d.yy[p]=this.yy[p]);h.setInput(t,d.yy),d.yy.lexer=h,d.yy.parser=this,void 0===h.yylloc&&(h.yylloc={});var u=h.yylloc;n.push(u);var g=h.options&&h.options.ranges;"function"==typeof d.yy.parseError?this.parseError=d.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;for(var x,y,m,f,b,T,E,_,v,w={};;){if(y=e[e.length-1],this.defaultActions[y]?m=this.defaultActions[y]:(null==x&&(v=void 0,"number"!=typeof(v=a.pop()||h.lex()||1)&&(v instanceof Array&&(v=(a=v).pop()),v=this.symbols_[v]||v),x=v),m=r[y]&&r[y][x]),void 0===m||!m.length||!m[0]){var P;for(b in _=[],r[y])this.terminals_[b]&&b>2&&_.push("'"+this.terminals_[b]+"'");P=h.showPosition?"Parse error on line "+(o+1)+":\n"+h.showPosition()+"\nExpecting "+_.join(", ")+", got '"+(this.terminals_[x]||x)+"'":"Parse error on line "+(o+1)+": Unexpected "+(1==x?"end of input":"'"+(this.terminals_[x]||x)+"'"),this.parseError(P,{text:h.match,token:this.terminals_[x]||x,line:h.yylineno,loc:u,expected:_})}if(m[0]instanceof Array&&m.length>1)throw new Error("Parse Error: multiple actions possible at state: "+y+", token: "+x);switch(m[0]){case 1:e.push(x),i.push(h.yytext),n.push(h.yylloc),e.push(m[1]),x=null,c=h.yyleng,s=h.yytext,o=h.yylineno,u=h.yylloc;break;case 2:if(T=this.productions_[m[1]][1],w.$=i[i.length-T],w._$={first_line:n[n.length-(T||1)].first_line,last_line:n[n.length-1].last_line,first_column:n[n.length-(T||1)].first_column,last_column:n[n.length-1].last_column},g&&(w._$.range=[n[n.length-(T||1)].range[0],n[n.length-1].range[1]]),void 0!==(f=this.performAction.apply(w,[s,c,o,d.yy,m[1],i,n].concat(l))))return f;T&&(e=e.slice(0,-1*T*2),i=i.slice(0,-1*T),n=n.slice(0,-1*T)),e.push(this.productions_[m[1]][0]),i.push(w.$),n.push(w._$),E=r[e[e.length-2]][e[e.length-1]],e.push(E);break;case 3:return!0}}return!0}},z={EOF:1,parseError:function(t,e){if(!this.yy.parser)throw new Error(t);this.yy.parser.parseError(t,e)},setInput:function(t,e){return this.yy=e||this.yy||{},this._input=t,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t},unput:function(t){var e=t.length,a=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),a.length-1&&(this.yylineno-=a.length-1);var n=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===i.length?this.yylloc.first_column:0)+i[i.length-a.length].length-a[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[n[0],n[0]+this.yyleng-e]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},less:function(t){this.unput(this.match.slice(t))},pastInput:function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var t=this.pastInput(),e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},test_match:function(t,e){var a,i,n;if(this.options.backtrack_lexer&&(n={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(n.yylloc.range=this.yylloc.range.slice(0))),(i=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=i.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],a=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),a)return a;if(this._backtrack){for(var r in n)this[r]=n[r];return!1}return!1},next:function(){if(this.done)return this.EOF;var t,e,a,i;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var n=this._currentRules(),r=0;r<n.length;r++)if((a=this._input.match(this.rules[n[r]]))&&(!e||a[0].length>e[0].length)){if(e=a,i=r,this.options.backtrack_lexer){if(!1!==(t=this.test_match(a,n[r])))return t;if(this._backtrack){e=!1;continue}return!1}if(!this.options.flex)break}return e?!1!==(t=this.test_match(e,n[i]))&&t:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){return this.next()||this.lex()},begin:function(t){this.conditionStack.push(t)},popState:function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"},pushState:function(t){this.begin(t)},stateStackSize:function(){return this.conditionStack.length},options:{"case-insensitive":!0},performAction:function(t,e,a,i){switch(a){case 0:return this.begin("open_directive"),84;case 1:return this.begin("type_directive"),85;case 2:return this.popState(),this.begin("arg_directive"),17;case 3:return this.popState(),this.popState(),87;case 4:return 86;case 5:case 54:case 67:return 5;case 6:case 7:case 8:case 9:case 10:break;case 11:return 24;case 12:return this.begin("LINE"),19;case 13:return this.begin("ID"),55;case 14:return this.begin("ID"),57;case 15:return e.yytext=e.yytext.trim(),this.begin("ALIAS"),74;case 16:return this.popState(),this.popState(),this.begin("LINE"),56;case 17:return this.popState(),this.popState(),5;case 18:return this.begin("LINE"),41;case 19:return this.begin("LINE"),42;case 20:return this.begin("LINE"),43;case 21:return this.begin("LINE"),44;case 22:return this.begin("LINE"),54;case 23:return this.begin("LINE"),46;case 24:return this.begin("LINE"),48;case 25:return this.begin("LINE"),53;case 26:return this.begin("LINE"),49;case 27:return this.begin("LINE"),52;case 28:return this.begin("LINE"),51;case 29:return this.popState(),20;case 30:return 21;case 31:return 69;case 32:return 70;case 33:return 63;case 34:return 64;case 35:return 65;case 36:return 66;case 37:return 61;case 38:return 58;case 39:return this.begin("ID"),26;case 40:return this.begin("ID"),28;case 41:return 34;case 42:return 35;case 43:return this.begin("acc_title"),36;case 44:return this.popState(),"acc_title_value";case 45:return this.begin("acc_descr"),38;case 46:return this.popState(),"acc_descr_value";case 47:this.begin("acc_descr_multiline");break;case 48:this.popState();break;case 49:return"acc_descr_multiline_value";case 50:return 7;case 51:return 23;case 52:return 25;case 53:return 68;case 55:return e.yytext=e.yytext.trim(),74;case 56:return 77;case 57:return 78;case 58:return 75;case 59:return 76;case 60:return 79;case 61:return 80;case 62:return 81;case 63:return 82;case 64:return 83;case 65:return 72;case 66:return 73;case 68:return"INVALID"}},rules:[/^(?:%%\{)/i,/^(?:((?:(?!\}%%)[^:.])*))/i,/^(?::)/i,/^(?:\}%%)/i,/^(?:((?:(?!\}%%).|\n)*))/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:[^\->:\n,;]+?([\-]*[^\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[48,49],inclusive:!1},acc_descr:{rules:[46],inclusive:!1},acc_title:{rules:[44],inclusive:!1},open_directive:{rules:[1,8],inclusive:!1},type_directive:{rules:[2,3,8],inclusive:!1},arg_directive:{rules:[3,4,8],inclusive:!1},ID:{rules:[7,8,15],inclusive:!1},ALIAS:{rules:[7,8,16,17],inclusive:!1},LINE:{rules:[7,8,29],inclusive:!1},INITIAL:{rules:[0,5,6,8,9,10,11,12,13,14,18,19,20,21,22,23,24,25,26,27,28,30,31,32,33,34,35,36,37,38,39,40,41,42,43,45,47,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68],inclusive:!0}}};function H(){this.yy={}}return q.lexer=z,H.prototype=q,q.Parser=H,new H}());o.parser=o;const c=o;let l,h,d,p={},u=[],g=[],x=!1;const y=function(t,e,a,i){let n=d;const r=p[t];if(r){if(d&&r.box&&d!==r.box)throw new Error("A same participant should only be defined in one Box: "+r.name+" can't be in '"+r.box.name+"' and in '"+d.name+"' at the same time.");if(n=r.box?r.box:d,r.box=n,r&&e===r.name&&null==a)return}null!=a&&null!=a.text||(a={text:e,wrap:null,type:i}),null!=i&&null!=a.text||(a={text:e,wrap:null,type:i}),p[t]={box:n,name:e,description:a.text,wrap:void 0===a.wrap&&b()||!!a.wrap,prevActor:l,links:{},properties:{},actorCnt:null,rectData:null,type:i||"participant"},l&&p[l]&&(p[l].nextActor=t),d&&d.actorKeys.push(t),l=t},m=function(t,e,a={text:void 0,wrap:void 0},i){if(i===T.ACTIVE_END&&(t=>{let e,a=0;for(e=0;e<g.length;e++)g[e].type===T.ACTIVE_START&&g[e].from.actor===t&&a++,g[e].type===T.ACTIVE_END&&g[e].from.actor===t&&a--;return a})(t.actor)<1){let e=new Error("Trying to inactivate an inactive participant ("+t.actor+")");throw e.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]},e}return g.push({from:t,to:e,message:a.text,wrap:void 0===a.wrap&&b()||!!a.wrap,type:i}),!0},f=function(t){return p[t]},b=()=>void 0!==h?h:(0,i.c)().sequence.wrap,T={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32},E=function(t,e,a){a.text,void 0===a.wrap&&b()||a.wrap;const i=[].concat(t,t);g.push({from:i[0],to:i[1],message:a.text,wrap:void 0===a.wrap&&b()||!!a.wrap,type:T.NOTE,placement:e})},_=function(t,e){const a=f(t);try{let t=(0,i.d)(e.text,(0,i.c)());t=t.replace(/&amp;/g,"&"),t=t.replace(/&equals;/g,"="),v(a,JSON.parse(t))}catch(t){i.l.error("error while parsing actor link text",t)}};function v(t,e){if(null==t.links)t.links=e;else for(let a in e)t.links[a]=e[a]}const w=function(t,e){const a=f(t);try{let t=(0,i.d)(e.text,(0,i.c)());P(a,JSON.parse(t))}catch(t){i.l.error("error while parsing actor properties text",t)}};function P(t,e){if(null==t.properties)t.properties=e;else for(let a in e)t.properties[a]=e[a]}const k=function(t,e){const a=f(t),n=document.getElementById(e.text);try{const t=n.innerHTML,e=JSON.parse(t);e.properties&&P(a,e.properties),e.links&&v(a,e.links)}catch(t){i.l.error("error while parsing actor details text",t)}},L=function(t){if(Array.isArray(t))t.forEach((function(t){L(t)}));else switch(t.type){case"sequenceIndex":g.push({from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:!1,type:t.signalType});break;case"addParticipant":y(t.actor,t.actor,t.description,"participant");break;case"addActor":y(t.actor,t.actor,t.description,"actor");break;case"activeStart":case"activeEnd":m(t.actor,void 0,void 0,t.signalType);break;case"addNote":E(t.actor,t.placement,t.text);break;case"addLinks":_(t.actor,t.text);break;case"addALink":!function(t,e){const a=f(t);try{const t={};let o=(0,i.d)(e.text,(0,i.c)());var n=o.indexOf("@");o=o.replace(/&amp;/g,"&"),o=o.replace(/&equals;/g,"=");var r=o.slice(0,n-1).trim(),s=o.slice(n+1).trim();t[r]=s,v(a,t)}catch(t){i.l.error("error while parsing actor link text",t)}}(t.actor,t.text);break;case"addProperties":w(t.actor,t.text);break;case"addDetails":k(t.actor,t.text);break;case"addMessage":m(t.from,t.to,t.msg,t.signalType);break;case"boxStart":e=t.boxData,u.push({name:e.text,wrap:void 0===e.wrap&&b()||!!e.wrap,fill:e.color,actorKeys:[]}),d=u.slice(-1)[0];break;case"boxEnd":d=void 0;break;case"loopStart":m(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":case"rectEnd":case"optEnd":case"altEnd":case"parEnd":case"criticalEnd":case"breakEnd":m(void 0,void 0,void 0,t.signalType);break;case"rectStart":m(void 0,void 0,t.color,t.signalType);break;case"optStart":m(void 0,void 0,t.optText,t.signalType);break;case"altStart":case"else":m(void 0,void 0,t.altText,t.signalType);break;case"setAccTitle":(0,i.s)(t.text);break;case"parStart":case"and":m(void 0,void 0,t.parText,t.signalType);break;case"criticalStart":m(void 0,void 0,t.criticalText,t.signalType);break;case"option":m(void 0,void 0,t.optionText,t.signalType);break;case"breakStart":m(void 0,void 0,t.breakText,t.signalType)}var e},I={addActor:y,addMessage:function(t,e,a,i){g.push({from:t,to:e,message:a.text,wrap:void 0===a.wrap&&b()||!!a.wrap,answer:i})},addSignal:m,addLinks:_,addDetails:k,addProperties:w,autoWrap:b,setWrap:function(t){h=t},enableSequenceNumbers:function(){x=!0},disableSequenceNumbers:function(){x=!1},showSequenceNumbers:()=>x,getMessages:function(){return g},getActors:function(){return p},getActor:f,getActorKeys:function(){return Object.keys(p)},getActorProperty:function(t,e){if(void 0!==t&&void 0!==t.properties)return t.properties[e]},getAccTitle:i.g,getBoxes:function(){return u},getDiagramTitle:i.t,setDiagramTitle:i.r,parseDirective:function(t,e,a){i.m.parseDirective(this,t,e,a)},getConfig:()=>(0,i.c)().sequence,clear:function(){p={},u=[],g=[],x=!1,(0,i.v)()},parseMessage:function(t){const e=t.trim(),a={text:e.replace(/^:?(?:no)?wrap:/,"").trim(),wrap:null!==e.match(/^:?wrap:/)||null===e.match(/^:?nowrap:/)&&void 0};return i.l.debug("parseMessage:",a),a},parseBoxData:function(t){const e=t.match(/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/);let a=null!=e&&e[1]?e[1].trim():"transparent",n=null!=e&&e[2]?e[2].trim():void 0;if(window&&window.CSS)window.CSS.supports("color",a)||(a="transparent",n=t.trim());else{const e=(new Option).style;e.color=a,e.color!==a&&(a="transparent",n=t.trim())}return{color:a,text:void 0!==n?(0,i.d)(n.replace(/^:?(?:no)?wrap:/,""),(0,i.c)()):void 0,wrap:void 0!==n?null!==n.match(/^:?wrap:/)||null===n.match(/^:?nowrap:/)&&void 0:void 0}},LINETYPE:T,ARROWTYPE:{FILLED:0,OPEN:1},PLACEMENT:{LEFTOF:0,RIGHTOF:1,OVER:2},addNote:E,setAccTitle:i.s,apply:L,setAccDescription:i.b,getAccDescription:i.a,hasAtLeastOneBox:function(){return u.length>0},hasAtLeastOneBoxWithTitle:function(){return u.some((t=>t.name))}},N=function(t,e){return(0,r.d)(t,e)},M=(t,e)=>{(0,i.E)((()=>{const a=document.querySelectorAll(t);0!==a.length&&(a[0].addEventListener("mouseover",(function(){A("actor"+e+"_popup")})),a[0].addEventListener("mouseout",(function(){S("actor"+e+"_popup")})))}))},A=function(t){var e=document.getElementById(t);null!=e&&(e.style.display="block")},S=function(t){var e=document.getElementById(t);null!=e&&(e.style.display="none")},O=function(t,e){let a=0,n=0;const r=e.text.split(i.e.lineBreakRegex),[s,o]=(0,i.B)(e.fontSize);let c=[],l=0,h=()=>e.y;if(void 0!==e.valign&&void 0!==e.textMargin&&e.textMargin>0)switch(e.valign){case"top":case"start":h=()=>Math.round(e.y+e.textMargin);break;case"middle":case"center":h=()=>Math.round(e.y+(a+n+e.textMargin)/2);break;case"bottom":case"end":h=()=>Math.round(e.y+(a+n+2*e.textMargin)-e.textMargin)}if(void 0!==e.anchor&&void 0!==e.textMargin&&void 0!==e.width)switch(e.anchor){case"left":case"start":e.x=Math.round(e.x+e.textMargin),e.anchor="start",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"middle":case"center":e.x=Math.round(e.x+e.width/2),e.anchor="middle",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"right":case"end":e.x=Math.round(e.x+e.width-e.textMargin),e.anchor="end",e.dominantBaseline="middle",e.alignmentBaseline="middle"}for(let[d,p]of r.entries()){void 0!==e.textMargin&&0===e.textMargin&&void 0!==s&&(l=d*s);const r=t.append("text");r.attr("x",e.x),r.attr("y",h()),void 0!==e.anchor&&r.attr("text-anchor",e.anchor).attr("dominant-baseline",e.dominantBaseline).attr("alignment-baseline",e.alignmentBaseline),void 0!==e.fontFamily&&r.style("font-family",e.fontFamily),void 0!==o&&r.style("font-size",o),void 0!==e.fontWeight&&r.style("font-weight",e.fontWeight),void 0!==e.fill&&r.attr("fill",e.fill),void 0!==e.class&&r.attr("class",e.class),void 0!==e.dy?r.attr("dy",e.dy):0!==l&&r.attr("dy",l);const u=p||i.Z;if(e.tspan){const t=r.append("tspan");t.attr("x",e.x),void 0!==e.fill&&t.attr("fill",e.fill),t.text(u)}else r.text(u);void 0!==e.valign&&void 0!==e.textMargin&&e.textMargin>0&&(n+=(r._groups||r)[0][0].getBBox().height,a=n),c.push(r)}return c},D=function(t,e){const a=t.append("polygon");var i,n,r,s;return a.attr("points",(i=e.x)+","+(n=e.y)+" "+(i+(r=e.width))+","+n+" "+(i+r)+","+(n+(s=e.height)-7)+" "+(i+r-8.4)+","+(n+s)+" "+i+","+(n+s)),a.attr("class","labelBox"),e.y=e.y+e.height/2,O(t,e),a};let R=-1;const Y=(t,e)=>{t.selectAll&&t.selectAll(".actor-line").attr("class","200").attr("y2",e-55)},$=function(t,e){(0,r.a)(t,e)},C=function(){function t(t,e,a,i,r,s,o){n(e.append("text").attr("x",a+r/2).attr("y",i+s/2+5).style("text-anchor","middle").text(t),o)}function e(t,e,a,r,s,o,c,l){const{actorFontSize:h,actorFontFamily:d,actorFontWeight:p}=l,[u,g]=(0,i.B)(h),x=t.split(i.e.lineBreakRegex);for(let t=0;t<x.length;t++){const i=t*u-u*(x.length-1)/2,l=e.append("text").attr("x",a+s/2).attr("y",r).style("text-anchor","middle").style("font-size",g).style("font-weight",p).style("font-family",d);l.append("tspan").attr("x",a+s/2).attr("dy",i).text(x[t]),l.attr("y",r+o/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),n(l,c)}}function a(t,a,i,r,s,o,c,l){const h=a.append("switch"),d=h.append("foreignObject").attr("x",i).attr("y",r).attr("width",s).attr("height",o).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");d.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t),e(t,h,i,r,s,o,c,l),n(d,c)}function n(t,e){for(const a in e)e.hasOwnProperty(a)&&t.attr(a,e[a])}return function(i){return"fo"===i.textPlacement?a:"old"===i.textPlacement?t:e}}(),B=function(){function t(t,e,a,i,r,s,o){n(e.append("text").attr("x",a).attr("y",i).style("text-anchor","start").text(t),o)}function e(t,e,a,r,s,o,c,l){const{actorFontSize:h,actorFontFamily:d,actorFontWeight:p}=l,u=t.split(i.e.lineBreakRegex);for(let t=0;t<u.length;t++){const i=t*h-h*(u.length-1)/2,s=e.append("text").attr("x",a).attr("y",r).style("text-anchor","start").style("font-size",h).style("font-weight",p).style("font-family",d);s.append("tspan").attr("x",a).attr("dy",i).text(u[t]),s.attr("y",r+o/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),n(s,c)}}function a(t,a,i,r,s,o,c,l){const h=a.append("switch"),d=h.append("foreignObject").attr("x",i).attr("y",r).attr("width",s).attr("height",o).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");d.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t),e(t,h,i,r,0,o,c,l),n(d,c)}function n(t,e){for(const a in e)e.hasOwnProperty(a)&&t.attr(a,e[a])}return function(i){return"fo"===i.textPlacement?a:"old"===i.textPlacement?t:e}}(),V=N,F=function(t,e,a,i){switch(e.type){case"actor":return function(t,e,a,i){const n=e.x+e.width/2,s=e.y+80;i||(R++,t.append("line").attr("id","actor"+R).attr("x1",n).attr("y1",s).attr("x2",n).attr("y2",2e3).attr("class","actor-line").attr("stroke-width","0.5px").attr("stroke","#999"));const o=t.append("g");o.attr("class","actor-man");const c=(0,r.g)();c.x=e.x,c.y=e.y,c.fill="#eaeaea",c.width=e.width,c.height=e.height,c.class="actor",c.rx=3,c.ry=3,o.append("line").attr("id","actor-man-torso"+R).attr("x1",n).attr("y1",e.y+25).attr("x2",n).attr("y2",e.y+45),o.append("line").attr("id","actor-man-arms"+R).attr("x1",n-18).attr("y1",e.y+33).attr("x2",n+18).attr("y2",e.y+33),o.append("line").attr("x1",n-18).attr("y1",e.y+60).attr("x2",n).attr("y2",e.y+45),o.append("line").attr("x1",n).attr("y1",e.y+45).attr("x2",n+16).attr("y2",e.y+60);const l=o.append("circle");l.attr("cx",e.x+e.width/2),l.attr("cy",e.y+10),l.attr("r",15),l.attr("width",e.width),l.attr("height",e.height);const h=o.node().getBBox();return e.height=h.height,C(a)(e.description,o,c.x,c.y+35,c.width,c.height,{class:"actor"},a),e.height}(t,e,a,i);case"participant":return function(t,e,a,i){const n=e.x+e.width/2,s=e.y+5,o=t.append("g");var c=o;i||(R++,c.append("line").attr("id","actor"+R).attr("x1",n).attr("y1",s).attr("x2",n).attr("y2",2e3).attr("class","actor-line").attr("stroke-width","0.5px").attr("stroke","#999"),c=o.append("g"),e.actorCnt=R,null!=e.links&&(c.attr("id","root-"+R),M("#root-"+R,R)));const l=(0,r.g)();var h="actor";null!=e.properties&&e.properties.class?h=e.properties.class:l.fill="#eaeaea",l.x=e.x,l.y=e.y,l.width=e.width,l.height=e.height,l.class=h,l.rx=3,l.ry=3;const d=N(c,l);if(e.rectData=l,null!=e.properties&&e.properties.icon){const t=e.properties.icon.trim();"@"===t.charAt(0)?(0,r.b)(c,l.x+l.width-20,l.y+10,t.substr(1)):(0,r.c)(c,l.x+l.width-20,l.y+10,t)}C(a)(e.description,c,l.x,l.y,l.width,l.height,{class:"actor"},a);let p=e.height;if(d.node){const t=d.node().getBBox();e.height=t.height,p=t.height}return p}(t,e,a,i)}},W=function(t,e,a){const i=t.append("g");$(i,e),e.name&&C(a)(e.name,i,e.x,e.y+(e.textMaxHeight||0)/2,e.width,0,{class:"text"},a),i.lower()},q=function(t,e,a,i,n){if(void 0===e.links||null===e.links||0===Object.keys(e.links).length)return{height:0,width:0};const r=e.links,o=e.actorCnt,c=e.rectData;var l="none";n&&(l="block !important");const h=t.append("g");h.attr("id","actor"+o+"_popup"),h.attr("class","actorPopupMenu"),h.attr("display",l),M("#actor"+o+"_popup",o);var d="";void 0!==c.class&&(d=" "+c.class);let p=c.width>a?c.width:a;const u=h.append("rect");if(u.attr("class","actorPopupMenuPanel"+d),u.attr("x",c.x),u.attr("y",c.height),u.attr("fill",c.fill),u.attr("stroke",c.stroke),u.attr("width",p),u.attr("height",c.height),u.attr("rx",c.rx),u.attr("ry",c.ry),null!=r){var g=20;for(let t in r){var x=h.append("a"),y=(0,s.N)(r[t]);x.attr("xlink:href",y),x.attr("target","_blank"),B(i)(t,x,c.x+10,c.height+g,p,20,{class:"actor"},i),g+=30}}return u.attr("height",g),{height:c.height+g,width:p}},z=function(t){return t.append("g")},H=function(t,e,a,i,n){const s=(0,r.g)(),o=e.anchored;s.x=e.startx,s.y=e.starty,s.class="activation"+n%3,s.width=e.stopx-e.startx,s.height=a-e.starty,N(o,s)},U=function(t,e,a,i){const{boxMargin:n,boxTextMargin:s,labelBoxHeight:o,labelBoxWidth:c,messageFontFamily:l,messageFontSize:h,messageFontWeight:d}=i,p=t.append("g"),u=function(t,e,a,i){return p.append("line").attr("x1",t).attr("y1",e).attr("x2",a).attr("y2",i).attr("class","loopLine")};u(e.startx,e.starty,e.stopx,e.starty),u(e.stopx,e.starty,e.stopx,e.stopy),u(e.startx,e.stopy,e.stopx,e.stopy),u(e.startx,e.starty,e.startx,e.stopy),void 0!==e.sections&&e.sections.forEach((function(t){u(e.startx,t.y,e.stopx,t.y).style("stroke-dasharray","3, 3")}));let g=(0,r.e)();g.text=a,g.x=e.startx,g.y=e.starty,g.fontFamily=l,g.fontSize=h,g.fontWeight=d,g.anchor="middle",g.valign="middle",g.tspan=!1,g.width=c||50,g.height=o||20,g.textMargin=s,g.class="labelText",D(p,g),g={x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0},g.text=e.title,g.x=e.startx+c/2+(e.stopx-e.startx)/2,g.y=e.starty+n+s,g.anchor="middle",g.valign="middle",g.textMargin=s,g.class="loopText",g.fontFamily=l,g.fontSize=h,g.fontWeight=d,g.wrap=!0;let x=O(p,g);return void 0!==e.sectionTitles&&e.sectionTitles.forEach((function(t,a){if(t.message){g.text=t.message,g.x=e.startx+(e.stopx-e.startx)/2,g.y=e.sections[a].y+n+s,g.class="loopText",g.anchor="middle",g.valign="middle",g.tspan=!1,g.fontFamily=l,g.fontSize=h,g.fontWeight=d,g.wrap=e.wrap,x=O(p,g);let i=Math.round(x.map((t=>(t._groups||t)[0][0].getBBox().height)).reduce(((t,e)=>t+e)));e.sections[a].height+=i-(n+s)}})),e.height=Math.round(e.stopy-e.starty),p},j=$,K=function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z")},X=function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",18).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},G=function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},J=function(t){t.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",5).append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")},Z=function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},Q=function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},tt=function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")};s.N;let et={};const at={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:function(){return Math.max.apply(null,0===this.actors.length?[0]:this.actors.map((t=>t.height||0)))+(0===this.loops.length?0:this.loops.map((t=>t.height||0)).reduce(((t,e)=>t+e)))+(0===this.messages.length?0:this.messages.map((t=>t.height||0)).reduce(((t,e)=>t+e)))+(0===this.notes.length?0:this.notes.map((t=>t.height||0)).reduce(((t,e)=>t+e)))},clear:function(){this.actors=[],this.boxes=[],this.loops=[],this.messages=[],this.notes=[]},addBox:function(t){this.boxes.push(t)},addActor:function(t){this.actors.push(t)},addLoop:function(t){this.loops.push(t)},addMessage:function(t){this.messages.push(t)},addNote:function(t){this.notes.push(t)},lastActor:function(){return this.actors[this.actors.length-1]},lastLoop:function(){return this.loops[this.loops.length-1]},lastMessage:function(){return this.messages[this.messages.length-1]},lastNote:function(){return this.notes[this.notes.length-1]},actors:[],boxes:[],loops:[],messages:[],notes:[]},init:function(){this.sequenceItems=[],this.activations=[],this.models.clear(),this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},this.verticalPos=0,ct((0,i.c)())},updateVal:function(t,e,a,i){void 0===t[e]?t[e]=a:t[e]=i(a,t[e])},updateBounds:function(t,e,a,i){const n=this;let r=0;function s(s){return function(o){r++;const c=n.sequenceItems.length-r+1;n.updateVal(o,"starty",e-c*et.boxMargin,Math.min),n.updateVal(o,"stopy",i+c*et.boxMargin,Math.max),n.updateVal(at.data,"startx",t-c*et.boxMargin,Math.min),n.updateVal(at.data,"stopx",a+c*et.boxMargin,Math.max),"activation"!==s&&(n.updateVal(o,"startx",t-c*et.boxMargin,Math.min),n.updateVal(o,"stopx",a+c*et.boxMargin,Math.max),n.updateVal(at.data,"starty",e-c*et.boxMargin,Math.min),n.updateVal(at.data,"stopy",i+c*et.boxMargin,Math.max))}}this.sequenceItems.forEach(s()),this.activations.forEach(s("activation"))},insert:function(t,e,a,n){const r=i.e.getMin(t,a),s=i.e.getMax(t,a),o=i.e.getMin(e,n),c=i.e.getMax(e,n);this.updateVal(at.data,"startx",r,Math.min),this.updateVal(at.data,"starty",o,Math.min),this.updateVal(at.data,"stopx",s,Math.max),this.updateVal(at.data,"stopy",c,Math.max),this.updateBounds(r,o,s,c)},newActivation:function(t,e,a){const i=a[t.from.actor],n=lt(t.from.actor).length||0,r=i.x+i.width/2+(n-1)*et.activationWidth/2;this.activations.push({startx:r,starty:this.verticalPos+2,stopx:r+et.activationWidth,stopy:void 0,actor:t.from.actor,anchored:z(e)})},endActivation:function(t){const e=this.activations.map((function(t){return t.actor})).lastIndexOf(t.from.actor);return this.activations.splice(e,1)[0]},createLoop:function(t={message:void 0,wrap:!1,width:void 0},e){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:t.message,wrap:t.wrap,width:t.width,height:0,fill:e}},newLoop:function(t={message:void 0,wrap:!1,width:void 0},e){this.sequenceItems.push(this.createLoop(t,e))},endLoop:function(){return this.sequenceItems.pop()},isLoopOverlap:function(){return!!this.sequenceItems.length&&this.sequenceItems[this.sequenceItems.length-1].overlap},addSectionToLoop:function(t){const e=this.sequenceItems.pop();e.sections=e.sections||[],e.sectionTitles=e.sectionTitles||[],e.sections.push({y:at.getVerticalPos(),height:0}),e.sectionTitles.push(t),this.sequenceItems.push(e)},saveVerticalPos:function(){this.isLoopOverlap()&&(this.savedVerticalPos=this.verticalPos)},resetVerticalPos:function(){this.isLoopOverlap()&&(this.verticalPos=this.savedVerticalPos)},bumpVerticalPos:function(t){this.verticalPos=this.verticalPos+t,this.data.stopy=i.e.getMax(this.data.stopy,this.verticalPos)},getVerticalPos:function(){return this.verticalPos},getBounds:function(){return{bounds:this.data,models:this.models}}},it=t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight}),nt=t=>({fontFamily:t.noteFontFamily,fontSize:t.noteFontSize,fontWeight:t.noteFontWeight}),rt=t=>({fontFamily:t.actorFontFamily,fontSize:t.actorFontSize,fontWeight:t.actorFontWeight}),st=function(t,e,a,n,r,s,o){if(!0===r.hideUnusedParticipants){const t=new Set;s.forEach((e=>{t.add(e.from),t.add(e.to)})),a=a.filter((e=>t.has(e)))}let c,l=0,h=0,d=0;for(const r of a){const a=e[r],s=a.box;c&&c!=s&&(o||at.models.addBox(c),h+=et.boxMargin+c.margin),s&&s!=c&&(o||(s.x=l+h,s.y=n),h+=s.margin),a.width=a.width||et.width,a.height=i.e.getMax(a.height||et.height,et.height),a.margin=a.margin||et.actorMargin,a.x=l+h,a.y=at.getVerticalPos();const p=F(t,a,et,o);d=i.e.getMax(d,p),at.insert(a.x,n,a.x+a.width,a.height),l+=a.width+h,a.box&&(a.box.width=l+s.margin-a.box.x),h=a.margin,c=a.box,at.models.addActor(a)}c&&!o&&at.models.addBox(c),at.bumpVerticalPos(d)},ot=function(t,e,a,i){let n=0,r=0;for(const s of a){const a=e[s],o=pt(a),c=q(t,a,o,et,et.forceMenus,i);c.height>n&&(n=c.height),c.width+a.x>r&&(r=c.width+a.x)}return{maxHeight:n,maxWidth:r}},ct=function(t){(0,i.f)(et,t),t.fontFamily&&(et.actorFontFamily=et.noteFontFamily=et.messageFontFamily=t.fontFamily),t.fontSize&&(et.actorFontSize=et.noteFontSize=et.messageFontSize=t.fontSize),t.fontWeight&&(et.actorFontWeight=et.noteFontWeight=et.messageFontWeight=t.fontWeight)},lt=function(t){return at.activations.filter((function(e){return e.actor===t}))},ht=function(t,e){const a=e[t],n=lt(t);return[n.reduce((function(t,e){return i.e.getMin(t,e.startx)}),a.x+a.width/2),n.reduce((function(t,e){return i.e.getMax(t,e.stopx)}),a.x+a.width/2)]};function dt(t,e,a,n,r){at.bumpVerticalPos(a);let s=n;if(e.id&&e.message&&t[e.id]){const a=t[e.id].width,r=it(et);e.message=i.u.wrapLabel(`[${e.message}]`,a-2*et.wrapPadding,r),e.width=a,e.wrap=!0;const o=i.u.calculateTextDimensions(e.message,r),c=i.e.getMax(o.height,et.labelBoxHeight);s=n+c,i.l.debug(`${c} - ${e.message}`)}r(e),at.bumpVerticalPos(s)}const pt=function(t){let e=0;const a=rt(et);for(const n in t.links){const t=i.u.calculateTextDimensions(n,a).width+2*et.wrapPadding+2*et.boxMargin;e<t&&(e=t)}return e},ut={parser:c,db:I,renderer:{bounds:at,drawActors:st,drawActorsPopup:ot,setConf:ct,draw:function(t,e,a,s){const{securityLevel:o,sequence:c}=(0,i.c)();let l;et=c,s.db.clear(),s.parser.parse(t),"sandbox"===o&&(l=(0,n.Ys)("#i"+e));const h="sandbox"===o?(0,n.Ys)(l.nodes()[0].contentDocument.body):(0,n.Ys)("body"),d="sandbox"===o?l.nodes()[0].contentDocument:document;at.init(),i.l.debug(s.db);const p="sandbox"===o?h.select(`[id="${e}"]`):(0,n.Ys)(`[id="${e}"]`),u=s.db.getActors(),g=s.db.getBoxes(),x=s.db.getActorKeys(),y=s.db.getMessages(),m=s.db.getDiagramTitle(),f=s.db.hasAtLeastOneBox(),b=s.db.hasAtLeastOneBoxWithTitle(),T=function(t,e,a){const n={};return e.forEach((function(e){if(t[e.to]&&t[e.from]){const r=t[e.to];if(e.placement===a.db.PLACEMENT.LEFTOF&&!r.prevActor)return;if(e.placement===a.db.PLACEMENT.RIGHTOF&&!r.nextActor)return;const s=void 0!==e.placement,o=!s,c=s?nt(et):it(et),l=e.wrap?i.u.wrapLabel(e.message,et.width-2*et.wrapPadding,c):e.message,h=i.u.calculateTextDimensions(l,c).width+2*et.wrapPadding;o&&e.from===r.nextActor?n[e.to]=i.e.getMax(n[e.to]||0,h):o&&e.from===r.prevActor?n[e.from]=i.e.getMax(n[e.from]||0,h):o&&e.from===e.to?(n[e.from]=i.e.getMax(n[e.from]||0,h/2),n[e.to]=i.e.getMax(n[e.to]||0,h/2)):e.placement===a.db.PLACEMENT.RIGHTOF?n[e.from]=i.e.getMax(n[e.from]||0,h):e.placement===a.db.PLACEMENT.LEFTOF?n[r.prevActor]=i.e.getMax(n[r.prevActor]||0,h):e.placement===a.db.PLACEMENT.OVER&&(r.prevActor&&(n[r.prevActor]=i.e.getMax(n[r.prevActor]||0,h/2)),r.nextActor&&(n[e.from]=i.e.getMax(n[e.from]||0,h/2)))}})),i.l.debug("maxMessageWidthPerActor:",n),n}(u,y,s);et.height=function(t,e,a){let n=0;Object.keys(t).forEach((e=>{const a=t[e];a.wrap&&(a.description=i.u.wrapLabel(a.description,et.width-2*et.wrapPadding,rt(et)));const r=i.u.calculateTextDimensions(a.description,rt(et));a.width=a.wrap?et.width:i.e.getMax(et.width,r.width+2*et.wrapPadding),a.height=a.wrap?i.e.getMax(r.height,et.height):et.height,n=i.e.getMax(n,a.height)}));for(const a in e){const n=t[a];if(!n)continue;const r=t[n.nextActor];if(!r){const t=e[a]+et.actorMargin-n.width/2;n.margin=i.e.getMax(t,et.actorMargin);continue}const s=e[a]+et.actorMargin-n.width/2-r.width/2;n.margin=i.e.getMax(s,et.actorMargin)}let r=0;return a.forEach((e=>{const a=it(et);let n=e.actorKeys.reduce(((e,a)=>e+(t[a].width+(t[a].margin||0))),0);n-=2*et.boxTextMargin,e.wrap&&(e.name=i.u.wrapLabel(e.name,n-2*et.wrapPadding,a));const s=i.u.calculateTextDimensions(e.name,a);r=i.e.getMax(s.height,r);const o=i.e.getMax(n,s.width+2*et.wrapPadding);if(e.margin=et.boxTextMargin,n<o){const t=(o-n)/2;e.margin+=t}})),a.forEach((t=>t.textMaxHeight=r)),i.e.getMax(n,et.height)}(u,T,g),Q(p),Z(p),tt(p),f&&(at.bumpVerticalPos(et.boxMargin),b&&at.bumpVerticalPos(g[0].textMaxHeight)),st(p,u,x,0,et,y,!1);const E=function(t,e,a,n){const r={},s=[];let o,c,l;return t.forEach((function(t){switch(t.id=i.u.random({length:10}),t.type){case n.db.LINETYPE.LOOP_START:case n.db.LINETYPE.ALT_START:case n.db.LINETYPE.OPT_START:case n.db.LINETYPE.PAR_START:case n.db.LINETYPE.PAR_OVER_START:case n.db.LINETYPE.CRITICAL_START:case n.db.LINETYPE.BREAK_START:s.push({id:t.id,msg:t.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case n.db.LINETYPE.ALT_ELSE:case n.db.LINETYPE.PAR_AND:case n.db.LINETYPE.CRITICAL_OPTION:t.message&&(o=s.pop(),r[o.id]=o,r[t.id]=o,s.push(o));break;case n.db.LINETYPE.LOOP_END:case n.db.LINETYPE.ALT_END:case n.db.LINETYPE.OPT_END:case n.db.LINETYPE.PAR_END:case n.db.LINETYPE.CRITICAL_END:case n.db.LINETYPE.BREAK_END:o=s.pop(),r[o.id]=o;break;case n.db.LINETYPE.ACTIVE_START:{const a=e[t.from?t.from.actor:t.to.actor],i=lt(t.from?t.from.actor:t.to.actor).length,n=a.x+a.width/2+(i-1)*et.activationWidth/2,r={startx:n,stopx:n+et.activationWidth,actor:t.from.actor,enabled:!0};at.activations.push(r)}break;case n.db.LINETYPE.ACTIVE_END:{const e=at.activations.map((t=>t.actor)).lastIndexOf(t.from.actor);delete at.activations.splice(e,1)[0]}}void 0!==t.placement?(c=function(t,e,a){const n=e[t.from].x,r=e[t.to].x,s=t.wrap&&t.message;let o=i.u.calculateTextDimensions(s?i.u.wrapLabel(t.message,et.width,nt(et)):t.message,nt(et));const c={width:s?et.width:i.e.getMax(et.width,o.width+2*et.noteMargin),height:0,startx:e[t.from].x,stopx:0,starty:0,stopy:0,message:t.message};return t.placement===a.db.PLACEMENT.RIGHTOF?(c.width=s?i.e.getMax(et.width,o.width):i.e.getMax(e[t.from].width/2+e[t.to].width/2,o.width+2*et.noteMargin),c.startx=n+(e[t.from].width+et.actorMargin)/2):t.placement===a.db.PLACEMENT.LEFTOF?(c.width=s?i.e.getMax(et.width,o.width+2*et.noteMargin):i.e.getMax(e[t.from].width/2+e[t.to].width/2,o.width+2*et.noteMargin),c.startx=n-c.width+(e[t.from].width-et.actorMargin)/2):t.to===t.from?(o=i.u.calculateTextDimensions(s?i.u.wrapLabel(t.message,i.e.getMax(et.width,e[t.from].width),nt(et)):t.message,nt(et)),c.width=s?i.e.getMax(et.width,e[t.from].width):i.e.getMax(e[t.from].width,et.width,o.width+2*et.noteMargin),c.startx=n+(e[t.from].width-c.width)/2):(c.width=Math.abs(n+e[t.from].width/2-(r+e[t.to].width/2))+et.actorMargin,c.startx=n<r?n+e[t.from].width/2-et.actorMargin/2:r+e[t.to].width/2-et.actorMargin/2),s&&(c.message=i.u.wrapLabel(t.message,c.width-2*et.wrapPadding,nt(et))),i.l.debug(`NM:[${c.startx},${c.stopx},${c.starty},${c.stopy}:${c.width},${c.height}=${t.message}]`),c}(t,e,n),t.noteModel=c,s.forEach((t=>{o=t,o.from=i.e.getMin(o.from,c.startx),o.to=i.e.getMax(o.to,c.startx+c.width),o.width=i.e.getMax(o.width,Math.abs(o.from-o.to))-et.labelBoxWidth}))):(l=function(t,e,a){let n=!1;if([a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN,a.db.LINETYPE.SOLID,a.db.LINETYPE.DOTTED,a.db.LINETYPE.SOLID_CROSS,a.db.LINETYPE.DOTTED_CROSS,a.db.LINETYPE.SOLID_POINT,a.db.LINETYPE.DOTTED_POINT].includes(t.type)&&(n=!0),!n)return{};const r=ht(t.from,e),s=ht(t.to,e),o=r[0]<=s[0]?1:0,c=r[0]<s[0]?0:1,l=[...r,...s],h=Math.abs(s[c]-r[o]);t.wrap&&t.message&&(t.message=i.u.wrapLabel(t.message,i.e.getMax(h+2*et.wrapPadding,et.width),it(et)));const d=i.u.calculateTextDimensions(t.message,it(et));return{width:i.e.getMax(t.wrap?0:d.width+2*et.wrapPadding,h+2*et.wrapPadding,et.width),height:0,startx:r[o],stopx:s[c],starty:0,stopy:0,message:t.message,type:t.type,wrap:t.wrap,fromBounds:Math.min.apply(null,l),toBounds:Math.max.apply(null,l)}}(t,e,n),t.msgModel=l,l.startx&&l.stopx&&s.length>0&&s.forEach((a=>{if(o=a,l.startx===l.stopx){const a=e[t.from],n=e[t.to];o.from=i.e.getMin(a.x-l.width/2,a.x-a.width/2,o.from),o.to=i.e.getMax(n.x+l.width/2,n.x+a.width/2,o.to),o.width=i.e.getMax(o.width,Math.abs(o.to-o.from))-et.labelBoxWidth}else o.from=i.e.getMin(l.startx,o.from),o.to=i.e.getMax(l.stopx,o.to),o.width=i.e.getMax(o.width,l.width)-et.labelBoxWidth})))})),at.activations=[],i.l.debug("Loop type widths:",r),r}(y,u,0,s);K(p),J(p),X(p),G(p);let _=1,v=1;const w=[];y.forEach((function(t){let e,a,n;switch(t.type){case s.db.LINETYPE.NOTE:at.resetVerticalPos(),a=t.noteModel,function(t,e){at.bumpVerticalPos(et.boxMargin),e.height=et.boxMargin,e.starty=at.getVerticalPos();const a=(0,r.g)();a.x=e.startx,a.y=e.starty,a.width=e.width||et.width,a.class="note";const i=t.append("g"),n=V(i,a),s=(0,r.e)();s.x=e.startx,s.y=e.starty,s.width=a.width,s.dy="1em",s.text=e.message,s.class="noteText",s.fontFamily=et.noteFontFamily,s.fontSize=et.noteFontSize,s.fontWeight=et.noteFontWeight,s.anchor=et.noteAlign,s.textMargin=et.noteMargin,s.valign="center";const o=O(i,s),c=Math.round(o.map((t=>(t._groups||t)[0][0].getBBox().height)).reduce(((t,e)=>t+e)));n.attr("height",c+2*et.noteMargin),e.height+=c+2*et.noteMargin,at.bumpVerticalPos(c+2*et.noteMargin),e.stopy=e.starty+c+2*et.noteMargin,e.stopx=e.startx+a.width,at.insert(e.startx,e.starty,e.stopx,e.stopy),at.models.addNote(e)}(p,a);break;case s.db.LINETYPE.ACTIVE_START:at.newActivation(t,p,u);break;case s.db.LINETYPE.ACTIVE_END:!function(t,e){const a=at.endActivation(t);a.starty+18>e&&(a.starty=e-6,e+=12),H(p,a,e,et,lt(t.from.actor).length),at.insert(a.startx,e-10,a.stopx,e)}(t,at.getVerticalPos());break;case s.db.LINETYPE.LOOP_START:dt(E,t,et.boxMargin,et.boxMargin+et.boxTextMargin,(t=>at.newLoop(t)));break;case s.db.LINETYPE.LOOP_END:e=at.endLoop(),U(p,e,"loop",et),at.bumpVerticalPos(e.stopy-at.getVerticalPos()),at.models.addLoop(e);break;case s.db.LINETYPE.RECT_START:dt(E,t,et.boxMargin,et.boxMargin,(t=>at.newLoop(void 0,t.message)));break;case s.db.LINETYPE.RECT_END:e=at.endLoop(),j(p,e),at.models.addLoop(e),at.bumpVerticalPos(e.stopy-at.getVerticalPos());break;case s.db.LINETYPE.OPT_START:dt(E,t,et.boxMargin,et.boxMargin+et.boxTextMargin,(t=>at.newLoop(t)));break;case s.db.LINETYPE.OPT_END:e=at.endLoop(),U(p,e,"opt",et),at.bumpVerticalPos(e.stopy-at.getVerticalPos()),at.models.addLoop(e);break;case s.db.LINETYPE.ALT_START:dt(E,t,et.boxMargin,et.boxMargin+et.boxTextMargin,(t=>at.newLoop(t)));break;case s.db.LINETYPE.ALT_ELSE:dt(E,t,et.boxMargin+et.boxTextMargin,et.boxMargin,(t=>at.addSectionToLoop(t)));break;case s.db.LINETYPE.ALT_END:e=at.endLoop(),U(p,e,"alt",et),at.bumpVerticalPos(e.stopy-at.getVerticalPos()),at.models.addLoop(e);break;case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:dt(E,t,et.boxMargin,et.boxMargin+et.boxTextMargin,(t=>at.newLoop(t))),at.saveVerticalPos();break;case s.db.LINETYPE.PAR_AND:dt(E,t,et.boxMargin+et.boxTextMargin,et.boxMargin,(t=>at.addSectionToLoop(t)));break;case s.db.LINETYPE.PAR_END:e=at.endLoop(),U(p,e,"par",et),at.bumpVerticalPos(e.stopy-at.getVerticalPos()),at.models.addLoop(e);break;case s.db.LINETYPE.AUTONUMBER:_=t.message.start||_,v=t.message.step||v,t.message.visible?s.db.enableSequenceNumbers():s.db.disableSequenceNumbers();break;case s.db.LINETYPE.CRITICAL_START:dt(E,t,et.boxMargin,et.boxMargin+et.boxTextMargin,(t=>at.newLoop(t)));break;case s.db.LINETYPE.CRITICAL_OPTION:dt(E,t,et.boxMargin+et.boxTextMargin,et.boxMargin,(t=>at.addSectionToLoop(t)));break;case s.db.LINETYPE.CRITICAL_END:e=at.endLoop(),U(p,e,"critical",et),at.bumpVerticalPos(e.stopy-at.getVerticalPos()),at.models.addLoop(e);break;case s.db.LINETYPE.BREAK_START:dt(E,t,et.boxMargin,et.boxMargin+et.boxTextMargin,(t=>at.newLoop(t)));break;case s.db.LINETYPE.BREAK_END:e=at.endLoop(),U(p,e,"break",et),at.bumpVerticalPos(e.stopy-at.getVerticalPos()),at.models.addLoop(e);break;default:try{at.resetVerticalPos(),n=t.msgModel,n.starty=at.getVerticalPos(),n.sequenceIndex=_,n.sequenceVisible=s.db.showSequenceNumbers();const e=function(t,e){at.bumpVerticalPos(10);const{startx:a,stopx:n,message:r}=e,s=i.e.splitBreaks(r).length,o=i.u.calculateTextDimensions(r,it(et)),c=o.height/s;let l;e.height+=c,at.bumpVerticalPos(c);let h=o.height-10;const d=o.width;if(a===n){l=at.getVerticalPos()+h,et.rightAngles||(h+=et.boxMargin,l=at.getVerticalPos()+h),h+=30;const t=i.e.getMax(d/2,et.width/2);at.insert(a-t,at.getVerticalPos()-10+h,n+t,at.getVerticalPos()+30+h)}else h+=et.boxMargin,l=at.getVerticalPos()+h,at.insert(a,l-10,n,l);return at.bumpVerticalPos(h),e.height+=h,e.stopy=e.starty+e.height,at.insert(e.fromBounds,e.starty,e.toBounds,e.stopy),l}(0,n);w.push({messageModel:n,lineStartY:e}),at.models.addMessage(n)}catch(t){i.l.error("error while drawing message",t)}}[s.db.LINETYPE.SOLID_OPEN,s.db.LINETYPE.DOTTED_OPEN,s.db.LINETYPE.SOLID,s.db.LINETYPE.DOTTED,s.db.LINETYPE.SOLID_CROSS,s.db.LINETYPE.DOTTED_CROSS,s.db.LINETYPE.SOLID_POINT,s.db.LINETYPE.DOTTED_POINT].includes(t.type)&&(_+=v)})),w.forEach((t=>function(t,e,a,n){const{startx:s,stopx:o,starty:c,message:l,type:h,sequenceIndex:d,sequenceVisible:p}=e,u=i.u.calculateTextDimensions(l,it(et)),g=(0,r.e)();g.x=s,g.y=c+10,g.width=o-s,g.class="messageText",g.dy="1em",g.text=l,g.fontFamily=et.messageFontFamily,g.fontSize=et.messageFontSize,g.fontWeight=et.messageFontWeight,g.anchor=et.messageAlign,g.valign="center",g.textMargin=et.wrapPadding,g.tspan=!1,O(t,g);const x=u.width;let y;s===o?y=et.rightAngles?t.append("path").attr("d",`M  ${s},${a} H ${s+i.e.getMax(et.width/2,x/2)} V ${a+25} H ${s}`):t.append("path").attr("d","M "+s+","+a+" C "+(s+60)+","+(a-10)+" "+(s+60)+","+(a+30)+" "+s+","+(a+20)):(y=t.append("line"),y.attr("x1",s),y.attr("y1",a),y.attr("x2",o),y.attr("y2",a)),h===n.db.LINETYPE.DOTTED||h===n.db.LINETYPE.DOTTED_CROSS||h===n.db.LINETYPE.DOTTED_POINT||h===n.db.LINETYPE.DOTTED_OPEN?(y.style("stroke-dasharray","3, 3"),y.attr("class","messageLine1")):y.attr("class","messageLine0");let m="";et.arrowMarkerAbsolute&&(m=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,m=m.replace(/\(/g,"\\("),m=m.replace(/\)/g,"\\)")),y.attr("stroke-width",2),y.attr("stroke","none"),y.style("fill","none"),h!==n.db.LINETYPE.SOLID&&h!==n.db.LINETYPE.DOTTED||y.attr("marker-end","url("+m+"#arrowhead)"),h!==n.db.LINETYPE.SOLID_POINT&&h!==n.db.LINETYPE.DOTTED_POINT||y.attr("marker-end","url("+m+"#filled-head)"),h!==n.db.LINETYPE.SOLID_CROSS&&h!==n.db.LINETYPE.DOTTED_CROSS||y.attr("marker-end","url("+m+"#crosshead)"),(p||et.showSequenceNumbers)&&(y.attr("marker-start","url("+m+"#sequencenumber)"),t.append("text").attr("x",s).attr("y",a+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(d))}(p,t.messageModel,t.lineStartY,s))),et.mirrorActors&&(at.bumpVerticalPos(2*et.boxMargin),st(p,u,x,at.getVerticalPos(),et,y,!0),at.bumpVerticalPos(et.boxMargin),Y(p,at.getVerticalPos())),at.models.boxes.forEach((function(t){t.height=at.getVerticalPos()-t.y,at.insert(t.x,t.y,t.x+t.width,t.height),t.startx=t.x,t.starty=t.y,t.stopx=t.startx+t.width,t.stopy=t.starty+t.height,t.stroke="rgb(0,0,0, 0.5)",W(p,t,et)})),f&&at.bumpVerticalPos(et.boxMargin);const P=ot(p,u,x,d),{bounds:k}=at.getBounds();i.l.debug("For line height fix Querying: #"+e+" .actor-line"),(0,n.td_)("#"+e+" .actor-line").attr("y2",k.stopy);let L=k.stopy-k.starty;L<P.maxHeight&&(L=P.maxHeight);let I=L+2*et.diagramMarginY;et.mirrorActors&&(I=I-et.boxMargin+et.bottomMarginAdj);let N=k.stopx-k.startx;N<P.maxWidth&&(N=P.maxWidth);const M=N+2*et.diagramMarginX;m&&p.append("text").text(m).attr("x",(k.stopx-k.startx)/2-2*et.diagramMarginX).attr("y",-25),(0,i.i)(p,I,M,et.useMaxWidth);const A=m?40:0;p.attr("viewBox",k.startx-et.diagramMarginX+" -"+(et.diagramMarginY+A)+" "+M+" "+(I+A)),i.l.debug("models:",at.models)}},styles:t=>`.actor {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n  }\n\n  text.actor > tspan {\n    fill: ${t.actorTextColor};\n    stroke: none;\n  }\n\n  .actor-line {\n    stroke: ${t.actorLineColor};\n  }\n\n  .messageLine0 {\n    stroke-width: 1.5;\n    stroke-dasharray: none;\n    stroke: ${t.signalColor};\n  }\n\n  .messageLine1 {\n    stroke-width: 1.5;\n    stroke-dasharray: 2, 2;\n    stroke: ${t.signalColor};\n  }\n\n  #arrowhead path {\n    fill: ${t.signalColor};\n    stroke: ${t.signalColor};\n  }\n\n  .sequenceNumber {\n    fill: ${t.sequenceNumberColor};\n  }\n\n  #sequencenumber {\n    fill: ${t.signalColor};\n  }\n\n  #crosshead path {\n    fill: ${t.signalColor};\n    stroke: ${t.signalColor};\n  }\n\n  .messageText {\n    fill: ${t.signalTextColor};\n    stroke: none;\n  }\n\n  .labelBox {\n    stroke: ${t.labelBoxBorderColor};\n    fill: ${t.labelBoxBkgColor};\n  }\n\n  .labelText, .labelText > tspan {\n    fill: ${t.labelTextColor};\n    stroke: none;\n  }\n\n  .loopText, .loopText > tspan {\n    fill: ${t.loopTextColor};\n    stroke: none;\n  }\n\n  .loopLine {\n    stroke-width: 2px;\n    stroke-dasharray: 2, 2;\n    stroke: ${t.labelBoxBorderColor};\n    fill: ${t.labelBoxBorderColor};\n  }\n\n  .note {\n    //stroke: #decc93;\n    stroke: ${t.noteBorderColor};\n    fill: ${t.noteBkgColor};\n  }\n\n  .noteText, .noteText > tspan {\n    fill: ${t.noteTextColor};\n    stroke: none;\n  }\n\n  .activation0 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .activation1 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .activation2 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .actorPopupMenu {\n    position: absolute;\n  }\n\n  .actorPopupMenuPanel {\n    position: absolute;\n    fill: ${t.actorBkg};\n    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));\n}\n  .actor-man line {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n  }\n  .actor-man circle, line {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n    stroke-width: 2px;\n  }\n`}},8770:function(t,e,a){a.d(e,{a:function(){return r},b:function(){return c},c:function(){return o},d:function(){return n},e:function(){return h},f:function(){return s},g:function(){return l}});var i=a(7967);const n=function(t,e){const a=t.append("rect");if(a.attr("x",e.x),a.attr("y",e.y),a.attr("fill",e.fill),a.attr("stroke",e.stroke),a.attr("width",e.width),a.attr("height",e.height),a.attr("rx",e.rx),a.attr("ry",e.ry),"undefined"!==e.attrs&&null!==e.attrs)for(let t in e.attrs)a.attr(t,e.attrs[t]);return"undefined"!==e.class&&a.attr("class",e.class),a},r=function(t,e){n(t,{x:e.startx,y:e.starty,width:e.stopx-e.startx,height:e.stopy-e.starty,fill:e.fill,stroke:e.stroke,class:"rect"}).lower()},s=function(t,e){const a=e.text.replace(/<br\s*\/?>/gi," "),i=t.append("text");i.attr("x",e.x),i.attr("y",e.y),i.attr("class","legend"),i.style("text-anchor",e.anchor),void 0!==e.class&&i.attr("class",e.class);const n=i.append("tspan");return n.attr("x",e.x+2*e.textMargin),n.text(a),i},o=function(t,e,a,n){const r=t.append("image");r.attr("x",e),r.attr("y",a);var s=(0,i.N)(n);r.attr("xlink:href",s)},c=function(t,e,a,n){const r=t.append("use");r.attr("x",e),r.attr("y",a);const s=(0,i.N)(n);r.attr("xlink:href","#"+s)},l=function(){return{x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0}},h=function(){return{x:0,y:0,width:100,height:100,fill:void 0,anchor:void 0,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}}}}]);