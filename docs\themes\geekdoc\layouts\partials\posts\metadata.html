<span class="flex align-center no-wrap">
  <svg class="gdoc-icon gdoc_date"><use xlink:href="#gdoc_date"></use></svg>
  <span class="gdoc-post__tag">
    <time datetime="{{ .Lastmod.Format "2006-01-02T15:04:05Z07:00" | safeHTML }}">
      {{ if .Lastmod.After (.Date.AddDate 0 0 1) }}
        {{ i18n "posts_update_prefix" }}
      {{ end }}
      {{ .Lastmod.Format "Jan 2, 2006" }}
    </time>
  </span>
</span>

<span class="flex align-center no-wrap">
  <svg class="gdoc-icon gdoc_timer"><use xlink:href="#gdoc_timer"></use></svg>
  <span class="gdoc-post__tag">{{ i18n "posts_read_time" .ReadingTime }}</span>
</span>

{{ $tc := 0 }}
{{ with .Params.tags }}
  {{ range sort . }}
    {{ $name := . }}
    {{ with $.Site.GetPage (printf "/tags/%s" $name | urlize) }}
      {{ if eq $tc 0 }}
        <span class="flex align-center no-wrap">
          <svg class="gdoc-icon gdoc_bookmark"><use xlink:href="#gdoc_bookmark"></use></svg>
          {{ template "post-tag" dict "name" $name "page" . }}
        </span>
      {{ else }}
        <span class="flex align-center">
          {{ template "post-tag" dict "name" $name "page" . }}
        </span>
      {{ end }}
    {{ end }}
    {{ $tc = (add $tc 1) }}
  {{ end }}
{{ end }}

{{ define "post-tag" }}
  <span class="gdoc-post__tag gdoc-button gdoc-button--regular">
    <a
      class="gdoc-button__link"
      href="{{ .page.RelPermalink }}"
      title="{{ i18n "posts_tagged_with" .name }}"
    >
      {{ .name }}
    </a>
  </span>
{{ end }}
